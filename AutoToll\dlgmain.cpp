#include "dlgmain.h"

#include <curl/curl.h>
#include <qsettings.h>

#include <QApplication>
#include <QDateTime>
#include <QDesktopWidget>
#include <QDialog>
#include <QMessageBox>

#include "abstractdev.h"
#include "abstractstate.h"
#include "batchmgr.h"
#include "cemvehlist.h"
#include "cfarecalcunit.h"
#include "coldsysproc.h"
#include "common/msgmanager.h"
#include "confirmdlg.h"
#include "cstationdoortable.h"
#include "csysinfo.h"
#include "ctransinfoshared.h"
#include "devicefactory.h"
#include "devices/abstractdev.h"
#include "devices/cardreader.h"
#include "etclanectrl.h"
#include "etcprompt.h"
#include "fdandsoundevent.h"
#include "formshowimage.h"
#include "gantrybyplate.h"
#include "globalui.h"
#include "globalutils.h"
#include "httpmsgmanager.h"
#include "httpreqparams.h"
#include "ilogmsg.h"
#include "laneinfo.h"
#include "lanesoundplay.h"
#include "lanetype.h"
#include "messagebox.h"
#include "messagedialog.h"
#include "parafileold.h"
#include "qpainter.h"
#include "stdlog.h"
#include "tollgantrymgr.h"
//#include "ui_dlgmain.h"
#include "ccurl.h"
#include "remoteclient.h"
#include "update.h"
#include "vehplatefunc.h"
#include "vehweightinfo.h"
#include "remotemsgmgr.h"
#include "common.pb.h"
#include "remotecontrolmgr.h"
#include "papercardmgr.h"
#include "vehtypelibmgr.h"
#include "vipumgr.h"
#include "clearfilemgr.h"
#include "abstractprinter.h"
#include "vdmlibSingleton.h"
#include "etcdelay.h"
#include "mobilepaydual.h"  // 包含双链路支付设备头文件
#include "rsuinitworker.h"  // 天线异步初始化工作类
#include "etclanectrl.h"    // ETC车道控制类
#include "transinfo.h"      // 交易信息类

#define _LARGEFILE64_SOURCE
#define _FILE_OFFSET_BITS 64
#define __USE_FILE_OFFSET64

//一秒时长
#define INTERVAL_ONE_SEC 1000
// 2分之一
#define INTERVAL_HALF_SEC 500
// 4分之一
#define INTERVAL_QUARTER_SEC 250
//缺省告警信息显示时间
#define DEFAULT_ALERT_TIME 5000

#define LANESERVER_LISTEN_PORT 40005

#define TWSM_NOTIFY_MSGID 10238

static QString g_psamNo;
QDlgMain::QDlgMain(QWidget *parent) : QDialog(parent), m_LaneServer(false, this)
{
    //创建子窗口
    //设备状态窗口
    m_pFrmDevStatus = new FormDevStatus(this);
    //最上面程序名称，收费站信息窗口
    m_pFrmLaneInfo = new FormLaneInfo(this);
    //视频采集卡窗口
    m_pFrmVideoCard = new FormVideoCard(this);
    //参数状态窗口
    m_pFrmParamVer = new FormParamVersion(this);
    //车辆队列示意窗口
    m_pFrmVehQueue = new FormVehQueue(this);
    //抓拍图像窗口
    m_pFrmVehImage = new FormVehImage(this);
    // ETC交易显示区
    m_pFrmEtcInfo[0] = new FormETCTradeInfo(0, this);
    m_pFrmEtcInfo[1] = new FormETCTradeInfo(1, this);
    //日志窗口
    m_pFrmLog = new FormLog(this);
    //交易流水窗口
    m_pFrmHistory = new FormHistory(this);
    //费显窗口
    m_pFrmFareDisp = new FormFareDisplayer(this);
    //提示窗口
    m_pFrmPromptMsg = new FormPromptMsg(this);
    //下班状态窗口
    m_pFrmUnlogin = new FormUnLogin(this);
    //计重窗口
    m_pFrmWeight = new FormWeight(this);
    //车辆信息输入窗口
    m_pFrmVehInfo = new FormVehInfo(this);
    //票号窗口
    m_pFrmExtraInfo = new FormExtraInfo(this);
    //支付窗口
    m_pFrmVehMoney = new FormVehMoney(this);
    //二维码窗口
    m_pFrmQRCode = new FormQRCode(this);
    //正在加载窗口
    m_pFrmLoading = new FormLoading(this);

    motorCadeFm = new CMotorCadeFm(this);

    m_pPsamDlg = new CPSAMDlg();

    m_pMsgLbl = new Cmessagelabel(this);
    m_FrmsMgr = new QStackedWidget(this);
    m_nExitAppTimerId = 0;
    ser = new SerNum();
    m_TimerOutUseTimeParam = 0;
    m_nLoadParamNum = 0;
    m_TimerReqParams = 0;
    m_TimerAskTimeID = 0;
    m_TimerReqParamsLf = 0;
    m_TimerPrintPaper = 0;
    m_nLastPaperStatus = 0;
    connect(&m_tLoginTimer, SIGNAL(timeout()), this, SLOT(OnChangeToUnloginState()));
    m_pFlashQueTimer = new QTimer(this);
    connect(m_pFlashQueTimer, SIGNAL(timeout()), this, SLOT(OnFlashQue()));
    m_nKeyBTimes = 0;
    // connect(&m_CheckShiftTimer, SIGNAL(timeout()), this, SLOT(OnKeyBClear()));

    ntpClient =
            new NTPClient(Ptr_Info->getNtpHost(), Ptr_Info->getNtpPort(), Ptr_Info->getNtpTime());
    connect(ntpClient, SIGNAL(OnSyncTimeFinish(QDateTime)), this, SLOT(OnSetTime(QDateTime)));

    connect(&m_PSAMSignTimer, SIGNAL(timeout()), this, SLOT(OnPSAMSign()));

    m_UpdateFronVehQueTimer.setSingleShot(true);
    m_UpdateFronVehQueTimer.setInterval(200);
    connect(&m_UpdateFronVehQueTimer, SIGNAL(timeout()), this, SLOT(RefreshTradeVehQueue()));
    connect(this, SIGNAL(NotifyClearVehFrmEvent(bool)), this, SLOT(OnClearVehFrmEvent(bool)));

    m_sVLPRHourBatchNo = QDateTime::currentDateTime().toString("yyyyMMddHH");
    m_VehicleDataCount = 0;
    m_VehiclePicCount = 0;

    connect(&m_VLPRSumTimer, SIGNAL(timeout()), this, SLOT(SendVlprSumWaste()));

    //远端控制关闭按钮
    m_pBtnCloseRemoteControl = new QPushButton("远控模式", this);
    connect(m_pBtnCloseRemoteControl, SIGNAL(clicked(bool)), this,
            SLOT(OnBtnCloseRemoteControlClicked(bool)));
    setObjectName(QString("DlgMain"));
}

QDlgMain::~QDlgMain()
{
    m_HeadTimer.stop();
    
    // 清理可能还在运行的天线初始化线程
    QMap<int, QThread*>::iterator it;
    for (it = m_mapInitThreads.begin(); it != m_mapInitThreads.end(); ++it) {
        QThread *pThread = it.value();
        if (pThread && pThread->isRunning()) {
            // 检查是否是当前线程，避免线程等待自己
            if (pThread != QThread::currentThread()) {
                pThread->quit();
                if (!pThread->wait(1000)) { // 等待最多1秒
                    DebugLog(QString("天线初始化线程 %1 未能正常退出，强制终止").arg(it.key()));
                    pThread->terminate();
                    if (pThread != QThread::currentThread()) {
                        pThread->wait(500); // 最多再等待500毫秒
                    }
                }
            } else {
                // 如果是当前线程，只发送退出信号，不等待
                DebugLog("检测到天线初始化线程试图等待自己，跳过等待");
                pThread->quit();
            }
        }
    }
    m_mapInitThreads.clear();
    
    if (ntpClient) delete ntpClient;
    ntpClient = NULL;
}

qint32 QDlgMain::Init()
{
    //根据配置参数，确定是否需要窗口一直在前
    if (Ptr_Info->IsLockWindowsOnTop()) {
        SetWindowStaysOnTopHint(true);
    }
    setWindowFlags(Qt::FramelessWindowHint);

    InitUIControls(Ptr_Info->GetLaneType());

    m_pStayOutTimeDlg = new CMessageBox(QString("车辆等待超时!"), CMessageBox::Style_OkCancel);
    m_pStayOutTimeDlg->SetHelpMsg("【确定】落杆,【取消】退出");
    m_bStayOut = false;
    InstallChildWndKeyEventFilter();

    return RETURN_OK;
}

void QDlgMain::StartExitAppTimer() { m_nExitAppTimerId = startTimer(500); }

void QDlgMain::StartShiftCheckTimer()
{
    if (m_CheckShiftTimer.isActive()) {
        m_CheckShiftTimer.stop();
    }
    DebugLog(QString("启动班次检测定时器"));
    m_CheckShiftTimer.start(10 * 1000);
}

void QDlgMain::StopShiftCheckTimer()
{
    DebugLog(QString("关闭班次检测定时器"));
    if (m_CheckShiftTimer.isActive()) {
        m_CheckShiftTimer.stop();
    }
}

void QDlgMain::ReqParamFun() {}

void QDlgMain::ShowLog(const QString sInfo)
{
    if (sInfo.isEmpty()) {
        return;
    }

    {
        QMutexLocker locker(&m_LogMutex);
        m_sLogList.push_back(sInfo);
    }
    DebugLog(sInfo);
    return;
}

void QDlgMain::OnFlashQue()
{
    m_pFlashQueTimer->stop();
    RefreshPassVehQueue();
    //  RefreshNewVehPic();
}

//更新车辆交易队列
void QDlgMain::OnUpdateTradeVehQueue() { m_UpdateFronVehQueTimer.start(); }

void QDlgMain::StopAllTimer()
{
    if (m_TimerOutUseTimeParam > 0) {
        killTimer(m_TimerOutUseTimeParam);
        m_TimerOutUseTimeParam = 0;
    }

    if (m_TimerReqParams > 0) {
        killTimer(m_TimerReqParams);
        m_TimerReqParams = 0;
    }
    if (m_TimerReqParamsLf > 0) {
        killTimer(m_TimerReqParamsLf);
        m_TimerReqParamsLf = 0;
    }

    if (m_TimerAskTimeID > 0) {
        killTimer(m_TimerAskTimeID);
        m_TimerAskTimeID = 0;
    }
    if (m_TimeHeadID > 0) {
        killTimer(m_TimeHeadID);
        m_TimeHeadID = 0;
    }
    if (m_TimerPrintPaper > 0) {
        killTimer(m_TimerPrintPaper);
        m_TimerPrintPaper = 0;
    }
}

void QDlgMain::StartAskTimeTimer()
{
    static qint32 nAdjustTimeInterval = 30 * 60 * 1000;  // 30分钟校时一次
    CSysParaDicTable *pTable = (CSysParaDicTable *)CParamFileMgr::GetParamFile(cfSysParaDic);
    qint32 nInterval = nAdjustTimeInterval;
    if (pTable) {
        const CSysParamInfo *pParamInfo = pTable->GetSysParam();
        nInterval = pParamInfo->m_nAdjustTimeVal * 60 * 1000;
    }

    if (nInterval != nAdjustTimeInterval) {
        if (m_TimerAskTimeID > 0) killTimer(m_TimerAskTimeID);
        nAdjustTimeInterval = nInterval;
    }

    m_TimerAskTimeID = startTimer(nAdjustTimeInterval);
}

bool QDlgMain::LoadMsgFromFile(CJKMsgInfo &jkMsgInfo)
{
    QString sNoticeFilePath("./msg/save.msg");
    if (!QFile::exists(sNoticeFilePath)) {
        return false;
    }

    QFile hNoticeFile(sNoticeFilePath);
    try {
        bool bRet = hNoticeFile.open(QIODevice::ReadOnly);
        if (!bRet) {
            DebugLog(QObject::tr("公告文件[%1]打开失败！").arg(sNoticeFilePath));
            return false;
        }

        qint64 dwTotalLength = hNoticeFile.size();
        if (-1 == dwTotalLength) {
            hNoticeFile.close();
            DebugLog(QObject::tr("LoadMsgFromFile 失败,[%1]大小[%2]错误！")
                     .arg(sNoticeFilePath)
                     .arg(dwTotalLength));
            return false;
        }
        DebugLog(QString("参数文件总长度:%1").arg(dwTotalLength));

        QByteArray totalFile = hNoticeFile.readAll();
        memcpy(&jkMsgInfo, totalFile.constData(), sizeof(CJKMsgInfo));
        hNoticeFile.close();

        return true;
    } catch (...) {
    }

    return false;
}

void QDlgMain::timerEvent(QTimerEvent *e)
{
    if (m_TimerShowLog == e->timerId()) {
        killTimer(m_TimerShowLog);
        RefreshLog();
        m_TimerShowLog = startTimer(10);
        return;
    }
    if (m_nExitAppTimerId == e->timerId()) {
        killTimer(m_nExitAppTimerId);
        m_nExitAppTimerId = 0;
        this->close();
        return;
    } else if (m_TimerAskTimeID == e->timerId()) {  //
        DebugLog(QString("开始定时校时任务..."));
        // CReqParams *pReqParams=CReqParams::GetReqParams();
        // pReqParams->StartAskTime();
    } else if (m_TimeHeadID == e->timerId()) {
        CReqParams *pReqParams = CReqParams::GetReqParams();
        pReqParams->StartHeartMsg();
    } else if (m_TimerReqParams == e->timerId()) {  // 参数请求检查-10分钟检查一次
        DebugLog(QString("开始参数定时下载任务..."));
        CReqParamTask task;
        task.bDownLoadCardBlack = 1;  // Ptr_Info->IsEntryLane()?1:0;
        task.taskTime = QDateTime::currentDateTime().toTime_t();
        CHttpReqParams::GetHttpReqParams()->StartDownLoadParam(task);  // ResumeThread();

    } else if (m_TimerOutUseTimeParam == e->timerId()) {  // 参数是否到期检查
        // DebugLog("刷新显示版本信息");
        RefreshParamVerInfo();  //刷新版本信息
    } else if (m_TimerPrintPaper == e->timerId()) {
        CheckPrintPaperStatus();  //检查打印纸状态
    } else {
        //防有未关闭定时器
        DebugLog(QString("未知定时器Id:%1").arg(e->timerId()));
        killTimer(e->timerId());
    }
}

void QDlgMain::customEvent(QEvent *event)
{
    if (event->type() == MtcKeyPressedEvent::getEventType()) {
        MtcKeyPressedEvent *mtcKeyEvent = static_cast<MtcKeyPressedEvent *>(event);
        mtcKeyPressed(mtcKeyEvent);
    } else if (event->type() == CCardOpenEvent::getEventType()) {
        //
        CCardOpenEvent *pCardOpenEvent = static_cast<CCardOpenEvent *>(event);
        bool bContinueReadCard = true;
        if (!OnOpenCardEvent(pCardOpenEvent->GetReaderId(), pCardOpenEvent->GetCardType(),
                             bContinueReadCard)) {
            if (bContinueReadCard) CCardReader::ContinueCardDetection();
        }
    } else if (event->type() == CScanQrCodeEvent::getEventType()) {
        CScanQrCodeEvent *pEvent = static_cast<CScanQrCodeEvent *>(event);
        OnQrCodeEvent(pEvent->GetIndex(), pEvent->GetQrCode());
    }
}

void QDlgMain::RefreshLog()
{
    QStringList tmpLogList;
    {
        QMutexLocker locker(&m_LogMutex);
        tmpLogList = m_sLogList;
        m_sLogList.clear();
    }
    for (int i = 0; i < tmpLogList.count(); ++i) {
        m_pFrmLog->AddLog(tmpLogList.at(i));
    }
}

void QDlgMain::mtcKeyPressed(MtcKeyPressedEvent *mtcKeyEvent)
{
    if (!mtcKeyEvent) return;

    if (Ptr_Info->IsLockWindowsOnTop()) {
        if (mtcKeyEvent->isLetterKey() && KeyB == mtcKeyEvent->key() &&
                'B' == mtcKeyEvent->ascii()) {
            m_KeyBClearTimer.stop();
            ++m_nKeyBTimes;
            if (3 == m_nKeyBTimes) {
                m_nKeyBTimes = 0;
                //重启窗口置顶定时器
                m_activeTimer.stop();
                m_activeTimer.start(60 * 1000);
                m_KeyBClearTimer.start(500);
                return;
            }
            m_KeyBClearTimer.start(500);

        } else {
            m_nKeyBTimes = 0;
        }
    } else {
    }

    if (mtcKeyEvent->isFuncKey()) {
        int nKey = mtcKeyEvent->func();
        if (KeyEsc == nKey) {
            CBaseOpWidget *pTopDlg =
                    (CBaseOpWidget *)CAbstractDev::GetTopWidget();  // CCardReader::GetTopWidget();
            if (pTopDlg) {
                DebugLog(
                            QString("main dlg process key, has top dlg:%1").arg(pTopDlg->objectName()));
                pTopDlg->mtcKeyPressed(mtcKeyEvent);
                return;
            }
        }
    }

    int nRlt = 0;
    CAbstractState *pState = CAbstractState::GetCurState();
    if (pState) {
        nRlt = pState->mtcKeyPressed(mtcKeyEvent);
        QString sLog = QString("LaneState:%1 processKey: %2,Rlt:%3")
                .arg(pState->objectName())
                .arg(mtcKeyEvent->keyName())
                .arg(nRlt);
        TraceLog(sLog);
    }

    if (mtcKeyEvent->processed()) {
        MtcKeyProcessResultRecorder::instance()->doKeyProcessed(mtcKeyEvent->GetKeyProperty(),
                                                                mtcKeyEvent->getKeyType());
    }
    return;
}

bool QDlgMain::OnOpenCardEvent(int nReaderId, int nCardType, bool &bContinueReadCard)
{
    CAbstractState *pState = CAbstractState::GetCurState();
    QString sError;
    bool bAlarm = false;
    if (pState) {
        bool bRlt =
                pState->OnOpenCardEvent(nReaderId, nCardType, bAlarm, sError, bContinueReadCard);
        if (!bRlt) {
            if (sError.length() > 0) pState->ShowErrorMessage(sError);
        }
        CCardReader *pCardReader = CCardReader::GetCardReader(nReaderId);
        if (pCardReader) pCardReader->CloseCard();
        return bRlt;
    }
    return true;
}

void QDlgMain::OnQrCodeEvent(int nPos, const QString &sQrCode)
{
    CAbstractState *pState = CAbstractState::GetCurState();
    if (pState) {
        pState->OnQrCodeEvent(nPos, sQrCode);
    }
}

bool QDlgMain::eventFilter(QObject *obj, QEvent *event)
{
    if (event->type() == MtcKeyPressedEvent::getEventType()) {
        MtcKeyPressedEvent *mtcKeyEvent = static_cast<MtcKeyPressedEvent *>(event);
        mtcKeyPressed(mtcKeyEvent);
        return true;
    } else {
        return QObject::eventFilter(obj, event);
    }
}

//安装子窗口键盘消息过滤器
void QDlgMain::InstallChildWndKeyEventFilter()
{
    QList<QWidget *> widgets = this->findChildren<QWidget *>();

    for (int i = 0; i < widgets.size(); i++) {
        widgets[i]->installEventFilter(this);
    }
}

void QDlgMain::closeEvent(QCloseEvent *event)
{
    if (Update::needUpdate) {
        AppDestroy();
        event->accept();
        return;
    }
    /*
    CAbstractState *pState = CAbstractState::GetCurState();
    if(pState && pState->GetCurStateId() != CAbstractState::StateID_UnLogin) {
        event->ignore();
        return ;
    }*/
    AppDestroy();
    event->accept();
}

bool QDlgMain::winEvent(MSG *message, long *result)
{
    if (message->message == TWSM_NOTIFY_MSGID) {
        quint8 option = message->wParam & 0xFF;
        quint8 result = (message->wParam >> 8) & 0xFF;
        CSpEventDev::OnResultCallBack(option, result, CDeviceFactory::GetSpEventDev());
        return true;
    }
    else // 如果双链路支付对象存在，让它处理消息
    {
        // 通过设备工厂获取移动支付设备
        MobilePayBase *pMobilePay = CDeviceFactory::GetMobilePayTw();
        if (pMobilePay != NULL)
        {
            // 尝试转换为MobilePayDual类型（当nType=3时，工厂返回的是MobilePayDual实例）
            MobilePayDual *pDualPay = dynamic_cast<MobilePayDual*>(pMobilePay);
            if (pDualPay != NULL)
            {
                // 调用双链路支付设备的Windows消息处理方法
                bool handled = pDualPay->processNativeMessage(message, result);
                if (handled)
                {
                    return true; // 消息已被处理
                }
            }
        }
    }
    return false;
}

void QDlgMain::InitUI(int nLaneType)
{
    this->setGeometry(g_GlobalUI.m_RectMainWnd);
    //子窗口初始化UI
    //设备状态图标窗口
    m_pFrmDevStatus->InitUI();
    //最上面程序名称，收费站信息窗口
    m_pFrmLaneInfo->InitUI();
    //视频采集卡窗口
    m_pFrmVideoCard->InitUI();
    //参数状态窗口
    m_pFrmParamVer->InitUI();
    //车辆队列示意窗口
    m_pFrmVehQueue->InitUI();
    //抓拍图像窗口
    m_pFrmVehImage->InitUI();
    // ETC交易显示区
    m_pFrmEtcInfo[0]->InitUI();
    m_pFrmEtcInfo[1]->InitUI();
    //日志窗口
    m_pFrmLog->InitUI();
    //交易流水窗口
    m_pFrmHistory->InitUI(nLaneType);
    //费显窗口
    m_pFrmFareDisp->InitUI();
    //提示窗口
    m_pFrmPromptMsg->InitUI();
    //计重窗口
    m_pFrmWeight->InitUI();
    //下班窗口
    m_pFrmUnlogin->InitUI();
    //票号窗口
    m_pFrmExtraInfo->InitUI();
    //二维码窗口
    m_pFrmQRCode->InitUI();
    //显示金额窗口
    m_pFrmVehMoney->Init();
    // psam
    m_pPsamDlg->InitUI();
    //正在加载窗口
    m_pFrmLoading->InitUI();

    motorCadeFm->Init();
    motorCadeFm->hide();
    m_FrmsMgr->setGeometry(g_GlobalUI.m_RectWndOperate);
    //远端控制按钮
    m_pBtnCloseRemoteControl->setGeometry(g_GlobalUI.lnif_CloseRemoteButtonRect);
    m_pBtnCloseRemoteControl->hide();
}

/**
 * @brief QDlgMain::InitUIControls
 * @param nLaneType
 * 各状态机的界面也在初始化
 */
void QDlgMain::InitUIControls(qint32 nLaneType)
{
    InitUI(nLaneType);
    m_FrmsMgr->addWidget(m_pFrmLoading);
    m_FrmsMgr->addWidget(m_pFrmVehInfo);
    m_FrmsMgr->addWidget(motorCadeFm);
    m_FrmsMgr->addWidget(m_pFrmUnlogin);
    m_FrmsMgr->addWidget(m_pFrmVehMoney);

    //初始化最上端的车道信息显示区
    InitLaneInfoArea(nLaneType);
    // TraceLog("初始化最上端的车道信息显示区--完成");
    m_pFrmVehInfo->Init(nLaneType);

    m_pFrmPromptMsg->ShowPromptMsg("程序启动，正在加载...");
}

void QDlgMain::InitLaneInfoArea(qint32 nLaneType)
{
    Q_UNUSED(nLaneType)

    //程序版本显示
    QString sVersion = Ptr_Info->GetAppVer();
    m_pFrmLaneInfo->SetAppVersion(sVersion);
    ShowOperatorId("000000");
    ShowShiftInfo(QDate::currentDate(), "");

    m_pFrmLaneInfo->SetSelfServiceMode(Ptr_Info->bCardMgrEnabled());
}

//显示上班时间
void QDlgMain::OnShowOnDutyTime(const QDateTime &dtOnDuty)
{
    m_pFrmLaneInfo->SetShiftTime(dtOnDuty);
    if (m_pFrmExtraInfo) {
        m_pFrmExtraInfo->ShowShiftTime(dtOnDuty);
    }
}

/**
 * @brief QDlgMain::OnRoutine10SecTime: 常规定时任务，10秒一次
 */
void QDlgMain::OnRoutine10SecTime()
{
    m_Routine10SecTimer.stop();
    //显示未上传数据
    int entry_exit_waste_num = 0;
    QString entry_exit_waste_time;
    int vip_waste_num = 0;
    QString vip_waste_time;
    int gantry_waste_num = 0;
    QString gantry_waste_time;
    Ptr_ETCCtrl->GetDataMgr()->getUnSendinfo(entry_exit_waste_num, entry_exit_waste_time,
                                             vip_waste_num, vip_waste_time, gantry_waste_num,
                                             gantry_waste_time);
    m_pFrmParamVer->ShowWasteCount(entry_exit_waste_num, entry_exit_waste_time, vip_waste_num,
                                   vip_waste_time, gantry_waste_num, gantry_waste_time);
    //显示内存占用
    quint32 dwTotalPhy = 0, dwAvailPhy = 0, dwTotalVir = 0, dwAvailVir = 0;
    int memorySize = GetMemorySize(dwTotalPhy, dwAvailPhy, dwTotalVir, dwAvailVir);
    DebugLog(QString("系统总内存%1K,剩余内存:%2K,总虚拟内存:%3,剩余虚拟内存:%4")
             .arg(dwTotalPhy)
             .arg(dwAvailPhy)
             .arg(dwTotalVir)
             .arg(dwAvailVir));
    m_pFrmParamVer->ShowMemorySize(QString("%1%").arg(memorySize));
    //显示磁盘空间
    m_pFrmParamVer->ShowDiskSpace(CSYSInfo::m_sDiskSpace);
    m_Routine10SecTimer.start(10000);
}

/**
 * @brief QDlgMain::OnFareDisplayerShowText 更新费显显示的文字
 * @param sText
 * @param nRow
 */
void QDlgMain::OnFareDisplayerShowText(QString sText, int nRow)
{
    m_pFrmFareDisp->ShowText(sText, nRow);
}

void QDlgMain::ShowWasteInfo(CWasteInfo *wasteInfo) { m_pFrmHistory->AddWaste(wasteInfo); }

/**
 * @brief QDlgMain::InitAllLaneState 初始化车道状态机
 */
void QDlgMain::InitAllLaneState()
{
    CAbstractState::SetGUIMgr(m_FrmsMgr);
    CETCLaneCtrl *pLaneCtrl = CETCLaneCtrl::GetETCLaneCtrl();
    pLaneCtrl->InitAllState();
    if (Ptr_Info->IsEntryLane()) {
        pLaneCtrl->SetLaneStateUI(CAbstractState::StateID_VehInputEn, m_pFrmVehInfo);
    } else
        pLaneCtrl->SetLaneStateUI(CAbstractState::StateID_VehInputEx, m_pFrmVehInfo);
    pLaneCtrl->SetLaneStateUI(CAbstractState::StateID_UnLogin, m_pFrmUnlogin);
    pLaneCtrl->SetLaneStateUI(CAbstractState::StateID_WaitPass, m_pFrmVehInfo);
    pLaneCtrl->SetLaneStateUI(CAbstractState::StateID_Motor, motorCadeFm);
    pLaneCtrl->SetLaneStateUI(CAbstractState::StateID_VehMoney, m_pFrmVehMoney);
}
//显示站名称
void QDlgMain::ShowStationName(const QString &stationname)
{
    m_pFrmLaneInfo->SetStationName(stationname);
}

//显示车道名称
void QDlgMain::ShowLaneName(int nLaneType, const QString &lanename)
{
    m_pFrmLaneInfo->SetLaneName(nLaneType, lanename);
}
//显示收费员编码
void QDlgMain::ShowOperatorId(const QString &operId)
{
    m_pFrmLaneInfo->SetOperatorName(operId);
    if (m_pFrmExtraInfo) {
        m_pFrmExtraInfo->ShowOperatorName(operId);
    }
}

//显示车道状态
void QDlgMain::ShowLaneState(qint32 nLaneStatus)
{
    QString sState = "";
    if (lsUnlogin == nLaneStatus) {
        sState = QString("下班");
    } else if (lsNormalWorking == nLaneStatus) {
        sState = QString("上班");
    } else if (lsSleep == nLaneStatus) {
        sState = QString("休眠");
    }
    m_pFrmLaneInfo->SetLaneState(sState);
    if (m_pFrmExtraInfo) {
        m_pFrmExtraInfo->ShowLaneState(sState);
    }
}

//显示班次信息
void QDlgMain::ShowShiftInfo(const QDate workdate, const QString &shift)
{
    QString shiftName = QString("%1(%2)").arg(shift).arg(workdate.toString("MM/dd"));
    m_pFrmLaneInfo->SetShiftName(shift);
    if (m_pFrmExtraInfo) {
        m_pFrmExtraInfo->ShowShiftName(shift);
    }
}

void QDlgMain::SetMotor(bool bMotor) {}

void QDlgMain::CheckAndDownLoadParam()
{
    if (m_pParamInfoDlg) m_pParamInfoDlg->CheckAndDownLoadCfgFile(false);
}

//刷新通过区车辆队列（过了光栅的待通过）
void QDlgMain::RefreshPassVehQueue()
{
    m_pFrmVehQueue->ClearAllPass();
    QList<CTransInfo> vehList;
    Ptr_ETCCtrl->GetVehQueue(vehList);
    for (int i = 0; i < vehList.count(); ++i) {
        CTransInfo transInfo = vehList.at(i);
        QString sPlate = GB2312toUnicode(transInfo.VehInfo.szVehPlate);
        if (transInfo.bTransOk()) {
            m_pFrmVehQueue->AddPassItem(sPlate, false);
        } else {
            m_pFrmVehQueue->AddPassItem(sPlate, true);
        }
    }
}

//刷新交易区车辆队列信息（交易完成，未过光栅）
void QDlgMain::RefreshTradeVehQueue()
{
    QMutexLocker locker(&m_waitVehListTex);
    QList<CTransInfo> list;
    Ptr_ETCCtrl->GetFronVehQueue(list);
    m_pFrmVehQueue->ClearAllTrade();
    int i = 0;
    QList<CTransInfo>::iterator it = list.begin();
    for (; it != list.end(); it++, i++) {
        QString sPlate = GB2312toUnicode(it->VehInfo.szVehPlate);
        m_pFrmVehQueue->AddTradeItem(sPlate);
    }
    return;
}

//状态机通知更新对应界面
void QDlgMain::OnETCDisplayMsgEvent(int nIndex, int nType, QList<QVariant> ParamList)
{
    if (nIndex < 0 || nIndex > 1) {
        ErrorLog(QString("主窗口处理显示事件时，天线索引值%1非法").arg(nIndex));
        return;
    }

    switch (nType) {
    case ETCDisplayType_Clear: {
        m_pFrmEtcInfo[nIndex]->ClearAllVehInfo();
        break;
    }
    case ETCDisplayType_ShowMoney: {
        if (ParamList.size() < 1) {
            return;
        }
        m_pFrmEtcInfo[nIndex]->ShowMoney(ParamList.at(0).toInt());
        break;
    }
    case ETCDisplayType_ShowOBU: {
        if (ParamList.size() < 1) {
            return;
        }
        m_pFrmEtcInfo[nIndex]->ShowCardID(ParamList.at(0).toString());
        break;
    }
    case ETCDisplayType_ShowPromptInfo: {
        if (ParamList.size() < 1) {
            return;
        }
        m_pFrmEtcInfo[nIndex]->ShowPrompt(ParamList.at(0).toString());
        break;
    }
    case ETCDisplayType_SetVehInfo: {
        if (ParamList.size() < 1) {
            return;
        }
        CVehInfo vehInfo = ParamList.at(0).value<CVehInfo>();
        m_pFrmEtcInfo[nIndex]->ShowVehInfo(vehInfo);
        break;
    }
    case ETCDisplayType_ShowEntryInfo: {
        if (ParamList.size() < 4) {
            return;
        }
        QDateTime enTime = ParamList.at(0).toDateTime();
        QString sEnStation = ParamList.at(1).toString();
        QString sEnVLP = ParamList.at(2).toString();
        QString sEnStationHex = ParamList.at(3).toString();
        m_pFrmEtcInfo[nIndex]->ShowEntryInfo(enTime, sEnStation, sEnVLP, sEnStationHex);
        break;
    }
    case ETCDisplayType_ShowCardInfo: {
        if (ParamList.size() < 3) {
            return;
        }
        int nCardType = ParamList.at(0).toInt();
        QString sCardId = ParamList.at(1).toString();
        quint32 dwBalance = ParamList.at(2).toUInt();
        m_pFrmEtcInfo[nIndex]->ShowCardInfo(nCardType, sCardId, dwBalance);
        break;
    }
    case ETCDisplayType_ShowTransInfo: {
        if (ParamList.size() < 1) {
            return;
        }
        CWasteInfo wasteInfo = ParamList.at(0).value<CWasteInfo>();
        m_pFrmEtcInfo[nIndex]->ShowTransInfo(wasteInfo);
        break;
    }
    }
}

//当收到显示提示信息的信号时
void QDlgMain::OnShowPromptMsgEvent(const QString &promptMsg, bool bWarnFlag, bool bAutoClear)
{
    if (m_pFrmPromptMsg) {
        m_pFrmPromptMsg->ShowPromptMsg(promptMsg, bWarnFlag, bAutoClear);
    }
}

void QDlgMain::NotifyWeightDataChange()
{
    //获取当前已经输入的车型
    CVehInfo vehInfo;
    CAbstractState::GetCurVehInfo(vehInfo, false);
    bool bSpecial = isZhuangXiangTruck(vehInfo.VehClass);

    m_pFrmWeight->ClearWeightInfoShow();
    QList<CVehAxisInfo> lstAxisInfo;
    VehWeightInfo::GetVehWeightInfo()->GetAllVeh(&lstAxisInfo);
    for (int i = 0; i < lstAxisInfo.size(); i++) {
        const CVehAxisInfo &axis = lstAxisInfo.at(i);
        if (i == 0) {
            //首辆车信息
            m_pFrmWeight->ShowVehWeight(&axis, bSpecial);
        }
        VehWeightItem item;
        item.axleCount = axis.GetConfirmedSingleAxisNum();
        item.totalWeight = axis.GetConfirmedTotalRawWeight();
        m_pFrmWeight->AddVehWeightInfo(item);
    }
    Ptr_RemoteCtrl->RefreshWeightInfo(vehInfo.VehClass, lstAxisInfo, bSpecial);
}

/**
 * @brief QDlgMain::OnVcrResultData 接收到车型识别结果时
 * @param dwCarID
 * @param nColor
 * @param sPlate
 */
void QDlgMain::OnVcrResultData(unsigned long dwCarID, int nColor, QString sPlate, qint32 vehClass,
                               int nAxisCount)
{
    /*
    CVehClass nVehClass = CDeviceFactory::GetVCRDev()->GetVehClass(nColor, sPlate);
    DebugLog(QString("显示自动识别车型%1").arg((int)nVehClass));
    m_pFrmVehInfo->ShowAutoVehClass(nVehClass);*/

    VcrResult vcrResult;
    bool bHaveResult = CDeviceFactory::GetVCRDev()->GetLastVcrResult(vcrResult);
    if (bHaveResult) {
        if (Ptr_Info->bRemoteControl())
            RemoteMsgMgr::GetSingleInst()->SendLaneReportVCRReq(&vcrResult);
        this->ShowLog(QString("车型结果:%1%2,%3(%4)")
                      .arg(GetVehPlateColorName(vcrResult.nColor))
                      .arg(vcrResult.sPlate)
                      .arg(GetVehClassName((CVehClass)vehClass))
                      .arg(nAxisCount));
    } else {
        this->ShowLog(QString("车型结果,CarId:%1,%2,%3,%4(%5)")
                      .arg(dwCarID)
                      .arg(GetVehPlateColorName(nColor))
                      .arg(sPlate)
                      .arg(GetVehClassName((CVehClass)vehClass))
                      .arg(nAxisCount));
    }
}

void QDlgMain::OnSetTime(QDateTime dateTime)
{
    QDateTime curTime = QDateTime::currentDateTime();
    if (dateTime.isValid()) {
        // dateTime = dateTime.addSecs(-20);
        QString scurBatch = CBatchMgr::GetBatchMgr()->GetCurBatch();
        QString sTime = QString("校时返回服务器时间:%1,当前批次:%2")
                .arg(dateTime.toString("yyyy-MM-dd hh:mm:ss"))
                .arg(scurBatch);
        QString sBatch = dateTime.toString("yyyyMMddhh");
        DebugLog(sTime);

        QString str = curTime.toString("hhmmsszzz");
        StdInfoLog(LogKey::OneKey_Other, LogKey::Other_TimeSynchro, str, QString(), sTime);
        int nSeconds = dateTime.secsTo(curTime);
        if (qAbs(nSeconds) > 1 * 60 * 60) {
            QString sServerTime = dateTime.toString("yyyy-MM-dd hh:mm:ss");
            QString sError =
                    QString("校时返回服务器时间%1与当前时间差异超过1小时").arg(sServerTime);
            StdInfoLog(LogKey::OneKey_Other, LogKey::Other_TimeSynchro, str, QString(), sError);
            ShowLog(sError);

            CDeviceFactory::StartAlarm(DevIndex_Second, 2000);
            QString sMsg = QString("校时服务返回时间:%1,与本地时间差异超过1小时,请检查")
                    .arg(dateTime.toString("yyyy-MM-dd hh:mm:ss"));
            RemoteMsgMgr::GetSingleInst()->SendLaneReportEventReq(1, CSpEventMgr::SpEvent_Other,
                                                                  sMsg);
            // QString sHelp = QString("如本地时间异常,请调整本地时间");

            // CMessageBox::Information_Help(QString("校时异常"), sMsg, sHelp,
            // CMessageBox::Style_Ok,
            //                            NULL, Ptr_Info->bRemoteControl());

            // CDeviceFactory::StopAlarm(DevIndex_Second);
            return;
        }

        if (sBatch < scurBatch) {
            QString sError = QString("校时后服务器返回批次号小于当前批次,不予处理");
            StdInfoLog(LogKey::OneKey_Other, LogKey::Other_TimeSynchro, str, QString(), sError);
            ShowLog(sError);
            return;
        }

        if (!SetLocalSystemTime(dateTime)) {
            DebugLog(QString("设置本地时间失败"));
        }
    }
    return;
}

void QDlgMain::OnPSAMSign()
{
    PsameSign sign;
    // PSAM卡号 PSAM MF下0015文件1~10字节。
    sign.psamNo = g_psamNo;
    //终端机编号 PSAM MF下0016文件1~6字节。
    quint8 bTerminateId[6] = {0};
    if (Ptr_Info->bHaveFrontDev())
        CDeviceFactory::GetRsuDev(0)->GetTerminateId(bTerminateId);
    else
        CDeviceFactory::GetRsuDev(1)->GetTerminateId(bTerminateId);

    sign.terminalNo = Raw2HexStr(bTerminateId, 6);
    //省域编码
    sign.provinceCode = QString("3601");
    //路段编码 省内现有编码
    sign.roadCode = QString("%1").arg(Ptr_Info->GetBL_SubCenter());  // GetOrgCode().sBL_Road;
    //路段名称
    sign.roadName = QString("路段名称");
    //收费站编码 省内现有编码
    sign.stationCode = QString::number(Ptr_Info->GetStationID());
    //收费站名称
    sign.stationName = Ptr_Info->GetStationName();
    //收费站类型  “01”-匝道收费站， “02”-主线收费站（非省界） “03”-省界主线收费站 “04”-普通标识点
    // c"05"-省界标识点
    sign.stationType = QString("01");
    if (Ptr_Info->IsEntryLane()) {
        //车道号 标识点默认值，上行-"00"，下行-"10"
        sign.laneNo = QString("00");
        //车道类型 "00"-保留  "01"-封闭MTC入口 "02"-封闭MTC出口 "03"-封闭ETC入口 "04"-封闭ETC出口
        //"05"-MTC开放式 "06"-ETC开放式 "07～0F"自定义 "10"-标识点 "11～FF"保留给未来使用
        sign.laneType = QString("03");
    } else {
        //车道号 标识点默认值，上行-"00"，下行-"10"
        sign.laneNo = QString("10");
        //车道类型 "00"-保留  "01"-封闭MTC入口 "02"-封闭MTC出口 "03"-封闭ETC入口 "04"-封闭ETC出口
        //"05"-MTC开放式 "06"-ETC开放式 "07～0F"自定义 "10"-标识点 "11～FF"保留给未来使用
        sign.laneType = QString("04");
    }
    //终端时间 例："2018-07-01 08:45:00"
    sign.terminalTime = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss");
    PsameSignResultResp resp;
    Ptr_ETCCtrl->GetSecureVerify()->SendPsamSign(sign, resp);
}

void QDlgMain::OnPsamAuthEvent(int nIndex, int nEvent, int nDevType)
{
    m_pPsamDlg->ProcessPsamAuthEvent_New(nIndex, nEvent, nDevType);
}

void QDlgMain::SetLastShiftWasteInfo(const QString &shiftName, int entry_exit_waste_num,
                                     int gantry_num)
{
    //显示前一班次合计数(问题：是显示承载门架+车道的，还是只显示车道的。
    m_pFrmHistory->SetPrevShiftEtcCount(entry_exit_waste_num);
}

void QDlgMain::DisplayLastShiftSumInfo()
{
    CShiftSumInfo ShiftSumInfo;
    Ptr_ETCCtrl->GetShiftMgr()->GetLastShiftSumInfo(ShiftSumInfo);
    if (ShiftSumInfo.wShiftID > 0) {
        QString sLDate = QString(ShiftSumInfo.szLDate).left(8).right(4);
        QString sShiftName =
                QString("%1(%2)").arg(QString::fromLocal8Bit(ShiftSumInfo.szShiftName)).arg(sLDate);
        int nWasteNum = ShiftSumInfo.SumRecord.vCnt;
        int nGantryNum = ShiftSumInfo.SumRecord.etcCardCnt;
        SetLastShiftWasteInfo(sShiftName, nWasteNum, nGantryNum);
    }
}

void QDlgMain::DisplayCurShiftSumInfo()
{
    CShiftSumInfo shiftSumInfo, BigShiftSumInfo;

    Ptr_ETCCtrl->GetShiftMgr()->GetShiftSumInfo(shiftSumInfo);
    Ptr_ETCCtrl->GetShiftMgr()->GetBigShiftSumInfo(BigShiftSumInfo);

    int nTotalETC = 0;
    if (1 == Ptr_ETCCtrl->GetShiftMgr()->m_statusFlag) {
        nTotalETC = BigShiftSumInfo.SumRecord.etcCardCnt;
    } else
        nTotalETC = shiftSumInfo.SumRecord.etcCardCnt + BigShiftSumInfo.SumRecord.etcCardCnt;
    m_pFrmHistory->SetShiftEtcCount(nTotalETC);
}

void QDlgMain::OnExitTimer()
{
    //
    m_ExitTimer.stop();
    switch (m_nExitCode) {
    case 1:  // 退出程序
    {
        try {
            DebugLog("执行退出程序操作！");
            AppDestroy();
        } catch (...) {
            ErrorLog("退出程序:资源释放出异常!");
        }
        qApp->exit(RETCODE_QUIT);
        return;
    }
    case 2:  // 重启程序
    {
        try {
            //                Ptr_Ctrl->SaveAppStarMsg(false);
            //                GetMainDlg()->AppDestroy();
            DebugLog("执行程序重启操作！");
        } catch (...) {
            ErrorLog("重启程序:资源释放出异常!");
        }
        // SleeperThread::msleep(3000);
        qApp->exit(RETCODE_RESTART);
        return;
    }
    case 3:  // 关闭计算机
    {
        try {
            AppDestroy();
            DebugLog("执行关闭计算机操作！");
        } catch (...) {
            ErrorLog("关闭计算机:资源释放出异常!");
        }
        ShutDownWindows();
        return;
    }
    case 4:  //重启计算机
    {
        try {
            GetMainDlg()->AppDestroy();
            DebugLog("执行重启计算机操作！");
        } catch (...) {
            ErrorLog("重启计算机:资源释放出异常!");
        }
        RestartWindows();
        return;
    }
    default:
        break;
    }
    return;
}

void QDlgMain::OnReomteClientStateChanged(int nState)
{
    QString sState;
    switch (nState) {
    case RemoteClient::Conn_Disconnected:
        sState = QString("远程控制连接断开");
        break;
    case RemoteClient::Conn_Connected: {
        sState = QString("远程控制建立连接");
        break;
    }
    case RemoteClient::Conn_Login: {
        sState = QString("远程控制系统登录成功");
        break;
    }
    default:
        break;
    }
    if (sState.length() > 0) {
        ShowLog(sState);
    }
}

//坐席远控关闭按钮
void QDlgMain::OnBtnCloseRemoteControlClicked(bool checked)
{
    ShowLog(QString("结束远程控制"));
    RemoteServer::GetSingleInst()->NotifyCloseRemoteControl();
    m_pBtnCloseRemoteControl->hide();
    //结束视频对讲
    if (Ptr_Info->bHaveCardMgr()) {
        CDeviceFactory::GetAutoExTollScreen()->StopVideoChat();
        if (!CDeviceFactory::GetVideoCard()->bVideoDisplay()) {
            ShowLog(QString("打开车道视频"));
            CDeviceFactory::GetVideoCard()->Display();
        }
    }
}
void QDlgMain::OnRemoteConnected(const QString &sIP)
{
    ShowLog(QString("新建坐席连接:%1").arg(sIP));
}

void QDlgMain::OnRemoteDisConnected(const QString &sIP)
{
    ShowLog(QString("断开坐席连接:%1").arg(sIP));
}

void QDlgMain::OnRemoteControlCmd(int nCmd, QString sMsg)
{
    ShowLog(sMsg);
    if (nCmd == C2L_REQ_CONTROL_MODE) {
        m_pBtnCloseRemoteControl->show();
    } else if (nCmd == C2L_REQ_CONTROL_MODE_FINISH) {
        m_pBtnCloseRemoteControl->hide();
    }
}

bool QDlgMain::LoadVPRResult()
{
    CVPRResult vprResult;
    QString sFileName = QString("./pic/truck1.jpg");
    QByteArray bigImg;
    QFile file(sFileName);
    bool bRlt = file.open(QIODevice::ReadOnly);
    if (!bRlt) return false;
    bigImg = file.readAll();
    file.close();

    vprResult.ClearResult();
    vprResult.AllocateMem();
    vprResult.nColor = 0;

    QString sVLP = QString("赣A12345");
    QByteArray bVLP = sVLP.toLocal8Bit();

    memcpy(vprResult.szVehPlate, bVLP.data(), bVLP.size());
    vprResult.nBigImgSize = bigImg.size();
    // memcpy(vprResult.lpBigImgBuf, bigImg.data(), bigImg.size());
    vprResult.sBigName = sFileName;

    RemoteMsgMgr::GetSingleInst()->SendLaneReporVPRReq(&vprResult, 1);
    return true;
}

bool QDlgMain::LoadVCRResult()
{
    VcrResult vcrResult;
    vcrResult.dwCarID = 1;
    vcrResult.sPlate = QString("赣A12345");
    vcrResult.dtPlate = QDateTime::currentDateTime();
    vcrResult.nAxleCnt = 2;
    vcrResult.nColor = 0;
    vcrResult.vehclass = VC_Car1;
    HvImage hvImg;

    QString sFileName = QString("./pic/truck1.jpg");

    hvImg.sImgFile = sFileName;
    vcrResult.bigImage[VehImg_Front] = hvImg;
    vcrResult.bigImage[VehImg_Body] = hvImg;
    vcrResult.bigImage[VehImg_Tail] = hvImg;
    CDeviceFactory::GetVCRDev()->AddVcrResult(vcrResult);

    VcrResult tmpResult;
    bool bRlt = CDeviceFactory::GetVCRDev()->GetVcrResult_Fuzzy(0, QString("鲁B12345"), tmpResult);
    if (bRlt) {
        DebugLog(QString("%1").arg(tmpResult.sPlate));
    }

    // RemoteMsgMgr::GetSingleInst()->SendLaneReportVCRReq(&vcrResult);
    return true;
}

void QDlgMain::OnInputVehInfoByRemote(qint32 vehClass, int nPlateColor, QString sVehPlate,
                                      qint32 vehType, QString axleType)
{
    CAbstractState *pCurState = CAbstractState::GetCurState();
    if (pCurState) {
        pCurState->OnInputVehInfoByRemote(vehClass, nPlateColor, sVehPlate, vehType, axleType);
    }
    return;
}

void QDlgMain::OnSelectPayTypeByRemote(int keyValue)
{
    CAbstractState *pCurState = CAbstractState::GetCurState();
    if (pCurState) {
        pCurState->OnSelectPayTypeByRemote((CKeyValue)keyValue);
    }
}

void QDlgMain::OnBackInputStateByRemote()
{
    CAbstractState *pCurState = CAbstractState::GetCurState();
    if (pCurState) {
        pCurState->OnBackInputStateByRemote();
    }
}

void QDlgMain::OnRemoteSimulateDownBar()
{
    ShowLog("坐席操作模拟落杆");
    //平板远控模拟落杆
    CAbstractState *pCurState = CAbstractState::GetCurState();
    if (pCurState) {
        pCurState->OnRemoteSimulateDownBar();
    }
}

void QDlgMain::ShowPromptMsg(const QString &promptMsg, bool bWarnFlag, bool bAutoClear)
{
    emit NotifyShowPromptEvent(promptMsg, bWarnFlag, bAutoClear);
}

void QDlgMain::SendVlprSumWaste()
{
    QString sBatchNo = QDateTime::currentDateTime().toString("yyyyMMddHH");
    if (sBatchNo == m_sVLPRHourBatchNo) return;
    QString sVLPRHourBatchNo = m_sVLPRHourBatchNo;
    quint32 VehicleDataCount = m_VehicleDataCount;
    quint32 VehiclePicCount = m_VehiclePicCount;
    {
        QMutexLocker locker(&m_VLPRNumMutex);
        m_sVLPRHourBatchNo = sBatchNo;
        m_VehicleDataCount = 0;
        m_VehiclePicCount = 0;
    }
    Ptr_ETCCtrl->SaveVLPRSumWaste(sVLPRHourBatchNo, VehicleDataCount, VehiclePicCount);
}

void QDlgMain::ShowGantryName(const QString &sGantryName)
{
    m_pFrmLaneInfo->SetGantryName(sGantryName);
}

//显示费率版本
void QDlgMain::ShowFeeVersion(QString sVersion) { m_pFrmParamVer->ShowFeeVersion(sVersion); }

void QDlgMain::ShowMinFeeVersion(QString sVersion, QString sNewVersion)
{
    m_pFrmParamVer->ShowMinFeeVersion(sVersion, sNewVersion);
}

//显示黑名单版本
void QDlgMain::ShowBlackListVersion(QString sVersion)
{
    m_pFrmParamVer->ShowCBListVersion(sVersion);
}

//显示告警信息
void QDlgMain::ShowWarning(QString sWarning) { m_pFrmPromptMsg->ShowPromptMsg(sWarning, true); }

bool QDlgMain::InitDevice(QString &sError)
{
    CDeviceFactory::InitDev(sError);

    // 注册车牌识别-----注册车牌识别消息,
    for (int i = 0; i < MAX_VPR_NUM; ++i) {  // Ptr_Info->bHaveVPR()) {
        CVPRDev *pVpr = CDeviceFactory::GetVPRDev(i);
        if (!pVpr) continue;
        if (!pVpr->StartVPRDev()) {
            sError = QString("启动车牌识别%1失败").arg(i);
            ErrorLog(sError);
        }
        // pVpr->RegisterMessage((HWND)this->winId(), WM_VPR_DETECT);
        // DebugLog("RegisterMessage完成");
        pVpr->SetResultfCallBack(CVPRDev::GGetRegResult, CVPRDev::GGetDevStatus);
        DebugLog("SetResultfCallBack完成");
    }
    DebugLog(QString("开始初始化IO"));
    quint32 dwInput = 0;

    SleeperThread::msleep(1000);
    if (!CDeviceFactory::GetIOCard()->StartIoCard(dwInput)) {
        sError = QString("IO卡初始化失败");
        ErrorLog(sError);
        sError = QString("IO卡设备初始化失败,程序退出");
#ifdef Q_WS_WIN
        return false;
#endif
    }

    CDeviceFactory::InitCardMachine(sError, this, SLOT(OnCardMachineEvent(int, int, int)),
                                    SLOT(OnHelpBtnEvent(int)));

    if (!CDeviceFactory::InitCardReader(sError, this, SLOT(OnPsamAuthEvent(int, int, int)))) {
        ShowLog(sError);
    }
    // CCardReader *pCardReader = CDeviceFactory::GetCardReader(0);
    // CCardReader::SetBaseDetector(this);
    CAbstractDev::SetBaseDetector(this);
    // 初始化RSU设备，添加异常保护
    for (int i = 0; i < 2; ++i) {
        CRsuDev *pRsuDev = CDeviceFactory::GetRsuDev(i);
        if (!pRsuDev) {
            DebugLog(QString("RSU设备%1未创建").arg(i));
            continue;
        }
        
        try {
            // pRsuDev->setLaneMode(Ptr_Info->GetLaneType());
            if (!pRsuDev->StartRsuDev(Ptr_Info->GetLaneType())) {
                ErrorLog(QString("RSU设备%1启动失败").arg(i));
            } else {
                DebugLog(QString("RSU设备%1启动成功").arg(i));
            }
        } catch (...) {
            ErrorLog(QString("RSU设备%1启动时发生异常").arg(i));
        }
    }
    return true;
}

void QDlgMain::SetWindowStaysOnTopHint(bool bWindowStaysOnTopHint)
{
    bool bHidden = this->isHidden();
    if (bHidden) this->hide();
    disconnect(&m_activeTimer, SIGNAL(timeout()), this, SLOT(SetWindowActivity()));
    m_activeTimer.stop();
    Qt::WindowFlags type;
    type = Qt::FramelessWindowHint;
#ifndef QT_DEBUG
    if (bWindowStaysOnTopHint) {
        type = type | Qt::WindowStaysOnTopHint;
        connect(&m_activeTimer, SIGNAL(timeout()), this, SLOT(SetWindowActivity()));
        m_activeTimer.start(3000);
    } else {
        type = type | Qt::Widget;
    }
#endif
    this->setWindowFlags(type);
    if (!bHidden) {
        this->show();
    }
}

/**
 * @brief QDlgMain::CheckPrintPaperStatus
 * 检查打印纸状态,
 */
void QDlgMain::CheckPrintPaperStatus()
{
    //此函数为入口增加打印纸券时增加的一个功能，暂时只处理入口纸券，不处理出口
    if (Ptr_Info->IsEntryLane() && Ptr_Info->bGrantPaperCard()) {
        PaperCardNo_Param param;
        bool bRlt = CPaperCardMgr::GetSingleInst()->GetPaperCardNO(VC_Car1, param);
#ifdef QT_DEBUG
        // bRlt = true;
#endif
        if (bRlt) {
            //入口，并且开始发纸券了
            CPrinterDevice *pDev = CDeviceFactory::GetPrinterDev();
            if (pDev) {
                int nPaperStatus = pDev->GetPaperStatus();
                if (nPaperStatus == CAbstractPrinter::PAPER_STATUS_OK) {
                    //打印纸正常
                    if (nPaperStatus != m_nLastPaperStatus) {
                        //状态变化，清除提示
                        if (m_pFrmPromptMsg) {
                            m_pFrmPromptMsg->ClearExtraMsg();
                        }
                    }
                } else {
                    //打印纸不正常，判断是否需要报警
                    QString PaperStatusStr;
                    if (nPaperStatus == CAbstractPrinter::PAPER_STATUS_LESS) {
                        PaperStatusStr = "打印纸将尽";
                    } else {
                        PaperStatusStr = "打印缺纸";
                    }
                    if (nPaperStatus != m_nLastPaperStatus) {
                        //状态变化，需要报一次警, 并改变显示
                        CDeviceFactory::FareDisplay_ShowHelpMsg(DevIndex_Second, PaperStatusStr);
                        CDeviceFactory::StartAlarm(DevIndex_Second, 5000);
                        ShowLog(PaperStatusStr);
                        // TODO 上报远程监控
                        if (m_pFrmPromptMsg) {
                            m_pFrmPromptMsg->ShowExtraMsg(PaperStatusStr);
                        }
                    }
                }
                m_nLastPaperStatus = nPaperStatus;

                //                if (CAbstractDev::DEV_STATUS_OK != nDevStatus) {
                //                    bool bPaperStatus =
                //                    if (!bPaperStatus) {
                //                        CDeviceFactory::FareDisplay_ShowHelpMsg(DevIndex_Second,
                //                                                                QString("打印机缺纸"));
                //                        CDeviceFactory::StartAlarm(DevIndex_Second, 5000);
                //                        ShowLog(QString("打印机缺纸"));
                //                    }
                //                } else {
                //                    bool bPaperStatus = pDev->GetPaperStatus();
                //                    if (bPaperStatus) {
                //                        CFareDisplayer_GB *pFareDev =
                //                            CDeviceFactory::GetETCFareDisPlayer(DevIndex_Second);
                //                        if (pFareDev) pFareDev->ClearAll();
                //                    }
                //                }
            }
        }
    }
}

void QDlgMain::SetWindowActivity()
{
    m_activeTimer.stop();
    DoKeepFocus();
    m_activeTimer.start(3000);
}

/**
 * @brief QDlgMain::AppStart 程序启动, 在主窗口显示后，由main函数中调用
 * @param sError
 * @return
 */
bool QDlgMain::AppStart(QString &sError)
{
    m_lAppStartTime = QDateTime::currentMSecsSinceEpoch();

    if (m_pFrmParamVer) m_pFrmParamVer->ShowHolidayVersion(Ptr_Info->GetSubVer());
    // CURLcode return_code;
    // return_code = curl_global_init(CURL_GLOBAL_ALL);
    QString sErrorCode;
    if (!Curl::Global_Init(sErrorCode)) {
        ErrorLog("初始化libcurl失败");
        sError = "网络模块初始化失败";
        return false;
    }
    qint32 nStationid = Ptr_Info->GetStationID();
    qint32 station = nStationid % 100;
    if (station < 0) {
        sError = QString("站编码配置错误");
        return false;
    }
    qint32 road = nStationid / 100 % 1000;
    if (road > 255 || road < 0) {
        sError = QString("站编码路段配置错误");
        return false;
    }
    QString sIP = QString("239.%1.%2.%3")
            .arg(QString::number(road))
            .arg(QString::number(station))
            .arg(QString::number(Ptr_Info->IsEntryLane() ? 0 : 1));
#ifndef QT_DEBUG
    if (CTransInfoShared::GetTransInfoShared()->LoadLib(sIP)) {
        if (!CTransInfoShared::GetTransInfoShared()->Init()) {
            sError = QString("交易信息共享动态库初始化失败");
            //#ifndef Q_OS_UNIX
            // return false;
            ShowLog(sError);
            //#endif
        }
    } else {
        sError = QString("交易信息共享动态库预加载失败");
        //#ifndef Q_OS_UNIX
        // return false;
        ShowLog(sError);
        //#endif
    }
#endif

    if (!CBatchMgr::GetBatchMgr()->InitBatchInfo(Ptr_Info->GetStationID(), Ptr_Info->GetLaneId())) {
        sError = QString("批次流水号初始化失败");
        return false;
    }
    /*
        if (Ptr_Info->IsExitLane() && Ptr_Info->bEticket()) {
            if (!CDeviceFactory::InitInvoiceQrCode()) {
                sError = QString("票据二维码动态库初始化失败");
                return false;
            }
            DebugLog(QString("采用电子票"));
        } else {
            DebugLog("非电子票");
        }*/

    //初始化状态机
    InitAllLaneState();
    //设置参数文件管理模块-----启动程序时加载1444151296
    CParamFileMgr::InitParamFiles(Ptr_Info->GetLaneType(), Ptr_Info->bOpenLane());
    QString sParaUrl, sBinFileAuth, sUpLoadUrl, sUpLoadUrlBak;

    Ptr_Info->GetParaUrl(sParaUrl, sBinFileAuth);
    Ptr_Info->GetDataUrl(sUpLoadUrl, sBinFileAuth, sUpLoadUrlBak);

    CHttpReqParams::GetHttpReqParams()->SetURL(sParaUrl, sUpLoadUrl, sBinFileAuth);
    CHttpReqParams::GetHttpReqParams()->SetOrgInfo(Ptr_Info->GetStationID(), Ptr_Info->GetLaneId(),
                                                   Ptr_Info->GetGBLaneId());

    //初始化国密认证
    URLParams urlParam;
    Ptr_Info->GetURLParams(urlParam);
    Ptr_ETCCtrl->GetSecureVerify()->setUrl(urlParam);

    m_pParamInfoDlg = new CParamInfoDlg();
    m_pParamInfoDlg->InitUI();
    CParamFileMgr::ConnParamLoadSignals(this,
                                        SLOT(OnLoadNewParamFileMsg(qint32, CCfgFileHead, bool)));

    DebugLog("开始加载参数");
    if (!m_pParamInfoDlg->CheckAndDownLoadCfgFile()) {
        //参数下载失败，退出程序
        // return false;
    }

    m_TimerShowLog = startTimer(30);
    if (!GetLocalStationinfo(sError)) {
        ErrorLog(sError);
        return false;
    }

    if (!InitOrgBasicInfo()) {
        sError = "基础信息映射表加载失败";
        return false;
    }

    CHttpMsgManager::SetGBLaneId(Ptr_Info->GetGBLaneId());
    //初始化门架信息
    if (Ptr_Info->IsExitLane() && Ptr_Info->CheckReplaceWriteCard()) {
        QString sGantryId = Ptr_Info->GetReplaceGantryID();
        COrgBasicInfoTable *pTable =
                (COrgBasicInfoTable *)CParamFileMgr::GetParamFile(cfOrgBasicInfo);
        COrgBasicInfo orgBasicInfo;
        bool bRlt = pTable->QryOrgBasicInfo(sGantryId, orgBasicInfo);
        if (!bRlt) {
            sError = QString("代写门架代码[%1]错误").arg(sGantryId);
            DebugLog(sError);
            return false;
        }
        DebugLog(QString("出口代写门架:%1[%2]").arg(sGantryId).arg(orgBasicInfo.sHex));
        Ptr_Info->SetReplaceGantryHex(orgBasicInfo.sHex);
    }

    InitStationDoorInfo();
    RefreshParamVerInfo();
    InitOpenStationInfo_New();
    Ptr_ETCCtrl->GetSecureVerify()->SetOrgInfo(Ptr_Info->GetGBStationId(),
                                               Ptr_Info->GetStationName(), Ptr_Info->GetStationID(),
                                               Ptr_Info->GetRoadName(), Ptr_Info->GetGBLaneId(),
                                               Ptr_Info->GetLaneId(), Ptr_Info->GetLaneType());

    if (Ptr_Info->bOpenLane()) {
        //开放式车道
        bool bRlt =
                GantryByPlate::GetSTGantryByPlate()->LoadLib(Ptr_Info->getOpenLaneIPPort(), sError);
        if (!bRlt) {
            DebugLog(sError);
            sError = QString("开放式门架动态库加载失败");
            return false;
        }

        if (!GantryByPlate::GetSTGantryByPlate()->InitLib(sError)) {
            DebugLog(QString("开放式门架动态库初始化失败,Error:%1").arg(sError));
        } else
            DebugLog(QString("开放式门架动态库初始化成功"));
    }

    //初始化化LaneCtrl类
    Ptr_ETCCtrl->InitETCLaneCtrl(Ptr_Info->GetStationID(), Ptr_Info->GetLaneId(),
                                 Ptr_Info->GetLaneType());
    // QString sBackUpUrl = QString("https://************:8890/api/v1/upload");
    Ptr_ETCCtrl->GetDataMgr()->SetUrl(sUpLoadUrl, sUpLoadUrlBak, sBinFileAuth);  //
    if (!connect(Ptr_ETCCtrl->GetDataMgr(), SIGNAL(NotifyDataSaveFailed(int, quint32, QString)),
                 this, SLOT(OnSaveDataFailedEvent(int, quint32, QString)))) {
        ErrorLog(QString("连接数据保存失败信号-槽失败"));
    }
    if (!connect(Ptr_ETCCtrl->GetDataMgr(), SIGNAL(NotifyNetWorkStatusChange(qint32)), this,
                 SLOT(OnNetWorkChanged(qint32)))) {
        ErrorLog(QString("连接网络状态信号-槽失败"));
    }

    connect(this, SIGNAL(NotifyShowQrCode()), this, SLOT(OnShowQrCode()));

    // 3、设置脱机数据存储模块
    if (!Ptr_ETCCtrl->InitLocalDB(Ptr_Info->GetDBPath(), Ptr_Info->GetBakDBPath(),
                                  Ptr_Info->bRemoveBakFile())) {
        sError = QString("数据库初始化失败,请联系维护人员");
        ChMessageOnlyOk_Error(sError);
        return false;
    }

    // 工班信号
    if (!QObject::connect(Ptr_ETCCtrl->GetShiftMgr(),
                          SIGNAL(NotifyWorkShiftChanged(CShiftSumInfo, CShiftSumInfo, bool)), this,
                          SLOT(OnWorkShiftChangedEvent(CShiftSumInfo, CShiftSumInfo, bool)))) {
        ErrorLog("连接工班切换信号-槽失败");
    }
    if (!QObject::connect(Ptr_ETCCtrl->GetShiftMgr(), SIGNAL(NotifyShiftEnd(CShiftSumInfo, bool)),
                          this, SLOT(NotifyShiftEnd(CShiftSumInfo, bool)))) {
        ErrorLog("连接班次结束信号-槽失败");
    }
    QObject::connect(Ptr_ETCCtrl, SIGNAL(NotifySaveWaste()), this, SLOT(DisplayCurShiftSumInfo()));

    //票据信息
    /*
    if (Ptr_Info->IsExitLane()) {
        if (!QObject::connect(Ptr_ETCCtrl->GetInvoiceInfo(),
                              SIGNAL(OnInvoiceChange(quint64, quint64, quint32)), this,
                              SLOT(OnInvoiceChange(quint64, quint64, quint32)))) {
            ErrorLog("连接发票信号-槽失败");
        }
    }*/

    if (Ptr_Info->bGrantPaperCard()) {
        DebugLog("电子纸卡");
        if (!QObject::connect(CPaperCardMgr::GetSingleInst(),
                              SIGNAL(NotifyPaperNoChanged(quint64, int, QString, QString)), this,
                              SLOT(OnPaperNoChange(quint64, int, QString, QString)))) {
            ErrorLog("连接纸卡信号-槽失败");
        } else {
            DebugLog("电子纸卡成功");
        }

        bool bInit = CPaperCardMgr::GetSingleInst()->InitPaperMgr(
                    Ptr_Info->GetStationID(), Ptr_Info->GetLaneId(), Ptr_Info->GetHexLaneID(),
                    Ptr_Info->IsEntryLane());
        if (!bInit) {
            ErrorLog(QString("纸卡数据库初始化失败"));
            return false;
        }
        //  CPaperCardMgr::GetSingleInst()->DownLoadPaperNo();
    }
    //工班相关的信号槽提到AppStart之前，这样能够处理到班次切换的信号
    if (!Ptr_ETCCtrl->AppStart(sError)) {
        return false;
    }
    DisplayLastShiftSumInfo();
    DisplayCurShiftSumInfo();

    QDateTime curTime = QDateTime::currentDateTime();
    if (Ptr_Info->IsEntryLane())
        Ptr_ETCCtrl->SaveAppStartWaste_En(curTime, 1);
    else
        Ptr_ETCCtrl->SaveAppStartWaste_Ex(curTime, 1);

    if (!connect(&m_HeadTimer, SIGNAL(timeout()), Ptr_ETCCtrl, SLOT(sendHeartbeat())))
        ErrorLog("心跳连接失败");
    m_HeadTimer.start(2 * 60 * 1000);
    connect(Ptr_ETCCtrl, SIGNAL(showOnDutyTime(const QDateTime &)), this,
            SLOT(OnShowOnDutyTime(const QDateTime &)));

    CDeviceFactory::InitDevFactory(Ptr_Info->GetLaneType(), Ptr_Info->bHaveCardMgr());

    //显示收费站名称和车道名称
    ShowStationName(Ptr_Info->GetStationName());
    QString sLaneName = QString("%1").arg(Ptr_Info->GetLaneId(), 2, 10, QLatin1Char('0'));
    ShowLaneName(Ptr_Info->GetLaneType(), sLaneName);
    Ptr_RemoteCtrl->InitLaneInfo();

    //初始化设备,首先连接设备信号
    //这个函数不能放前面，有可能有些设备还没创建，导致信号槽连接不上。
    ConnectAllDevSignal();

    InitRsuProcessor();
    if (Ptr_Info->IsEntryLane()) {
        ETCDelay::GetSingleStance()->SetOrgInfo(
                    Ptr_Info->GetGBStationId(), Ptr_Info->GetStationName(), Ptr_Info->GetGBLaneId());
        ETCDelay::GetSingleStance()->SetUrl(Ptr_Info->GeteETCDelayUrl());
        ETCDelay::GetSingleStance()->ResumeThread();
    }

    // ETC交易界面显示
    connect(this, SIGNAL(NotifyETCDisplayEvent(int, int, QList<QVariant>)), this,
            SLOT(OnETCDisplayMsgEvent(int, int, QList<QVariant>)));
    //提示信息
    connect(this, SIGNAL(NotifyShowPromptEvent(QString, bool, bool)), this,
            SLOT(OnShowPromptMsgEvent(QString, bool, bool)));
    //
    ShowLog(QString("设备初始化"));
    if (!InitDevice(sError)) return false;
    ShowLog(QString("设备初始化完成"));

    //初始化车辆外轮廓尺寸检测仪状态
    InitVehicleOutlineDevStatus();

    VDMLibSingleton::instance()->VdmInit();

    //出口软件，判断支付方式的初始化是否成功
    if (Ptr_Info->IsExitLane() && !InitMobilePay(sError)) {
        return false;
    }

    for (int i = 0; i < 2; ++i) {
        CFareDisplayer_GB *pFD = CDeviceFactory::GetETCFareDisPlayer(i);
        if (pFD)
            pFD->SetStation(Ptr_Info->GetStationName(), Ptr_Info->GetFareShow(),
                            Ptr_Info->GetLaneType());
    }

    //打开视频采集卡
    QRect rctVideo = m_pFrmVideoCard->geometry();
    if (CDeviceFactory::GetVideoCard()->Init(m_pFrmVideoCard->winId(), rctVideo.left(),
                                             rctVideo.top(), rctVideo.width(), rctVideo.height(),
                                             800, 600)) {
        CDeviceFactory::GetVideoCard()->Display();
    }
    //如果有卡机，实始化自助收卡机的界面
    //如果是自助收卡机，初始化卡机界面的信息
    if (Ptr_Info->bHaveCardMgr() && Ptr_Info->IsExitLane()) {
        CDeviceFactory::GetAutoExTollScreen()->InitLaneInfo(Ptr_Info->GetStationName(),
                                                            Ptr_Info->GetLaneName());
    }

    CFareCalcUnit::SetUrl(Ptr_Info->GetFeeUrl(), Ptr_Info->GetEntryUrl(),
                          Ptr_Info->GetGBStationId());

    CFareCalcUnit::SetQryFlagUrl(Ptr_Info->GetFlagUrl());
    if (Ptr_Info->IsExitLane() && Ptr_Info->bEticket()) {
        if (!CDeviceFactory::InitInvoiceQrCode()) {
            sError = QString("票据二维码动态库初始化失败");
            return false;
        }
        DebugLog(QString("采用电子票"));
    } else {
        DebugLog("非电子票");
    }
    m_bAppInitOk = true;
    Ptr_ETCCtrl->ChangeToUnLoginState();

    m_LaneServer.listen(QHostAddress::Any, LANESERVER_LISTEN_PORT);

    // 校时
    // StartAskTimeTimer();
    // 参数下载定时器
    m_TimerReqParams = startTimer(5 * 60 * 1000);  // 5分钟下载一次参数文件
    m_TimerReqParamsLf = 0;  // startTimer(24*60*60*1000);//24小时下载一次参数文件
    m_TimerOutUseTimeParam = startTimer(60 * 1000);
    m_TimeHeadID = 0;  // startTimer(5*60*1000);

    //检查打印纸状态

    if (Ptr_Info->bGrantPaperCard()) {
        m_TimerPrintPaper = startTimer(2 * 1000);
    }

    m_PrintLogTimer.start(1000);  //启动打印后，一小时打印一次日志

    UpdateVehState();
    DebugLog(QString("启动参数下载任务"));
    CReqParamTask task;
    task.bDownLoadCardBlack = 1;

    CHttpReqParams::GetHttpReqParams()->StartDownLoadParam(task);

    QString sBatchNo;
    int vehicleDataCount = 0;
    int vehiclePicCount = 0;
    Ptr_Info->GetVlprSmInfo(sBatchNo, vehicleDataCount, vehiclePicCount);
    if (m_sVLPRHourBatchNo == sBatchNo) {
        m_VehicleDataCount = vehicleDataCount;
        m_VehiclePicCount = vehiclePicCount;
    }
    m_VLPRSumTimer.start(5 * 60 * 1000);

    /*
    if (!QObject::connect(&m_UploadLogTimer, SIGNAL(timeout()), this, SLOT(OnUploadLog()))) {
        ErrorLog(QString("日志上传信号-槽连接失败"));
    }
    m_UploadLogTimer.start(10 * 1000);
    */
    OnPrintLog();
    //首次启动常规任务m_Routine10SecTimer,先立即执行一次
    m_Routine10SecTimer.start(10);

    QString stime = QTime::currentTime().toString("hhmmss");
    StdInfoLog(LogKey::OneKey_LaneInfo, LogKey::LaneInfo_AppStart, stime, stime, QString(""));

    if (Ptr_Info->bRemoteControl()) {
        connect(Ptr_RemoteCtrl,
                SIGNAL(OnInputVehInfoByRemote(qint32, int, QString, qint32, QString)), this,
                SLOT(OnInputVehInfoByRemote(qint32, int, QString, qint32, QString)),
                Qt::QueuedConnection);
        connect(Ptr_RemoteCtrl, SIGNAL(OnSelectPayTypeByRemote(int)), this,
                SLOT(OnSelectPayTypeByRemote(int)), Qt::QueuedConnection);
        connect(Ptr_RemoteCtrl, SIGNAL(OnBackInputStateByRemote()), this,
                SLOT(OnBackInputStateByRemote()), Qt::QueuedConnection);
        connect(Ptr_RemoteCtrl, SIGNAL(OnRemoteSimulateDownBar()), this,
                SLOT(OnRemoteSimulateDownBar()), Qt::QueuedConnection);

        connect(RemoteMsgMgr::GetSingleInst(), SIGNAL(NotifyRemoteCmd(int, QString)), this,
                SLOT(OnRemoteControlCmd(int, QString)));
        RemoteServer *pRemoteServer = RemoteServer::GetSingleInst();
        connect(pRemoteServer, SIGNAL(NotifyNewConnection(QString)), this,
                SLOT(OnRemoteConnected(QString)));
        connect(pRemoteServer, SIGNAL(NotifyDisConnected(QString)), this,
                SLOT(OnRemoteDisConnected(QString)));
        connect(pRemoteServer, SIGNAL(NotifyRemoteCmd(int, QString)), this,
                SLOT(OnRemoteControlCmd(int, QString)));
        bool bRlt = pRemoteServer->StartServer(Ptr_Info->GetLocalPort());
        if (!bRlt) {
            ErrorLog("Remote server listen failed");
        }
        connect(RemoteClient::GetSingleInst(), SIGNAL(Notify_StateChange(int)), this,
                SLOT(OnReomteClientStateChanged(int)));
        RemoteClient::GetSingleInst()->SetHost(
                    Ptr_Info->GetRemoteServerIP(),
                    Ptr_Info->GetRemotePort());  // QString("***********")
        RemoteClient::GetSingleInst()->StartWorkThead();
    }

    if (Ptr_Info->bUseVehLib()) {
        QString sQryTokenUrl, sRefreshTokenUrl, sSaveUrl;
        Ptr_Info->GetTokenUrl(sQryTokenUrl, sRefreshTokenUrl, sSaveUrl);
        CVehTypeLibMgr::GetVehTypeLibMgr()->InitVehTypeLib(Ptr_Info->GetVehLibUrl(), sQryTokenUrl,
                                                           sRefreshTokenUrl, sSaveUrl);
    }
    CVIPUMgr::GetVIPUMgr()->InitVIPUMgr();
    //所有程序启动完毕后,再检测IO状态
    int nMaxOn = 0, nMaxOff = 0;
    Ptr_Info->GetMaxLoopTime(nMaxOn, nMaxOff);
    CDeviceFactory::GetIOCard()->SetMaxTime(nMaxOn, nMaxOff);
    CDeviceFactory::GetIOCard()->StartDetection();
    CClearFileMgr::GetClearFileMgr()->InitClearFileMgr(Ptr_Info->GetPicPath());

    if (Ptr_Info->IsExitLane() && Ptr_Info->bHaveCardMgr()) {
        //程序启动强制回收卡机头
        CDeviceFactory::GetPayMentMgr()->SetVehState(false, true);
    }

#ifdef QT_DEBUG
    // LoadVCRResult();
    QString sKey = QString("bimxbimxbimxbimx");
    QString sPassword = CTokenThread::AesPassWord(QString("123456"), sKey);
    // CAbstractState::SetAutoVehPlate(QString("赣A12346"), 0);
    DebugLog(sPassword);
    // m_pFrmVehInfo->ShowAutoVehPlate(VP_COLOR_LittleGREEN, QString("赣ANN891F"));
    CVehInfo vehInfo;
    vehInfo.VehClass = VC_Car2;
    QString sPlate = QString("赣A12345");
    qsnprintf(vehInfo.szVehPlate, sizeof vehInfo.szVehPlate, "%s", sPlate.toLocal8Bit().data());
    qsnprintf(vehInfo.szAutoVehPlate, sizeof vehInfo.szAutoVehPlate, "%s",
              sPlate.toLocal8Bit().data());
    vehInfo.nAutoVehPlateColor = 1;
    vehInfo.nVehPlateColor = 1;
    vehInfo.nVehClassWay = VehClassway_Input;
    CVehTypeLibMgr::GetVehTypeLibMgr()->DebugLogVehInfo(vehInfo);

    CFlagListTable *pTable = (CFlagListTable *)CParamFileMgr::GetParamFile(cfFlagList);
    if (pTable) {
        QString sVlp = QString("赣E83J08");
        int nvlpc = 0;
        QString sCardId = QString("****************");
        QString sOBUId = QString("");
        QDateTime enTime =
                QDateTime::fromString("2024-07-20 01:00:00", QString("yyyy-MM-dd hh:mm:ss"));
        CFlagListInfo flagListInfo;
        bool bRlt = pTable->QryFlagInfo(sVlp, nvlpc, sCardId, sOBUId, enTime, flagListInfo);
    }

#endif
    return true;
}

bool QDlgMain::InitStationDoorInfo()
{
    CStationDoorTable *pTable = (CStationDoorTable *)CParamFileMgr::GetParamFile(cfStationDoor);
    if (!pTable) return false;
    QString sStationId = Ptr_Info->GetGBStationId();
    quint8 bDirect = Ptr_Info->IsEntryLane() ? 1 : 2;
    CStationDoorInfo Info;
    if (!pTable->QueryStationDoorInfo(sStationId, bDirect, Info)) {
        return false;
    }

    TollDoorFrameTable *pDoorTable = (TollDoorFrameTable *)CParamFileMgr::GetParamFile(cfDoorCode);
    if (!pDoorTable) {
        return false;
    }

    TollDoorFrame doorFrame;
    bool bRlt = pDoorTable->QueryGantryInfo(QString("36"), Info.sGantryId, doorFrame);
    if (!bRlt) {
        return false;
    }
    QString scurTime = QDateTime::currentDateTime().toString("yyyy-MM-dd");
    if (scurTime < doorFrame.startDate || scurTime > doorFrame.endDate) {
        // return false;
    }

    CVirGantryInfo virGantryInfo;
    virGantryInfo.nIndex = 0;
    virGantryInfo.nStationId = Ptr_Info->GetStationID();
    virGantryInfo.sGBStationId = Ptr_Info->GetGBStationId();
    virGantryInfo.sStationHex = Ptr_Info->GetHexStationID();
    virGantryInfo.sStationName = Ptr_Info->GetStationName();
    virGantryInfo.sGantryHex = doorFrame.etcGantryHex;
    virGantryInfo.sGantryId = doorFrame.gantryID;
    virGantryInfo.sGantryName = doorFrame.gantryName;
    virGantryInfo.nSn = doorFrame.gantryID.right(3).left(2).toInt();
    virGantryInfo.bDirection = Ptr_Info->IsEntryLane() ? 1 : 2;
    virGantryInfo.bLaneId = Ptr_Info->GetLaneId();
    virGantryInfo.sGBLaneId = Ptr_Info->GetGBLaneId();
    virGantryInfo.sLaneHex = Ptr_Info->GetHexLaneID();
    virGantryInfo.sTollIntervals = doorFrame.tollIntervals;
    CTollGantryMgr::GetTollGantryMgr()->AddGantryInfo(virGantryInfo);
    CTollGantryMgr::GetTollGantryMgr()->SetBaseInfo(doorFrame.gantryID, doorFrame.etcGantryHex,
                                                    doorFrame.tollIntervals, doorFrame.gantryName,
                                                    1);  // doorFrame.gantryType);

    Ptr_Info->AddVirStationInfo(virGantryInfo);
    this->ShowGantryName(doorFrame.gantryName);
    return true;
}

bool QDlgMain::InitOrgBasicInfo()
{
    COrgBasicInfoTable *pTable = (COrgBasicInfoTable *)CParamFileMgr::GetParamFile(cfOrgBasicInfo);
    COrgBasicInfo orgBasicInfo;
    if (pTable) {
        QString sStationHex = Ptr_Info->GetHexStationID();
        QString sGBStatioId, sGBLaneId;
        if (!pTable->QryOrgBasicInfo(36, ORG_TYPE_STATION_GB, sStationHex, orgBasicInfo)) {
            DebugLog("查询本站对应映射信息失败");
            return false;
        }
        sGBStatioId = orgBasicInfo.sId;
        QString sLaneHex = Ptr_Info->GetHexLaneID();
        COrgBasicInfo laneOrgBasicInfo;

        if (!pTable->QryOrgBasicInfo(36, ORG_TYPE_LANE_GB, sLaneHex, laneOrgBasicInfo)) {
            DebugLog("查询当前车道映射信息失败");
            return false;
        }
        sGBLaneId = laneOrgBasicInfo.sId;
        Ptr_Info->SetGBStationInfo(sGBStatioId, sGBLaneId);
        qint32 nStationid = Ptr_Info->GetStationID();
        qint32 road = nStationid / 100 % 1000;
        get_log(LOGGBSTD)->SetFileNamePrefix(
                    QString("fangxing_36_%1_%2_%3_3_").arg(road).arg(sGBStatioId).arg(sGBLaneId));
        Ptr_Info->SetStationName(orgBasicInfo.sName);
        return true;
    }
    return false;
}

bool QDlgMain::GetLocalStationinfo(QString &sError)
{
    COrgCode orgCode;
    COrgCodeTable *pOrgTable = (COrgCodeTable *)CParamFileMgr::GetParamFile(cfOrgCode);
    if (!pOrgTable) {
        sError = QString("机构信息参数文件未创建");
        ErrorLog(sError);
        return false;
    }
    bool bRlt = pOrgTable->QueryOrg(orgCode, Ptr_Info->GetStationID());
    if (!bRlt) {
        sError = QString("查询本站信息失败");
        return false;
    }
    DebugLog(QString("本站基本信息,orgCode:%1,orgName:%2,bl_road:%3")
             .arg(orgCode.sOrgCode)
             .arg(orgCode.sOrgSName)
             .arg(orgCode.sBL_Road));

    COrgCode orgRoad;
    bRlt = pOrgTable->QueryOrg(orgRoad, orgCode.sBL_Road.toInt(), ORG_TYPE_SUBCENTER);
    if (!bRlt) {
        DebugLog(QString("查询路段%1名称失败").arg(orgCode.sBL_Road));
    } else {
    }
    Ptr_Info->SetOrgCode(orgCode, orgRoad.sOrgSName);
    return true;
}

void QDlgMain::AppDestroy()
{
    if (m_bAppInitOk) {

        StopAllTimer();

        if (Ptr_Info->bRemoteControl()) {
            RemoteServer *pRemoteServer = RemoteServer::GetSingleInst();
            if (pRemoteServer) {
                pRemoteServer->StopServer();
            }
        }
        CPaperCardMgr::GetSingleInst()->RelasePaperCardMgr();
        CVehTypeLibMgr::GetVehTypeLibMgr()->ReleaseVehTypeLibMgr();

        DisConnectDevSignal();

        // 等待一段时间，确保所有异步操作完成
        SleeperThread::msleep(500);

        QDateTime curTime = QDateTime::currentDateTime();
        CVIPUMgr::GetVIPUMgr()->FreeVIPUMgr();

        if (Ptr_Info->IsEntryLane())
            Ptr_ETCCtrl->SaveAppStartWaste_En(curTime, 2);
        else
            Ptr_ETCCtrl->SaveAppStartWaste_Ex(curTime, 2);

        if (Ptr_Info->bOpenLane()) {
            DebugLog("释放开放式门架动态库");
            GantryByPlate::ReleaseStGantryByPlate();
        }
        DebugLog("释放参数下载框");
        if (m_pParamInfoDlg) {
            delete m_pParamInfoDlg;
            m_pParamInfoDlg = NULL;
        }
        DebugLog(QString("释放参数下载模块"));
        CHttpReqParams::ReleaseStaticPointer();
        DebugLog("释放设备");
        CDeviceFactory::ReleaseAllDev();
        DebugLog("释放状态机");
        CETCLaneCtrl::GetETCLaneCtrl()->ClearAllState();
        DebugLog("释放车道控制模块");
        CETCLaneCtrl::ReleaseStETCLaneCtrl();
        DebugLog("关闭车道监听端口");
        // 关闭监听
        m_LaneServer.close();
        //关闭定时器
        //StopAllTimer();

        CTransInfoShared::GetTransInfoShared()->FreeLib();
        DebugLog(QString("释放组播控件"));
        m_bAppInitOk = false;
    }

    if (m_VLPRSumTimer.isActive()) m_VLPRSumTimer.stop();
    Ptr_Info->SetVlprSmInfo(m_sVLPRHourBatchNo, m_VehicleDataCount, m_VehiclePicCount);
    // curl_global_cleanup();
    Curl::Global_Cleanup();
    DebugLog(QString("车道程序退出完毕"));
}

void QDlgMain::ExitApp(int nExitCode)
{
    m_nExitCode = nExitCode;
    connect(&m_ExitTimer, SIGNAL(timeout()), this, SLOT(OnExitTimer()));
    m_ExitTimer.start(100);
}

void QDlgMain::ShowAuthDlg() { m_pPsamDlg->doModalShow(); }

bool QDlgMain::InitOpenStationInfo()
{
    // 1、取出虚拟站列表
    SpParaTable *pTable = (SpParaTable *)CParamFileMgr::GetParamFile(cfSpPara);
    if (!pTable) {
        return false;
    }

    QStringList sVerList;
    if (!pTable->InitVirStationInfo(Ptr_Info->GetStationID(), sVerList, false)) {
        return false;
    }

    int nIndex = 1;
    int nCount = 0;
    foreach (QString str, sVerList) {
        // 1、根据hex，从映射表里查门架id
        COrgBasicInfoTable *pOrgTable =
                (COrgBasicInfoTable *)CParamFileMgr::GetParamFile(cfOrgBasicInfo);
        COrgBasicInfo orgBasicInfo;
        if (!pOrgTable) return false;

        QString sGantryHex = str;
        QString sGantryId;
        if (!pOrgTable->QryOrgBasicInfo(36, ORG_TYPE_GANTRY_GB, sGantryHex, orgBasicInfo)) {
            DebugLog(QString("查询门架%1对应映射信息失败").arg(sGantryHex));
            return false;
        }
        sGantryId = orgBasicInfo.sId;
        QString sGantryName = orgBasicInfo.sName;

        // 2、根据gantryId查虚拟站Id
        CStationDoorTable *pStationDoorTable =
                (CStationDoorTable *)CParamFileMgr::GetParamFile(cfStationDoor);
        if (!pStationDoorTable) return false;

        CStationDoorInfo Info;
        if (!pStationDoorTable->QueryStationInfoByGantryId(sGantryId, Info)) {
            return false;
        }

        // 3、根据虚拟站id 查站信息
        COrgBasicInfo orgStationInfo;
        if (!pOrgTable->QryOrgBasicInfo(Info.sStationId, orgStationInfo)) {
            return false;
        }
        COrgBasicInfo orgLaneInfo;
        int nLaneId = Ptr_Info->GetLaneId();
        QString sLaneHex = orgStationInfo.sHex +
                QString("%1").arg(nLaneId, 2, 16, QLatin1Char('0')).toUpper().right(2);
        if (!pOrgTable->QryOrgBasicInfo(36, ORG_TYPE_LANE_GB, sLaneHex, orgLaneInfo)) {
            DebugLog(QString("虚拟站不存在车道%1").arg(sLaneHex));
            nLaneId = nLaneId % 5;
            if (0 == nLaneId) nLaneId = 5;
            sLaneHex = orgStationInfo.sHex +
                    QString("%1").arg(nLaneId, 2, 16, QLatin1Char('0')).toUpper().right(2);
            if (!pOrgTable->QryOrgBasicInfo(36, ORG_TYPE_LANE_GB, sLaneHex, orgLaneInfo)) {
                DebugLog(QString("虚拟站不存在车道%1").arg(sLaneHex));
                orgLaneInfo.sId = orgStationInfo.sId +
                        QString("1010%1").arg(nLaneId % 1000, 2, 10, QLatin1Char('0')) +
                        QString("0");
                orgLaneInfo.sHex = sLaneHex;
            }
        }
        DebugLog(QString("当前虚拟车道%1,%2").arg(sLaneHex).arg(orgLaneInfo.sId));

        CVirGantryInfo gantryInfo;
        quint8 bStationRaw[2] = {0};
        Hex2Raw(bStationRaw, orgStationInfo.sHex.right(4));
        quint16 nStationId = qFromBigEndian<quint16>(bStationRaw);

        gantryInfo.nIndex = nIndex;
        nIndex++;

        gantryInfo.nStationId = 3600000 + nStationId;
        gantryInfo.sStationHex = orgStationInfo.sHex;
        gantryInfo.sGBStationId = orgStationInfo.sId;
        gantryInfo.sStationName = orgStationInfo.sName;
        gantryInfo.sGantryHex = sGantryHex;
        gantryInfo.sGantryId = sGantryId;
        gantryInfo.sGantryName = sGantryName;
        gantryInfo.nSn = sGantryId.right(3).left(2).toInt();
        gantryInfo.bDirection = 1;
        gantryInfo.bLaneId = nLaneId;
        gantryInfo.sGBLaneId = orgLaneInfo.sId;
        gantryInfo.sLaneHex = orgLaneInfo.sHex;
        DebugLog(QString("添加开放式门架:%1,%2,%3,%4")
                 .arg(gantryInfo.sStationHex)
                 .arg(gantryInfo.sGBStationId)
                 .arg(gantryInfo.nStationId)
                 .arg(gantryInfo.sGBLaneId));
        if (Ptr_Info->IsEntryLane())
            CTollGantryMgr::GetTollGantryMgr()->AddGantryInfo(
                        gantryInfo);  // SetBaseInfo(doorFrame.gantryID,doorFrame.etcGantryHex,
        // doorFrame.tollIntervals,doorFrame.gantryName,1);
        else
            Ptr_Info->AddVirStationInfo(gantryInfo);
        nCount++;
    }
    return nCount > 0;
}

bool QDlgMain::InitOpenStationInfo_New()
{
    //初始化本车道序列号
    CBatchMgr::GetBatchMgr()->UpdateBatchInfo(Ptr_Info->GetHexLaneID(), 0);

    // 1、取出虚拟站列表
    SpParaTable *pTable = (SpParaTable *)CParamFileMgr::GetParamFile(cfSpPara);
    if (!pTable) {
        return false;
    }

    //如果批次流水号是旧格式转过来的，就用门架列表设置一遍之前的流水信息
    if (CBatchMgr::GetBatchMgr()->bFromOldFormat()) {
        QStringList sOldVerList;
        //即使没有105参数，也要继续设置当前门架信息
        if (!pTable->InitVirStationInfo(Ptr_Info->GetStationID(), sOldVerList, false)) {
            // return false;
            DebugLog(QString("特殊参数不存在105参数"));
        }
        QString scurGantry;
        CVirGantryInfo gantryInfo;
        bool bOpenGantry = false;
        CTollGantryMgr::GetTollGantryMgr()->GetCurGantryInfo(scurGantry, gantryInfo, bOpenGantry);
        sOldVerList.push_front(gantryInfo.sGantryHex);
        CBatchMgr::GetBatchMgr()->UpdateBatchInfo(sOldVerList);
    }
    // QStringList sVerList;
    QList<COpenGantryParam> opGantryParams;
    //取一遍新参数
    if (!pTable->InitVirStationInfo(Ptr_Info->GetStationID(), opGantryParams, true)) {
        //取一遍旧参数
        if (!pTable->InitVirStationInfo(Ptr_Info->GetStationID(), opGantryParams, false)) {
            return false;
        }
    }

    int nIndex = 1;
    int nCount = 0;
    QList<CVirGantryInfo> openList;
    foreach (COpenGantryParam param, opGantryParams) {
        // 1、根据hex，从映射表里查门架id
        COrgBasicInfoTable *pOrgTable =
                (COrgBasicInfoTable *)CParamFileMgr::GetParamFile(cfOrgBasicInfo);
        COrgBasicInfo orgBasicInfo;
        if (!pOrgTable) return false;

        QString sGantryHex = param.sGantryHex;
        QString sGantryId;
        if (!pOrgTable->QryOrgBasicInfo(36, ORG_TYPE_GANTRY_GB, sGantryHex, orgBasicInfo)) {
            DebugLog(QString("查询门架%1对应映射信息失败").arg(sGantryHex));
            continue;
        }
        sGantryId = orgBasicInfo.sId;
        QString sGantryName = orgBasicInfo.sName;

        // 2、根据gantryId查虚拟站Id
        CStationDoorTable *pStationDoorTable =
                (CStationDoorTable *)CParamFileMgr::GetParamFile(cfStationDoor);
        if (!pStationDoorTable) return false;

        CStationDoorInfo Info;
        if (!pStationDoorTable->QueryStationInfoByGantryId(sGantryId, Info)) {
            return false;
        }

        // 3、根据虚拟站id 查站信息
        COrgBasicInfo orgStationInfo;
        if (!pOrgTable->QryOrgBasicInfo(Info.sStationId, orgStationInfo)) {
            return false;
        }
        COrgBasicInfo orgLaneInfo;
        int nLaneId = Ptr_Info->GetLaneId();
        QString sLaneHex = orgStationInfo.sHex +
                QString("%1").arg(nLaneId, 2, 16, QLatin1Char('0')).toUpper().right(2);
        if (!pOrgTable->QryOrgBasicInfo(36, ORG_TYPE_LANE_GB, sLaneHex, orgLaneInfo)) {
            DebugLog(QString("虚拟站不存在车道%1").arg(sLaneHex));
            nLaneId = nLaneId % 5;
            if (0 == nLaneId) nLaneId = 5;
            sLaneHex = orgStationInfo.sHex +
                    QString("%1").arg(nLaneId, 2, 16, QLatin1Char('0')).toUpper().right(2);
            if (!pOrgTable->QryOrgBasicInfo(36, ORG_TYPE_LANE_GB, sLaneHex, orgLaneInfo)) {
                DebugLog(QString("虚拟站不存在车道%1").arg(sLaneHex));
                orgLaneInfo.sId = orgStationInfo.sId +
                        QString("1010%1").arg(nLaneId % 1000, 2, 10, QLatin1Char('0')) +
                        QString("0");
                orgLaneInfo.sHex = sLaneHex;
            }
        }
        DebugLog(QString("当前虚拟车道%1,%2").arg(sLaneHex).arg(orgLaneInfo.sId));

        CVirGantryInfo gantryInfo;
        quint8 bStationRaw[2] = {0};
        Hex2Raw(bStationRaw, orgStationInfo.sHex.right(4));
        quint16 nStationId = qFromBigEndian<quint16>(bStationRaw);

        gantryInfo.nIndex = nIndex;
        nIndex++;

        gantryInfo.nStationId = 3600000 + nStationId;
        gantryInfo.sStationHex = orgStationInfo.sHex;
        gantryInfo.sGBStationId = orgStationInfo.sId;
        gantryInfo.sStationName = orgStationInfo.sName;
        gantryInfo.sGantryHex = sGantryHex;
        gantryInfo.sGantryId = sGantryId;
        gantryInfo.sGantryName = sGantryName;
        gantryInfo.nSn = sGantryId.right(3).left(2).toInt();
        gantryInfo.bDirection = 1;
        gantryInfo.bLaneId = nLaneId;
        gantryInfo.sGBLaneId = orgLaneInfo.sId;
        gantryInfo.sLaneHex = orgLaneInfo.sHex;
        gantryInfo.nMaxTime = param.nMaxTime;
        DebugLog(QString("添加开放式门架:%1,%2,%3,%4")
                 .arg(gantryInfo.sStationHex)
                 .arg(gantryInfo.sGBStationId)
                 .arg(gantryInfo.nStationId)
                 .arg(gantryInfo.sGBLaneId));

        openList.push_back(gantryInfo);

        /*
        if (Ptr_Info->IsEntryLane())
            CTollGantryMgr::GetTollGantryMgr()->AddGantryInfo(
                gantryInfo);  // SetBaseInfo(doorFrame.gantryID,doorFrame.etcGantryHex,
                              // doorFrame.tollIntervals,doorFrame.gantryName,1);

        Ptr_Info->AddVirStationInfo(gantryInfo);
        */
        nCount++;
    }
    if (nCount > 0) {
        if (Ptr_Info->IsEntryLane()) {
            CTollGantryMgr::GetTollGantryMgr()->AddOpenGantryInfo(openList);
        }
        Ptr_Info->AddOpenStationInfo(openList);
    }
    return nCount > 0;
}

void QDlgMain::RefreshParamVerInfo()
{
    //显示费率模块版本
    CBaseParamFile *pParamFile = CParamFileMgr::GetParamFile(cfFareDll);
    CCfgFileHead cfgFileHead;
    if (pParamFile) pParamFile->GetCfgFileHead(cfgFileHead);
    QString sVersion = "";
    sVersion = cfgFileHead.sVersion;
    CCfgFileHead newCfgFileHead;
    pParamFile->GetCfgFileHead(newCfgFileHead, true);
    if (newCfgFileHead.nFileID > 0) {
        sVersion = QString("%1(%2)").arg(sVersion).arg(newCfgFileHead.sVersion);
    }
    ShowFeeVersion(sVersion);

    pParamFile = CParamFileMgr::GetParamFile(cfGBCardBList);
    if (pParamFile) {
        pParamFile->GetCfgFileHead(cfgFileHead);
        sVersion = cfgFileHead.sVersion;
        ShowBlackListVersion(sVersion);
    }
    pParamFile = CParamFileMgr::GetParamFile(cfMinFee);
    if (pParamFile) {
        pParamFile->GetCfgFileHead(cfgFileHead);
        sVersion = cfgFileHead.sVersion;
        pParamFile->GetCfgFileHead(cfgFileHead, true);
        QString sNewVersion = cfgFileHead.sVersion;
        if (sNewVersion == sVersion) sNewVersion.clear();
        ShowMinFeeVersion(sVersion, sNewVersion);
    }
}

void QDlgMain::OnLaneStatusChanedEvent()
{
    qint32 nLaneStatus = Ptr_ETCCtrl->GetLaneStatus();
    ShowLaneState(nLaneStatus);
    switch (nLaneStatus) {
    case lsNormalWorking: {
        CShiftMgr *pShiftMgr = Ptr_ETCCtrl->GetShiftMgr();
        if (pShiftMgr) {
            QString sOperId = QString("%2(%1)").arg(pShiftMgr->m_dwOperId).arg(pShiftMgr->m_sOperName.isEmpty()?"未知":pShiftMgr->m_sOperName);
            ShowOperatorId(sOperId);
        }
        // veh->ChangPic();
        break;
    }
    case lsUnlogin: {
        CShiftMgr *pShiftMgr = Ptr_ETCCtrl->GetShiftMgr();
        if (pShiftMgr) {
            // if (0 != pShiftMgr->m_dwOperId) pShiftMgr->m_dwOperId = 0;
            // ShowOtperatorId("0000000");
        }
        // veh->ChangPic();
        break;
    }
    case lsSleep:
        break;
    default:
        break;
    }
}

void QDlgMain::OnWorkShiftChangedEvent(const CShiftSumInfo OldShiftSumInfo,
                                       const CShiftSumInfo newShiftSumInfo, bool bAutoLogin)
{
    CShiftParam ShiftParam;
    if (CShiftMgr::GetShiftParamFromShiftSum(newShiftSumInfo, ShiftParam)) {
        const QDate lDate = ShiftParam.tmWorkDate.date();
        CStdLog::StdLogLaneInfo(ShiftParam.tmWorkDate);
        CStdLog::StdLogDevInfo_ProductAndVersion();

        QString sLDate = lDate.toString("MMdd");
        QString sShiftName = QString("%1(%2)").arg(ShiftParam.sShiftName).arg(sLDate);
        ShowShiftInfo(lDate, sShiftName);
        ShowLog(QString("逻辑日：%1，班次：%2")
                .arg(lDate.toString("yyyyMMdd"))
                .arg(ShiftParam.sShiftName));
    }
    //开始上班
    if (bAutoLogin) {
        Ptr_ETCCtrl->AutoLogin();
    }
    DisplayCurShiftSumInfo();
}

void QDlgMain::OnChangeToUnloginState()
{
    m_tLoginTimer.stop();
    CETCLaneCtrl::GetETCLaneCtrl()->ChangeToUnLoginState();
}

void QDlgMain::OnKeyBClear()
{
    if (m_KeyBClearTimer.isActive()) {
        m_KeyBClearTimer.stop();
    }
    m_nKeyBTimes = 0;
}

void QDlgMain::OnClearVehFrmEvent(bool bClearTrans) { m_pFrmVehInfo->ClearAllVehInfo(bClearTrans); }

void QDlgMain::OnShowQrCode()
{
    ShowQRCode(NULL);
    ShowQRCode(&m_qrCodeInfo);
}

void QDlgMain::OnInvoiceChange(quint64 llCode, quint64 llStartNo, quint32 dwCount)
{
    if (m_pFrmExtraInfo) {
        m_pFrmExtraInfo->ShowInvoice(llCode, llStartNo, dwCount);
    }
}

void QDlgMain::OnPaperNoChange(quint64 lCurNo, int nCnt, QString sTime, QString sEndTime)
{
    if (m_pFrmExtraInfo) {
        QString sTmpTime;
        if (sTime.length() > 0)
            sTmpTime = QString("%1-%2").arg(sTime.right(10)).arg(sEndTime.right(10));
        m_pFrmExtraInfo->ShowPaperNo(lCurNo, nCnt, sTmpTime, Ptr_Info->IsEntryLane());
    }
}

void QDlgMain::OnRsuError(int nIndex, quint8 bFrameId, quint8 bErrorCode)
{
    DebugLog(
                QString("天线%1报错,bFrameId:%2,bErrorCode:%3").arg(nIndex).arg(bFrameId).arg(bErrorCode));
    CTransInfo *pcurTransInfo = Ptr_ETCCtrl->GetCurTransInfo(nIndex);
    if (pcurTransInfo) pcurTransInfo->SetFrameId(bFrameId, bErrorCode);
}

void QDlgMain::OnStayOutTimeEvent(QString sPlate, quint8 bType)
{
    // CMessageBox::WarnOnTime("等待超时,确定键落杆,取消退处]","落杆",15);
    // Ptr_ETCCtrl->ChangeToMotorcade();
    if (0 == bType) {
        //抬杆
        QMutexLocker locker(&m_StayOutMt);
        if (m_bStayOut) {
            m_pStayOutTimeDlg->OnClickedCancel();
            m_bStayOut = false;
        }
    } else if (1 == bType) {
        //超时
        CDeviceFactory::FareDisplay_ShowHelpMsg(DevIndex_ALL,
                                                QString("%1\n超时停留\n请注意落杆").arg(sPlate));
        StdInfoLog(LogKey::OneKey_DevInfo, LogKey::DevInfo_FD,
                   QTime::currentTime().toString("hhmmsszzz"),
                   CDeviceFactory::GetIOCard()->GetDevId(), QString("超时停留,请注意落杆"));
        if (Ptr_Info->bHaveBackDev())
            CDeviceFactory::StartAlarm(DevIndex_Second, 10000);
        else
            CDeviceFactory::StartAlarm(DevIndex_First, 10000);
        {
            QMutexLocker locker(&m_StayOutMt);
            m_bStayOut = true;
        }
        bool bRlt = m_pStayOutTimeDlg->DoWarnMessageOnTime(QString("落杆"), 20);

        if (bRlt) {
            Ptr_ETCCtrl->SimulateDownBar();
            RefreshPassVehQueue();
            RefreshTradeVehQueue();
            CDeviceFactory::FareDisplay_ShowHelpMsg(
                        DevIndex_ALL, QString("%1\n超时停留\n请人工处理").arg(sPlate));
        }
    }
}

void QDlgMain::OnVehInvoiceEvent() { ShowLog("车辆闯关"); }

void QDlgMain::OnUploadLog()
{
    m_UploadLogTimer.stop();
    Ptr_ETCCtrl->UploadLog1();
    // m_UploadLogTimer.start(10 * 60 * 1000);
    m_UploadLogTimer.start(20 * 1000);
}

void QDlgMain::OnLoadNewParamFileMsg(qint32 nCfgFile, CCfgFileHead cfgFileHead, bool bPreLoad)
{
    // DebugLog();
    CBaseParamFile *pBaseFile = (CBaseParamFile *)CParamFileMgr::GetParamFile((CCfgFile)nCfgFile);
    if (pBaseFile) {
        if (!bPreLoad) {
            QString sInfo = QString("参数%1,加载成功").arg(pBaseFile->GetDisplayName());
            this->ShowLog(sInfo);
            if (nCfgFile == CCfgFile(cfSpPara)) {
                SpParaTable *pSpTable = (SpParaTable *)pBaseFile;
                bool bRlt = pSpTable->InitMaxFee();
                if (!bRlt) {
                    ShowLog("最大费率限额初始化失败");
                } else {
                    ShowLog("最大费率限额初始化完毕");
                }

                bRlt = pSpTable->InitMinFeePercent_New();
                if (!bRlt) {
                    ShowLog("最小费率动态系数加载失败");
                } else
                    ShowLog("最小费率动态系数加载成功");

                bRlt = pSpTable->InitDifferentFeeParam();
                if (!bRlt) {
                    ShowLog("差异化计费参数加载失败");
                } else {
                    ShowLog("差异化计费参数加载成功");
                }

                if (m_bAppInitOk) {
                    DebugLog(QString("特殊参数更新,重新初始化开放门架"));
                    this->InitOpenStationInfo_New();
                }
            }
        }
    }
}

void QDlgMain::OnCardMachineEvent(int nEvent, int nPos, int nIndex)
{
    CAbstractState *pCurState = CAbstractState::GetCurState();
    DebugLog(QString("卡机事件:%1,nPos:%2,nInde:%3").arg(nEvent).arg(nPos).arg(nIndex));
    if (pCurState) {
        DebugLog(QString("curState:%1").arg(pCurState->GetStateId()));
        pCurState->ProcessCardMachineEvent(nEvent, nPos, nIndex);
    }
}

void QDlgMain::OnHelpBtnEvent(int nPos)
{
    QString sPos = 1 == nPos ? QString("上求助按钮") : QString("下求助按钮");
    ShowLog(sPos);
    if (Ptr_Info->bHaveCardMgr()) {
        //上求助是1， 下求助是0
        int devSn = nPos == 1 ? 2 : 1;
        CDeviceFactory::GetAutoExTollScreen()->StartVideoChat(Ptr_Info->GetGBLaneId(),
                                                              Ptr_Info->GetLaneId(), devSn);
    }

    RemoteMsgMgr::GetSingleInst()->SendCallVideoReq();
}

void QDlgMain::InitRsuProcessor()
{
    for (int i = 0; i < 2; ++i) {
        CRsuDev *pDev = CDeviceFactory::GetRsuDev(i);
        if (!pDev) continue;
        // if (0 == i) continue;
        m_pProcessorThreads[i] = new QThread();
        CRsuDataProcessor *pProcessor = new CRsuDataProcessor();

        if (!QObject::connect(pDev, SIGNAL(NotifyRsuEvent(int, quint32, qint32, quint8, quint8)),
                              pProcessor, SLOT(OnRsuEvent(int, quint32, qint32, quint8, quint8)))) {
            ErrorLog("Rsu event 信号槽连接失败1;");
        }
        /*
        if(!QObject::connect(pDev,SIGNAL(NotifyRsuError(quint8,quint8)),pProcessor,SLOT(OnRsuError(quint8,quint8)))){
            ErrorLog("OnRsuError 信号槽连接失败");
        }*/

        connect(m_pProcessorThreads[i], SIGNAL(finished()), pProcessor, SLOT(deleteLater()));
        connect(pProcessor, SIGNAL(NotifyPsamAuthEvent(int, int, int)), this,
                SLOT(OnPsamAuthEvent(int, int, int)));

        pProcessor->moveToThread(m_pProcessorThreads[i]);
        m_pProcessorThreads[i]->start();
    }
    return;
}

bool QDlgMain::InitMobilePay(QString &sError)
{
    CSpEventDev *pSpDev = CDeviceFactory::GetSpEventDev();
    if (pSpDev->IsExist()) {
        //参数文件里面配置了，并且动态库加载成功
        QString sRoad = QString::number(Ptr_Info->GetBL_SubCenter());
        QString sRoadName = Ptr_Info->GetRoadName();
        QString sAreaCode = QString("3601#%1").arg(sRoad);
        QString sStationInfo =
                QString("%1#%2").arg(Ptr_Info->GetStationID()).arg(Ptr_Info->GetStationName());
        quint32 nThreadID = (quint32)QThread::currentThreadId();
        HWND hWnd = this->winId();
        bool bRlt = pSpDev->InitEnViromentWnd(hWnd, TWSM_NOTIFY_MSGID, sAreaCode, sStationInfo,
                                              Ptr_Info->GetLaneId(), 36);
        if (!bRlt) {
            sError = QString("壁挂扫码设备初始化失败");
            DebugLog(sError);
            return false;
        }
    }
    //

    return true;
}

void QDlgMain::InitVehicleOutlineDevStatus()
{
    // 检查车辆外轮廓设备配置是否启用
    QString szDriver, szConnStr1, szConnStr2;
    quint8 bStd;
    int nType, bUsed;
    GetDevCfg(QString("VehicleOutline"), szDriver, szConnStr1, szConnStr2, bStd, nType, bUsed);
    if (!bUsed || szDriver.isEmpty()) {
        m_pFrmWeight->SetVehicleOutlineDevStatus(0);
        DebugLog("车辆外轮廓尺寸检测仪未启用");
        return;
    }
    // 检查车辆外轮廓是否存在并设置状态
    CVehicleOutlineDev* pVehicleOutlineDev = CDeviceFactory::GetVehicleOutlineDev();
    if(!pVehicleOutlineDev){
        // 设备不存在
        m_pFrmWeight->SetVehicleOutlineDevStatus(0);
        DebugLog("车辆外轮廓尺寸检测仪未初始化");
        return;
    }
    // 检查设备是否正常工作
    bool bDeviceOK = pVehicleOutlineDev->CheckDeviceStatus();
    if(bDeviceOK){
        m_pFrmWeight->SetVehicleOutlineDevStatus(1);
        DebugLog("车辆外轮廓尺寸检测仪正常工作");
    }else{
        m_pFrmWeight->SetVehicleOutlineDevStatus(2);
        DebugLog("车辆外轮廓尺寸检测仪工作异常");
    }
}
void QDlgMain::OnPrintLog()
{
    m_PrintLogTimer.stop();
    QTime time = QTime::currentTime();
    QString str = time.toString("hhmmsszzz");
    CSYSInfo::PrintCpuUsge(str);
    CSYSInfo::PrintProcInfo(str);
    CSYSInfo::PrintDIskInfo(str);
    CSYSInfo::PrintMemInfo(str);
    // QString status(Ptr_ETCCtrl->GetDataMgr()->GetNetState()?"网络正常":"网络断开");
    // StdInfoLog(LogKey::OneKey_DevInfo, LogKey::DevInfo_NetWorkStatus, str, QString(), status);
    CStdLog::StdLogDevInfo_DevStatus(DEV_NETWORK, Ptr_ETCCtrl->GetDataMgr()->GetNetState() ? 0 : 1);
    CStdLog::StdLogDevInfo_LoopStatus();
    CCardReader *pCardReader = CDeviceFactory::GetCardReader(0);
    if (pCardReader) CStdLog::StdLogDevInfo_DevStatus(DEV_CardReader, pCardReader->GetStatus());

    CCardReader *pCardReader1 = CDeviceFactory::GetCardReader(1);
    if (pCardReader1) CStdLog::StdLogDevInfo_DevStatus(DEV_CardReader, pCardReader1->GetStatus());
    CCardReader *pCardReader2 = CDeviceFactory::GetCardReader(2);
    if (pCardReader2) CStdLog::StdLogDevInfo_DevStatus(DEV_CardReader, pCardReader2->GetStatus());

    StdInfoLog(LogKey::OneKey_SystemInfo, LogKey::SystemInfo_OS, str, QString(),
               Ptr_Info->GetWindowsVer());
    qint64 tmp = QDateTime::currentMSecsSinceEpoch();
    double totalHours = (tmp - m_lAppStartTime) / 3600000.00;
    QString sTotalHoues = QString("%1").arg(totalHours, 0, 'f', 2, QLatin1Char('0'));
    StdInfoLog(LogKey::OneKey_SystemInfo, LogKey::SystemInfo_SoftRunTime, str, QString(),
               sTotalHoues);
    // QString("%1小时%2分钟").arg((tmp-m_lAppStartTime)/1000/60/60)
    //.arg((tmp-m_lAppStartTime)/1000/60%60));
    m_PrintLogTimer.start(3600 * 1000);
}

void QDlgMain::OnShiftTimer()
{
    if (Ptr_ETCCtrl->GetLaneStatus() == lsUnlogin)  //下班状态检测班次到期
    {
        bool bChanged = Ptr_ETCCtrl->GetShiftMgr()->CheckAndSwitchShift(false);  //
        if (bChanged) {
            DebugLog("下班状态下检测到班次更新");
        }
    } else {
        DebugLog("上班状态下班次更新");
    }

    QString sBatch;
    sBatch = CBatchMgr::GetBatchMgr()->GetCurBatch();
    CTollGantryMgr::GetTollGantryMgr()->CheckBatchChanged_New(sBatch);
    return;
}

QDlgMain *GetMainDlg()
{
    static QDlgMain *dlgMain = new QDlgMain;
    return dlgMain;
}

void QDlgMain::ConnectAllDevSignal()
{
    if (!CDeviceFactory::ConnDevStateSignal(this, SLOT(OnDevStatusChange(qint32, qint32)))) {
    }

    //

    if (!QObject::connect(Ptr_ETCCtrl, SIGNAL(NotifyStayOutTime(QString, quint8)), this,
                          SLOT(OnStayOutTimeEvent(QString, quint8)))) {
        DebugLog("静态超时信号槽连接失败");
    }
    if (!QObject::connect(Ptr_ETCCtrl, SIGNAL(NotifyFrontVehQueChanged()),
                          &this->m_UpdateFronVehQueTimer, SLOT(start()))) {
        //    if(!QObject::connect(Ptr_ETCCtrl,SIGNAL(NotifyFrontVehQueChanged()),this,SLOT(OnUpdateTradeVehQueue()))){
        DebugLog("车辆前队列信号槽连接失败");
    }

    if (!QObject::connect(Ptr_ETCCtrl, SIGNAL(NotifyVehInvoice()), this,
                          SLOT(OnVehInvoiceEvent()))) {
        DebugLog("闯关事件信号槽连接失败");
    }

    //界面显示前费显显示的信息
    for (int i = 0; i < MAX_FAREDISPLAYER_NUM; ++i) {
        CFareDisplayer_GB *pFD = CDeviceFactory::GetETCFareDisPlayer(i);
        if (!pFD) continue;
        if (!QObject::connect(pFD, SIGNAL(NotifyShowText(QString, int)), this,
                              SLOT(OnFareDisplayerShowText(QString, int)))) {
            DebugLog("费显显示信号槽连接失败");
        }
    }

    // IO卡输入
    if (!this->connect(CDeviceFactory::GetIOCard(),
                       SIGNAL(NotifyInputChanged(quint32, quint32, quint8, QString)), this,
                       SLOT(OnIOInputChanged(quint32, quint32, quint8, QString)))) {
        ErrorLog(QString("IO卡输入状态信号槽连接失败"));
    }

    /*
    if(!this->connect(CDeviceFactory::GetIOCard(),SIGNAL(NotifyDIChanged(int,int,quint8,QString)),this,SLOT(OnDIStatusChanged(int,int,quint8,QString)))){
        ErrorLog(QString("IO卡输入状态信号槽连接失败"));
    }*/
    // IO卡输出
    if (!this->connect(CDeviceFactory::GetIOCard(), SIGNAL(NotifyDOChanged(int, int)), this,
                       SLOT(OnDOStatusChanged(int, int)))) {
        ErrorLog(QString("IO卡输出状态信号槽连接失败"));
    }
    // 锁杆状态
    if (!this->connect(CDeviceFactory::GetIOCard(), SIGNAL(NotifyLockBar(bool)), this,
                       SLOT(OnLockBar(bool)))) {
        ErrorLog(QString("IO卡锁杆信号槽连接失败"));
    }
    // 车道状态信号
    if (!QObject::connect(Ptr_ETCCtrl, SIGNAL(NotifyLaneStatusChanged()), this,
                          SLOT(OnLaneStatusChanedEvent()))) {
        ErrorLog("连接车道状态信号-槽失败");
    }

    if (!QObject::connect(Ptr_ETCCtrl, SIGNAL(NotifyTransCompleteETC(bool, int, CTransInfo *)),
                          this, SLOT(OnTransCompleteETC(bool, int, CTransInfo *)))) {
        ErrorLog("ETC交易完毕信号-槽连接失败");
    }

    //车牌、车型识别
    for (int i = 0; i < MAX_VPR_NUM; ++i) {
        CVPRDev *pVpr = CDeviceFactory::GetVPRDev(i);
        if (!pVpr) {
            DebugLog(QString("车牌识别%1为空").arg(i));
            continue;
        }
        if (!QObject::connect(pVpr, SIGNAL(NotifyVPRDev(CVPRResult *)), this,
                              SLOT(OnVPRDevEvent(CVPRResult *)))) {
            ErrorLog(QString("连接车牌识别%1识别结果信号槽失败").arg(i));
        } else {
            ErrorLog(QString("连接车牌识别%1识别结果信号槽成功").arg(i));
        }
    }
    if (!QObject::connect(CDeviceFactory::GetVCRDev(),
                          SIGNAL(OnVcrResultData(unsigned long, int, QString, qint32, int)), this,
                          SLOT(OnVcrResultData(unsigned long, int, QString, qint32, int)))) {
        ErrorLog(QString("连接车型识别 信号-槽连接失败"));
    }
    //抓拍
    if (!QObject::connect(CDeviceFactory::GetVideoCard(), SIGNAL(NotifyCaptureEvent(QString)), this,
                          SLOT(OnCaptureEvent(QString)))) {
        ErrorLog(QString("连接车辆抓拍信号-槽失败"));
    }

    for (int i = 0; i < 2; ++i) {
        CRsuDev *pDev = CDeviceFactory::GetRsuDev(i);
        if (!pDev) continue;
        /*
        if(!QObject::connect(pDev,SIGNAL(NotifyRsuEvent(quint32,qint32,quint8,quint8)),this,SLOT(OnRsuEvent(quint32,qint32,quint8,quint8)))){
            ErrorLog("Rsu event 信号槽连接失败1;");
        }*/

        if (!QObject::connect(pDev, SIGNAL(NotifyRsuError(int, quint8, quint8)), this,
                              SLOT(OnRsuError(int, quint8, quint8)))) {
            ErrorLog("OnRsuError 信号槽连接失败");
        }
        if (!QObject::connect(pDev, SIGNAL(NotifyRsuState(int, bool)), this,
                              SLOT(OnRsuState(int, bool)))) {
            ErrorLog(QString("更新RSU状态 信号-槽连接失败"));
        }
    }

    if (!QObject::connect(Ptr_ETCCtrl, SIGNAL(NotifyVehQueChanged()), this, SLOT(UpdateVehState()),
                          Qt::QueuedConnection)) {
        ErrorLog(QString("更新车道内车辆状态 信号-槽连接失败"));
    }

    //计重信息变化
    if (!connect(VehWeightInfo::GetVehWeightInfo(), SIGNAL(NotifyWeightDataChange()), this,
                 SLOT(NotifyWeightDataChange()))) {
        ErrorLog(QString("更新计重信息 信号-槽连接失败"));
    } else {
        // TODO for debug
#ifdef QT_DEBUG
        CVehAxisInfo VehAxisInfo;

        VehAxisInfo.AddRawAxis(1, 1000, 12);
        VehAxisInfo.AddRawAxis(1, 3730, 12);
        VehWeightInfo::GetVehWeightInfo()->AddVeh(VehAxisInfo);

        VehAxisInfo.AddRawAxis(1, 13700, 12);
        VehAxisInfo.AddRawAxis(1, 15700, 12);
        VehAxisInfo.AddRawAxis(4, 7700, 12);
        VehWeightInfo::GetVehWeightInfo()->AddVeh(VehAxisInfo);
        VehAxisInfo.Clear();

        VehAxisInfo.AddRawAxis(1, 700, 12);
        VehAxisInfo.AddRawAxis(2, 900, 12);
        VehWeightInfo::GetVehWeightInfo()->AddVeh(VehAxisInfo);
#endif
    }

    if (1) {
        if (!QObject::connect(&m_CheckShiftTimer, SIGNAL(timeout()), this, SLOT(OnShiftTimer()))) {
            ErrorLog(QString("更新班次 信号-槽连接失败"));
        }
    }

    QObject::connect(&m_PrintLogTimer, SIGNAL(timeout()), this, SLOT(OnPrintLog()));

    //连接常规任务定时器
    QObject::connect(&m_Routine10SecTimer, SIGNAL(timeout()), this, SLOT(OnRoutine10SecTime()));
}

void QDlgMain::DisConnectDevSignal()
{
    CDeviceFactory::DisConnDevStateSignal(this, SLOT(OnDevStatusChange(qint32, qint32)));
    //费显更新显示

    // QObject::disconnect(CDeviceFactory::GetETCFareDisPlayer(DevIndex_First),SIGNAL(NotifyShowText(QString,int)),this,SLOT(OnFareDisplayerShowText(QString,int)));

    QObject::disconnect(Ptr_ETCCtrl, SIGNAL(NotifyVehInvoice()), this, SLOT(OnVehInvoiceEvent()));

    // QObject::disconnect(Ptr_ETCCtrl,SIGNAL(NotifyFrontVehQueChanged()),this,SLOT(RefreshNewVehPic()));

    QObject::disconnect(Ptr_ETCCtrl, SIGNAL(NotifyFrontVehQueChanged()),
                        &this->m_UpdateFronVehQueTimer, SLOT(start()));
    //    QObject::disconnect(Ptr_ETCCtrl,SIGNAL(NotifyFrontVehQueChanged()),this,SLOT(OnUpdateTradeVehQueue()));

    QObject::disconnect(Ptr_ETCCtrl, SIGNAL(NotifyStayOutTime(QString, quint8)), this,
                        SLOT(OnStayOutTimeEvent(QString, quint8)));
    CRsuDev *pRsuDev = NULL;
    for (int i = 0; i < 2; ++i) {
        pRsuDev = CDeviceFactory::GetRsuDev(i);
        if (!pRsuDev) continue;
        /*
        QObject::disconnect(pRsuDev, SIGNAL(NotifyRsuEvent(int, quint32, qint32, quint8,quint8)),
        this, SLOT(OnRsuEvent(int,quint32, qint32, quint8, quint8))); QObject::disconnect(pRsuDev,
        SIGNAL(NotifyRsuError(int, quint8, quint8)), this, SLOT(OnRsuError(int, quint8, quint8)));
                            */
    }
    QObject::disconnect(VehWeightInfo::GetVehWeightInfo(), SIGNAL(NotifyWeightDataChange()), this,
                        SLOT(NotifyWeightDataChange()));
    QObject::disconnect(CDeviceFactory::GetVCRDev(),
                        SIGNAL(OnVcrResultData(unsigned long, int, QString, qint32, int)), this,
                        SLOT(OnVcrResultData(unsigned long, int, QString, qint32, int)));
    
    // 断开VPR设备的信号槽连接
    for (int i = 0; i < MAX_VPR_NUM; ++i) {
        CVPRDev *pVpr = CDeviceFactory::GetVPRDev(i);
        if (pVpr) {
            DebugLog(QString("断开车牌识别%1的信号槽连接").arg(i));
            QObject::disconnect(pVpr, SIGNAL(NotifyVPRDev(CVPRResult *)), this,
                              SLOT(OnVPRDevEvent(CVPRResult *)));
        }
    }
    
    QObject::disconnect(Ptr_ETCCtrl, SIGNAL(NotifyLaneStatusChanged()), this,
                        SLOT(OnLaneStatusChanedEvent()));
    QObject::disconnect(Ptr_ETCCtrl, SIGNAL(NotifyLaneStatusChanged()), this,
                        SLOT(OnLaneStatusChanedEvent()));
    //  QObject::disconnect(Ptr_ETCCtrl,SIGNAL(NotifyTransComplete(bool,int)),this,SLOT(OnTransComplete(bool,int)));
    QObject::disconnect(Ptr_ETCCtrl, SIGNAL(NotifyTransCompleteETC(bool, int, CTransInfo *)), this,
                        SLOT(OnTransCompleteETC(bool, int, CTransInfo *)));
    QObject::disconnect(Ptr_ETCCtrl->GetShiftMgr(),
                        SIGNAL(NotifyWorkShiftChanged(CShiftSumInfo, CShiftSumInfo, bool)), this,
                        SLOT(OnWorkShiftChangedEvent(CShiftSumInfo, CShiftSumInfo, bool)));
    QObject::disconnect(Ptr_ETCCtrl->GetShiftMgr(), SIGNAL(NotifyShiftEnd(CShiftSumInfo, bool)),
                        this, SLOT(NotifyShiftEnd(CShiftSumInfo, bool)));

    QObject::disconnect(CDeviceFactory::GetIOCard(),
                        SIGNAL(NotifyInputChanged(quint32, quint32, quint8, QString)), this,
                        SLOT(OnIOInputChanged(quint32, quint32, quint8, QString)));
    QObject::disconnect(CDeviceFactory::GetIOCard(), SIGNAL(NotifyDOChanged(int, int)), this,
                        SLOT(OnDOStatusChanged(int, int)));
    QObject::disconnect(CDeviceFactory::GetIOCard(), SIGNAL(NotifyLockBar(bool)), this,
                        SLOT(OnLockBar(bool)));
    QObject::disconnect(CDeviceFactory::GetVideoCard(), SIGNAL(NotifyCaptureEvent(QString)), this,
                        SLOT(OnCaptureEvent(QString)));
    QObject::disconnect(Ptr_ETCCtrl, SIGNAL(NotifyVehQueChanged()), this, SLOT(UpdateVehState()));
    QObject::disconnect(pRsuDev, SIGNAL(NotifyRsuState(int, bool)), this,
                        SLOT(OnRsuState(int, bool)));
    if (1) {
        QObject::disconnect(&m_CheckShiftTimer, SIGNAL(timeout()), this, SLOT(OnShiftTimer()));
    }
    QObject::disconnect(&m_HeadTimer, SIGNAL(timeout()), Ptr_ETCCtrl, SLOT(sendHeartbeat()));
    QObject::disconnect(&m_PrintLogTimer, SIGNAL(timeout()), this, SLOT(OnPrintLog()));

    //连接常规任务定时器
    QObject::disconnect(&m_Routine10SecTimer, SIGNAL(timeout()), this, SLOT(OnRoutine10SecTime()));
}

void QDlgMain::OnDevStatusChange(qint32 nDevId, qint32 nDevStatus)
{
    //更新界面显示
    m_pFrmDevStatus->Devstatus(nDevId, nDevStatus);
    //保存设备的状态
    Ptr_ETCCtrl->OnDevStatusChangedEvent(nDevId, nDevStatus);

    switch (nDevId) {
    case DEV_Weight:
        //计重设备的状态
        m_pFrmWeight->SetDevStatusNormal(nDevStatus == CAbstractDev::DEV_STATUS_OK);
        CStdLog::StdLogDevInfo_DevStatus(DEV_Weight, nDevStatus);
        break;
    case DEV_VehicleOutline:
        //车辆外轮廓尺寸检测仪的状态
        {
            int outlineStatus = 2; // 默认异常
            if(nDevStatus == CAbstractDev::DEV_STATUS_OK){
                outlineStatus = 1;
            }
            m_pFrmWeight->SetVehicleOutlineDevStatus(outlineStatus);
            CStdLog::StdLogDevInfo_DevStatus(DEV_VehicleOutline, nDevStatus);
        }
        break;
    default:
        break;
    }
    return;
}

/*只有过车队列发生变化才进行更新处理*/
void QDlgMain::UpdateVehState() { m_pFlashQueTimer->start(50); }

void QDlgMain::OnIOInputChanged(quint32 dwOldInput, quint32 dwNewInput, quint8 bLoopStatus,
                                QString sLoopStatus)
{
    if (!m_bAppInitOk) {
        //        DebugLog(QString("系统尚未启动完毕,暂不不处理IO信号"));
        return;
    }

    CIOCard *pIOCard = CDeviceFactory::GetIOCard();
    /*
    DebugLog(QString("接收到IO信号,Old:%1,New:%2,sStatus:%3")
                 .arg(dwOldInput)
                 .arg(dwNewInput)
                 .arg(sLoopStatus));
                 */
    if (!pIOCard) {
        return;
    }

    for (int i = DI_LoopTrig1; i <= DI_LoopBack; ++i) {
        bool bOldStatus = false, bNewStatus = false;
        bool bChanged =
                pIOCard->CheckInputPortStatus(dwOldInput, dwNewInput, i, bOldStatus, bNewStatus);
        if (bChanged) {
            // StdInfoLog(LogKey::OneKey_DevInfo,
            // LogKey::DevInfo_LoopStatus,QTime::currentTime().toString("hhmmsszzz"),
            // CDeviceFactory::GetIOCard()->GetDevId(),   sLoopStatus);
            OnDIStatusChanged(i, bNewStatus, bLoopStatus, sLoopStatus);
            ShowLog(QString("地感状态:%1").arg(sLoopStatus));
        }
    }
}

void QDlgMain::OnDIStatusChanged(int nDI, bool bStatus, quint8 bLoopStatus, QString sLoopStatus)
{
    DebugLog(QString("设备%1,状态发生改变:%2").arg(nDI).arg(bStatus ? 1 : 0));

    if (nDI >= DI_LoopTrig1 && nDI <= DI_LoopBack) {
        CStdLog::StdLogDevInfo_Loop(nDI, bStatus);
        if (Ptr_Info->bHaveFrontDev()) {
            CRsuDev *pRsuDev = CDeviceFactory::GetRsuDev(DevIndex_First);
            if (DI_LoopExist != nDI) {
                if (pRsuDev) pRsuDev->SendLoopStatus(bLoopStatus);
            }
        }

        if (DI_LoopExist == nDI || DI_LoopBack == nDI) {
            RemoteMsgMgr::GetSingleInst()->SendLoopStatusEventReq(nDI, bStatus ? 1 : 0);
        }
    }

    if (nDI == DI_LoopExist) {
        if (bStatus) {
            CVideoCard *pVideoCard = CDeviceFactory::GetVideoCard();
            if (pVideoCard && Ptr_Info->GetCapture()) pVideoCard->NotifyToCapture();
        }
    }
    //更新界面显示
    m_pFrmDevStatus->SetDIDevStatus(nDI, bStatus);

    if (!Ptr_Info->bHaveFrontDev()) {
        if (DI_LoopFront == nDI || DI_LoopDetect == nDI) {
            return;
        }
    }

    CAbstractState *pcurState = CAbstractState::GetCurState();
    QString sError;
    if (pcurState) {
        if (!pcurState->ProcessDIEvent(nDI, bStatus, sError)) {
            return;
        }
    }
    if (Ptr_ETCCtrl->OnDIChangeEvent(nDI, bStatus, sLoopStatus)) {
    }
    return;

    if (nDI == DI_LoopBack) {
        QMutexLocker locker(&m_StayOutMt);
        if (m_bStayOut) {
            m_pStayOutTimeDlg->OnClickedCancel();
            m_bStayOut = false;
        }
    }
}

void QDlgMain::OnLockBar(bool bLockBar)
{
    //更新界面显示
    m_pFrmDevStatus->SetBarLock(bLockBar);
}

void QDlgMain::OnDOStatusChanged(int nDO, int bStatus)
{
    if (!m_bAppInitOk) {
        DebugLog(QString("系统尚未启动完毕,暂不处理输出端口[%1]信号").arg(nDO));
        return;
    }

    //更新界面显示
    m_pFrmDevStatus->SetDODevStatus(nDO, bStatus);

    Ptr_ETCCtrl->OnDOChangeEvent(nDO, bStatus);

    CAbstractState *pcurState = CAbstractState::GetCurState();
    QString sError;
    if (pcurState && pcurState->PorcessDOEvent(nDO, bStatus, sError)) {
    }
}

void QDlgMain::OnNetWorkChanged(qint32 nStatus)
{
    if (0 == nStatus) {
        m_pFrmParamVer->ShowNetState("正常");
    } else {
        m_pFrmParamVer->ShowNetState("断开");
    }
    OnDevStatusChange(DEV_NETWORK, nStatus);
}

void QDlgMain::ShowETCVPRPic(QString &spic) { m_pFrmVehImage->ShowVehpic(spic); }

void QDlgMain::OnVPRDevEvent(CVPRResult *pRlt)
{
    if (!pRlt) {
        DebugLog(tr("接收到空的车牌识别结果"));
        return;
    }
    //显示在界面上
    CVehInfo autovehInfo;
    autovehInfo.Clear();
    //自动识别车牌
    qstrncpy(autovehInfo.szAutoVehPlate, pRlt->szVehPlate, sizeof autovehInfo.szAutoVehPlate);

    // RemovePlateSpecChar(autovehInfo.szAutoVehPlate,sizeof
    // autovehInfo.szAutoVehPlate,pRlt->szVehPlate);
    //自动识别车牌颜色
    autovehInfo.nAutoVehPlateColor = pRlt->nColor;
    if (9 == autovehInfo.nAutoVehPlateColor) autovehInfo.nAutoVehPlateColor = 0;

    QString sAutoPlate = GB2312toUnicode(autovehInfo.szAutoVehPlate);
    DebugLog(tr("接收到车牌识别结果:%1,颜色%2,index:%3,bigImgSize:%4,devIndex:%5")
             .arg(sAutoPlate)
             .arg(pRlt->nColor)
             .arg(pRlt->nIndex)
             .arg(pRlt->nBigImgSize)
             .arg(pRlt->nDevIndex));

    if (Ptr_ETCCtrl->IsTheLastVeh(sAutoPlate, pRlt->nColor)) {
        DebugLog(QString("车牌识别结果%1 已经发送流水,不予保存").arg(sAutoPlate));
        return;
    }

    if (sAutoPlate.size() > 0) {
        if (Ptr_Info->bUseVehLib()) {
            CVehTypeLibMgr::GetVehTypeLibMgr()->AsyncGetVehResult(sAutoPlate,
                                                            autovehInfo.nAutoVehPlateColor);
        }
        
        // 添加车牌信息到ETCPrompt进行高频次现金通行查询
        ETCPrompt::GetETCPrompt()->AddVehInfo(sAutoPlate, pRlt->nColor);
    }

    //显示车牌
    m_pFrmVehInfo->ShowAutoVehPlate((VP_COLOR)pRlt->nColor, sAutoPlate);
    //获取车型
    VcrResult *pVcrResult = NULL;
    VcrResult vcrResult;
    CVehClass nVehClass = VC_None;

    VCRDev *pVcrDev = CDeviceFactory::GetVCRDev();
    if (pVcrDev) {
        bool bHaveVcr = pVcrDev->GetVcrResult(pRlt->nColor, sAutoPlate, vcrResult);
        if (bHaveVcr) {
            nVehClass = (CVehClass)vcrResult.vehclass;
            if (pVcrDev->bSendVideo()) {  //入口车道传三张图片和视频
                if (vcrResult.sVideoFileName.isEmpty())
                    vcrResult.sVideoFileName = pVcrDev->GetVideoFileName(vcrResult.dwCarID);
            }
            pVcrResult = &vcrResult;
        }
        DebugLog(QString("显示自动识别车型%1").arg((int)nVehClass));
        m_pFrmVehInfo->ShowAutoVehClass(nVehClass);
    }

    RemoteMsgMgr::GetSingleInst()->SendLaneReporVPRReq(pRlt, nVehClass);

    /*
    if(Ptr_ETCCtrl->bReETCAutoCap(pRlt->nDevIndex,sAutoPlate)){
        DebugLog(tr("接收重复的车牌识别结果"));
        return;
    }*/

    QString str = QTime::currentTime().toString("hhmmsszzz");
    QString sColorName = GetVehPlateColorName(pRlt->nColor);
    if (sColorName.isEmpty()) sColorName = QString("未确定");
    QString sFullPlate = sColorName + sAutoPlate;
    CVPRDev *pVpr = CDeviceFactory::GetVPRDev(pRlt->nDevIndex);
    if (pVpr)
        StdInfoLog(LogKey::OneKey_DevInfo, LogKey::DevInfo_RegPlate, str, pVpr->GetDevId(),
                   sFullPlate);

    QString sBigImgFile;
    QString sRelativeFileName;
    Ptr_ETCCtrl->SaveVLPImg(*pRlt, sBigImgFile, sRelativeFileName);

    if (pRlt->nDevIndex == DevIndex_Second) {
        CAbstractState::SetAutoVehPlate(sAutoPlate, pRlt->nColor);
        CAbstractState::SetAutoVehClass(nVehClass);
    }

    //远控端更新车辆识别信息
    Ptr_RemoteCtrl->RefreshVehInput();

    CAutoRegInfo autoRegInfo;
    if (Ptr_ETCCtrl->SetETCAutoVehPlate(pRlt->nDevIndex, autovehInfo, sBigImgFile,
                                        sRelativeFileName, autoRegInfo, pVcrResult)) {
        //汇总数应该与上送的流水报文对应
        QMutexLocker locker(&m_VLPRNumMutex);
        if (sAutoPlate.length() > 0) {
            m_VehicleDataCount++;
            m_VehiclePicCount++;
            Ptr_Info->SetVlprSmInfo(m_sVLPRHourBatchNo, m_VehicleDataCount, m_VehiclePicCount);
        }
    }
    m_lstShowImgs.clear();
    if (pRlt->HasSmallImg()) {
        m_lstShowImgs.append(pRlt->sSmallName);
    }
    if (pRlt->HasBigImg()) {
        m_lstShowImgs.append(sBigImgFile);
    }
    QString ShowImgFileName = sBigImgFile;
    if (pRlt->HasSmallImg()) {
        ShowImgFileName = pRlt->sSmallName;
    }
    m_pFrmVehImage->ShowVehpic(ShowImgFileName);
    DebugLog(QString("车牌识别处理完毕，sBigFileName：%1").arg(sBigImgFile));
    if (pVpr)
        StdInfoLog(LogKey::OneKey_DevInfo, LogKey::DevInfo_Capture, str, pVpr->GetDevId(),
                   sBigImgFile);
    // 调用CETCLaneCtrl的方法来停止卡机定时器
    if (Ptr_Info && Ptr_Info->GetCardMgrType() == 31)
        Ptr_ETCCtrl->StartVehInputCardMgrTimer();
    return;
}

void QDlgMain::OnCaptureEvent(QString sFileName)
{
    Q_UNUSED(sFileName)
    // DebugLog(QString("抓拍图像[%1]保存成功").arg(sFileName));
    // Ptr_Ctrl->SetCapImageName(sFileName);
    //  QString sPath=GetCurrentPath()+"capTmp.bmp";
    //  DebugLog(sPath);
    //  m_vehPic->showVehpic(sPath);
    //  DebugLog(QString("抓拍图像[%1]显示成功").arg(sPath));
}

void QDlgMain::OnSaveDataFailedEvent(int nMsgType, quint32 occurTime, QString sError)
{
    QDateTime dtOccurTime;
    CardTime2QDateTime(occurTime, dtOccurTime);
    QString log = QString("%1报文[%2] 保存失败,Error:%3")
            .arg(nMsgType)
            .arg(dtOccurTime.toString("yyyy-MM-dd hh:mm:ss"))
            .arg(sError);
    ErrorLog(log);
    CAbstractState::OnDataSaveFailedEvent();
    ShowPromptMsg("数据保存失败,请联系维护人员");
    return;
}

void QDlgMain::OnTransCompleteETC(bool bRedo, int nViolateId, CTransInfo *pLastTranInfo)
{
    if (!pLastTranInfo) {
        return;
    }
    CTransInfo transInfo = *pLastTranInfo;
    CTransInfo *pTranInfo = &transInfo;
    // DebugLog(QString("OnTransCompleteETC0"));
    CWasteInfo wasteInfo;
    if (!bRedo && nViolateId == ViolateT_None) {
        m_pFrmVehImage->ClearPic();
        m_lstShowImgs.clear();
    }

    if (ViolateT_FalseAlarm == nViolateId)  //闯关误报警车辆不显示流水
        return;

    wasteInfo.bExit = Ptr_Info->IsExitLane();
    wasteInfo.dtWasteTime = QDateTime::currentDateTime();
    CVehInfo vehInfo = pTranInfo->VehInfo;
    wasteInfo.sVehPlate = GB2312toUnicode(vehInfo.szVehPlate);
    wasteInfo.vehClass = vehInfo.VehClass;
    wasteInfo.vehType = vehInfo.GBVehType;
    wasteInfo.nVLPC = vehInfo.nVehPlateColor;

    if (1) {
        wasteInfo.bType = pTranInfo->transResult >= Tr_Successed ? 1 : 0;
        if (wasteInfo.bType == 1) {
            if (pTranInfo->m_bReprint) {
                wasteInfo.bType = 3;
            }
        }
        wasteInfo.sVehPlate = GB2312toUnicode(pTranInfo->VehInfo.szVehPlate);

        wasteInfo.vehClass = CVehClass(pTranInfo->VehInfo.VehClass);
        if (0 == wasteInfo.bType)
            wasteInfo.sDesc = pTranInfo->GetTransTypeDesc();
        else if (pTranInfo->IsWhiteListVeh()) {
            wasteInfo.sDesc = QString("公务车");
        }
    }
    if (pTranInfo->IccInfo.ProCardBasicInfo.bType == CARD_TYPE_TALLY_CARD) {
        wasteInfo.bTallyCard = true;

    } else if (pTranInfo->IccInfo.ProCardBasicInfo.bType == CARD_TYPE_STORE_CARD) {
        wasteInfo.bIsPayByStoreCard = true;
    }

    if (pTranInfo->mediaType == MediaType_OBU) {
        wasteInfo.sCardId = QString::fromAscii(pTranInfo->IccInfo.ProCardBasicInfo.szCardNo);
    } else if (pTranInfo->mediaType == MediaType_CPC) {
        wasteInfo.sCardId = QString::fromAscii(pTranInfo->cpcIccInfo.cpcBasicInfo.sCardID);
    }
    wasteInfo.dwLastMoney = 0;

    if (nViolateId == ViolateT_Violate)  //闯关
    {
        if (wasteInfo.sVehPlate.isEmpty()) {
            wasteInfo.sVehPlate = QString("闯关车");
        }
    }

    if (Ptr_Info->IsExitLane()) {
        wasteInfo.sEnStationName = pTranInfo->vehEntryInfo.sEnStaionName;
    }
    if (Ptr_Info->IsEntryLane()) {
        qint32 dwLastMoney, dwFeeMoney;
        pTranInfo->GetLastMoney_Entry(dwLastMoney, dwFeeMoney);
        wasteInfo.dwLastMoney = dwLastMoney / 100.00;
        wasteInfo.dwConsumeMoney = pTranInfo->GetConsumeMoney() / 100.00;
    } else {
        quint32 dwLastMoney, dwConsumeMoney;
        dwLastMoney = pTranInfo->GetLastMoney_Exit(dwConsumeMoney);
        wasteInfo.dwLastMoney =
                dwLastMoney / 100.00;  // pTranInfo->GetPayMoney(Ptr_Info->IsExitLane()))/100.00;
        wasteInfo.dwConsumeMoney = dwConsumeMoney / 100.00;

        switch (pTranInfo->m_transPayType) {
        case TransPT_OBU:
            wasteInfo.sTransPayType = QString("ETC");
            break;
        case TransPT_ETCCard:
            wasteInfo.sTransPayType = QString("ETC刷卡");
            break;
        case TransPT_Cash:
            wasteInfo.sTransPayType = QString("现金");
            break;
        case TransPT_Other:
            wasteInfo.sTransPayType = QString("第三方");
            break;
        case TransPT_Union:
            wasteInfo.sTransPayType = QString("银联");
            break;
        case TransPT_AliPay:
            wasteInfo.sTransPayType = QString("支付宝");
            break;
        case TransPT_WeChat:
            wasteInfo.sTransPayType = QString("微信");
            break;
        default:
            wasteInfo.sTransPayType.clear();
        }
    }
    if (pTranInfo->transResult == Tr_Successed)
        wasteInfo.dwBalance = (pTranInfo->ConsumeInfo.dwBalanceAfter);
    else
        wasteInfo.dwBalance = pTranInfo->IccInfo.dwBalance;

    wasteInfo.dwToTalWeight = pTranInfo->m_dwToTalWeight;
    wasteInfo.dwWeightLimit = pTranInfo->m_dwWeightLimit;
    if (pTranInfo->mediaType == MediaType_OBU)
        if (pTranInfo->m_nRsuIndex >= 0 && pTranInfo->m_nRsuIndex < 2)
            m_pFrmEtcInfo[pTranInfo->m_nRsuIndex]->ShowTransInfo(wasteInfo);

    ShowWasteInfo(&wasteInfo);
}

void QDlgMain::OnRsuState(int nIndex, bool isOK)
{
    //更新界面上天线的状态显示
    int nDevId = 0 == nIndex ? DEV_RSU : DEV_RSU1;
    if (isOK) {
        m_pFrmDevStatus->Devstatus(nDevId, 0);
    } else {
        m_pFrmDevStatus->Devstatus(nDevId, 1);
    }

    CRsuDev *pRsuDev = CDeviceFactory::GetRsuDev(nIndex);
    if (!pRsuDev) return;
    Ptr_ETCCtrl->OnDevStatusChangedEvent(nDevId, isOK ? 0 : 1);
    if (isOK) {
        if (pRsuDev) {
            const CRsuBaseInfo_Jx *pRsuBaseInfo = pRsuDev->GetRsuBaseInfo();
            QString str = QTime::currentTime().toString("hhmmsszzz");
            StdInfoLog(LogKey::OneKey_RSUInfo, LogKey::RSUInfo_RSUID, str, str,
                       Raw2HexStr(pRsuBaseInfo->rsuId, sizeof pRsuBaseInfo->rsuId));
            StdInfoLog(LogKey::OneKey_RSUInfo, LogKey::RSUInfo_RSUStatus, str, str,
                       QString("%1").arg(pRsuBaseInfo->bRsuStatus));
            StdInfoLog(LogKey::OneKey_RSUInfo, LogKey::RSUInfo_PSAMNum, str, str,
                       QString("%1").arg(pRsuBaseInfo->bPsamNum));
            StdInfoLog(LogKey::OneKey_RSUInfo, LogKey::RSUInfo_RSUVendor, str, str,
                       QString("%1").arg(pRsuBaseInfo->rsuManulId));

            for (int i = 0; i < pRsuBaseInfo->bPsamNum; ++i) {
                QString sPsam = Raw2HexStr(pRsuBaseInfo->PsamInfo[i].TerminateCode, 6);
                ShowLog(QString("天线[%1]终端机[%2]版本:%3编号:%4")
                        .arg(nIndex)
                        .arg(i)
                        .arg(pRsuBaseInfo->PsamInfo[i].bVersion)
                        .arg(sPsam));
                StdInfoLog(LogKey::OneKey_RSUInfo, LogKey::RSUInfo_TerminalID1, str, str, sPsam);
                StdInfoLog(LogKey::OneKey_RSUInfo, LogKey::RSUInfo_PSamVersion1, str, str,
                           QString("%1").arg(pRsuBaseInfo->PsamInfo[i].bVersion));
                StdInfoLog(LogKey::OneKey_RSUInfo, LogKey::RSUInfo_RSUAlgId1, str, str,
                           QString("%1").arg(pRsuBaseInfo->bRsuAlgId));
            }
            QString sVer = Raw2HexStr(pRsuBaseInfo->rsuVersion, sizeof pRsuBaseInfo->rsuVersion);
            QString sRsuInfo = QString("天线厂家Id:%1,天线版本号:%2,授权状态:%3")
                    .arg(pRsuBaseInfo->rsuManulId)
                    .arg(sVer)
                    .arg(pRsuBaseInfo->authorization);

            ShowLog(sRsuInfo);
            StdInfoLog(LogKey::OneKey_RSUInfo, LogKey::RSUInfo_RSUVersion, str, str, sVer);
            StdInfoLog(LogKey::OneKey_RSUInfo, LogKey::RSUInfo_RSUHardWareVersion, str, str,
                       QString("0"));
            if (!pRsuDev->IsPsamNeedAuth()) {
                pRsuDev->OpenRsu(1);
            } else {
            }
        }
    }

    CFareDisplayer_GB *pFD = CDeviceFactory::GetETCFareDisPlayer(nIndex);
    if (false) {
        if (pFD) {
            pFD->ClearAll();
            pFD->ShowWelcomeInfo(isOK);
        }
    } else {
        if (pFD) {
            pFD->ClearAll();
            pFD->ShowWelcomeInfo(false);
        }
    }
}

bool QDlgMain::CheckSer(int nOrgID, int nLaneID)
{
    QString sError = "";
    if (!ser->LoadAdapterInfo(sError)) {
        ErrorLog("注册信息:注册码加载本机硬件信息出错" + sError);
        return false;
    }
#define Run_Key "HKEY_LOCAL_MACHINE\\SOFTWARE\\AutoLane\\Key"
    QSettings *pReg = new QSettings(Run_Key, QSettings::NativeFormat);
    QVariant vt = pReg->value("SerNum");
    if (vt.isNull()) {
        return false;
    } else {
        QString sSerNum = vt.toString();
        int nOrgCode = nOrgID * 100 + nLaneID;
        if (!ser->CheckSeriNo(nOrgCode, sSerNum.toLatin1().data(), sSerNum.length(), sError)) {
            ErrorLog("注册信息:注册码验证失败请重新验证");
            return false;
        }
    }
    return true;
}

//提示班次结束
void QDlgMain::NotifyShiftEnd(CShiftSumInfo ShiftSumInfo, bool bAutoLogin)
{
    if (bAutoLogin) {
        //下班
        Ptr_ETCCtrl->UnLogin(false);
    }
    //此处更新班次信息
    CShiftSumInfo lastSumInfo;
    if (Ptr_ETCCtrl->EndCurShiftInfo(lastSumInfo)) {
        //更新班次信息
        DisplayLastShiftSumInfo();
    }
}

//发送更新ETC交易的相关消息（多线程更新界面需要）
void QDlgMain::Emit_ETCShowOBUInfo(int nIndex, const QString &sOBUSn)
{
    QList<QVariant> Params;
    emit NotifyETCDisplayEvent(nIndex, ETCDisplayType_Clear, Params);
    QVariant varOBU = QVariant(sOBUSn);
    Params.push_back(varOBU);
    emit NotifyETCDisplayEvent(nIndex, ETCDisplayType_ShowOBU, Params);
    return;
}

void QDlgMain::Emit_ETCShowVehInfo(int nIndex, const CVehInfo &vehInfo)
{
    QList<QVariant> Params;
    QVariant varVehInfo;
    varVehInfo.setValue(vehInfo);
    Params.push_back(varVehInfo);
    emit NotifyETCDisplayEvent(nIndex, ETCDisplayType_SetVehInfo, Params);
}

// 发送交易信息
void QDlgMain::Emit_ETCShowTransInfo(int nIndex, const CWasteInfo &wasteInfo)
{
    QList<QVariant> Params;
    QVariant varWasteInfo;
    varWasteInfo.setValue(wasteInfo);
    Params.push_back(varWasteInfo);
    emit NotifyETCDisplayEvent(nIndex, ETCDisplayType_ShowTransInfo, Params);
}

void QDlgMain::Emit_ETCShowCardInfo(int nIndex, int nCardType, const QString &sCardId,
                                    quint32 dwBalance)
{
    QList<QVariant> Params;
    QVariant varCardType(nCardType);
    QVariant varCardId(sCardId);
    QVariant varBalance(dwBalance);
    Params.push_back(varCardType);
    Params.push_back(varCardId);
    Params.push_back(varBalance);

    emit NotifyETCDisplayEvent(nIndex, ETCDisplayType_ShowCardInfo, Params);
}

void QDlgMain::Emit_ETCShowEntryInfo(int nIndex, const QDateTime &EnTime,
                                     const QString &sEnStationName, const QString &sVLP,
                                     const QString &sHexId)
{
    QList<QVariant> Params;
    QVariant varTime = QVariant(EnTime);
    QVariant varStationName(sEnStationName);
    QVariant varVLP(sVLP);
    QVariant varHexId(sHexId);

    Params.push_back(varTime);
    Params.push_back(varStationName);
    Params.push_back(varVLP);
    Params.push_back(varHexId);
    emit NotifyETCDisplayEvent(nIndex, ETCDisplayType_ShowEntryInfo, Params);
}

void QDlgMain::Emit_ETCShowMoney(int nIndex, quint32 dwMoney)
{
    QList<QVariant> Params;
    QVariant varMoney(dwMoney);
    Params.push_back(varMoney);
    emit NotifyETCDisplayEvent(nIndex, ETCDisplayType_ShowMoney, Params);
}

//在ETC交易窗口显示提示信息
void QDlgMain::Emit_ETCShowPrompt(int nIndex, const QString &promptMsg)
{
    QList<QVariant> Params;
    QVariant varMsg(promptMsg);
    Params.push_back(varMsg);
    emit NotifyETCDisplayEvent(nIndex, ETCDisplayType_ShowPromptInfo, Params);
}

void QDlgMain::Emit_ClearVehInfoFrm(bool bClearTrans) { emit NotifyClearVehFrmEvent(bClearTrans); }

void QDlgMain::ShowImages(int nVlpColor, const QString &sPlate)
{
    QStringList lstFileNames;
    for (int i = 0; i < m_lstShowImgs.size(); i++) {
        if (FileExists(m_lstShowImgs[i])) {
            lstFileNames.append(m_lstShowImgs[i]);
        }
    }
    QString sFindPlate = "无车牌";
    if (sPlate.length() > 0) {
        //有车牌结果
        sFindPlate = sPlate;
    }
    VcrResult vcrResult;
    bool bHaveResult = CDeviceFactory::GetVCRDev()->GetVcrResult(nVlpColor, sPlate, vcrResult);
    if (bHaveResult) {
        for (int i = 0; i < vcrResult.iBigImgCount; i++) {
            if (FileExists(vcrResult.bigImage[i].sImgFile)) {
                lstFileNames.append(vcrResult.bigImage[i].sImgFile);
            }
        }
    }
    //得到可显示的文件
    for (int i = 0; i < lstFileNames.size(); i++) {
        DebugLog(QString("Show_%1:%2").arg(i).arg(lstFileNames[i]));
    }

    //    QStringList lstTest;
    //    lstTest.append("bigimage.jpg");
    //    lstTest.append("smallimage.jpg");

    FormShowImage dlgShow(this);
    dlgShow.ShowImages(lstFileNames);
}

void QDlgMain::SetQrCode(QrCodeInfo_Lane *pInfo)
{
    m_qrCodeInfo = *pInfo;
    emit NotifyShowQrCode();
}

void QDlgMain::ShowQRCode(QrCodeInfo_Lane *pInfo)
{
    if (m_pFrmQRCode) {
        if (pInfo)
            m_pFrmQRCode->ShowQRCode(*pInfo);
        else
            m_pFrmQRCode->ClearShow();
    }
}

void QDlgMain::ShowHistoryQrCodeImage(const QString &vehicleId, QDateTime exTime)
{
    if (m_pFrmQRCode) {
        m_pFrmQRCode->ShowHistoryQrCodeImage(vehicleId, exTime);
    }
}

void QDlgMain::ShowHistoryQrCodeImage(const QString &sRecord)
{
    QString sVehId = sRecord.left(sRecord.indexOf(","));
    QString exTimeStr = sRecord.mid(sRecord.indexOf(",") + 1);
    QDateTime exTime = QDateTime::fromString(exTimeStr, "yyyy-MM-ddThh:mm:ss");
    ShowHistoryQrCodeImage(sVehId, exTime);
}

void QDlgMain::RefreshDevStatus()
{
    if (m_pFrmDevStatus) m_pFrmDevStatus->RefreshDevStatus();
}

void QDlgMain::RefreshLaneInfo()
{
    if (m_pFrmLaneInfo){
        m_pFrmLaneInfo->SetSelfServiceMode(Ptr_Info->bCardMgrEnabled());
        m_pFrmLaneInfo->update();
    }
}
bool QDlgMain::ReinitializeRsu(int nRsuIndex, QString *sError)
{
    try {
        // 参数-1表示初始化所有天线
        if (nRsuIndex == -1) {
            // 初始化所有天线
            bool allSuccess = true;
            for (int i = 0; i < 2; ++i) {
                try {
                    CRsuDev *pDev = CDeviceFactory::GetRsuDev(i);
                    if (!pDev) continue;
                    
                    // 先检查天线设备状态
                    int nDevId = (i == 0) ? DEV_RSU : DEV_RSU1;
                    
                    // 先关闭原有线程
                    try {
                        if (m_pProcessorThreads[i] && m_pProcessorThreads[i]->isRunning()) {
                            m_pProcessorThreads[i]->quit();
                            if (!m_pProcessorThreads[i]->wait(1000)) {
                                // 如果等待超时，强制终止
                                m_pProcessorThreads[i]->terminate();
                                m_pProcessorThreads[i]->wait();
                            }
                            delete m_pProcessorThreads[i];
                            m_pProcessorThreads[i] = NULL;
                        }
                    }
                    catch (const std::exception& e) {
                        QString errorMsg = QString("天线%1关闭原有线程异常: %2").arg(i).arg(e.what());
                        ErrorLog(errorMsg);
                        if (sError) *sError = errorMsg;
                        allSuccess = false;
                        continue;
                    }
                    
                    // 重新创建处理线程
                    try {
                        m_pProcessorThreads[i] = new QThread();
                        if (!m_pProcessorThreads[i]) {
                            QString errorMsg = QString("天线%1创建新线程失败").arg(i);
                            ErrorLog(errorMsg);
                            if (sError) *sError = errorMsg;
                            allSuccess = false;
                            continue;
                        }
                        
                        CRsuDataProcessor *pProcessor = new CRsuDataProcessor();
                        if (!pProcessor) {
                            QString errorMsg = QString("天线%1创建数据处理器失败").arg(i);
                            ErrorLog(errorMsg);
                            if (sError) *sError = errorMsg;
                            delete m_pProcessorThreads[i];
                            m_pProcessorThreads[i] = NULL;
                            allSuccess = false;
                            continue;
                        }

                        if (!QObject::connect(pDev, SIGNAL(NotifyRsuEvent(int, quint32, qint32, quint8, quint8)),
                                              pProcessor, SLOT(OnRsuEvent(int, quint32, qint32, quint8, quint8)))) {
                            QString errorMsg = QString("天线%1信号槽连接失败").arg(i);
                            ErrorLog(errorMsg);
                            if (sError) *sError = errorMsg;
                            
                            // 更新天线状态为异常
                            m_pFrmDevStatus->Devstatus(nDevId, 1);
                            Ptr_ETCCtrl->OnDevStatusChangedEvent(nDevId, 1);
                            
                            delete pProcessor;
                            delete m_pProcessorThreads[i];
                            m_pProcessorThreads[i] = NULL;
                            allSuccess = false;
                            continue;
                        }

                        connect(m_pProcessorThreads[i], SIGNAL(finished()), pProcessor, SLOT(deleteLater()));
                        connect(pProcessor, SIGNAL(NotifyPsamAuthEvent(int, int, int)), this,
                                SLOT(OnPsamAuthEvent(int, int, int)));

                        pProcessor->moveToThread(m_pProcessorThreads[i]);
                        
                        // 启动线程
                        try {
                            m_pProcessorThreads[i]->start();
                            if (!m_pProcessorThreads[i]->isRunning()) {
                                QString errorMsg = QString("天线%1线程启动失败").arg(i);
                                ErrorLog(errorMsg);
                                if (sError) *sError = errorMsg;
                                delete pProcessor;
                                delete m_pProcessorThreads[i];
                                m_pProcessorThreads[i] = NULL;
                                allSuccess = false;
                                continue;
                            }
                        }
                        catch (const std::exception& e) {
                            QString errorMsg = QString("天线%1线程启动异常: %2").arg(i).arg(e.what());
                            ErrorLog(errorMsg);
                            if (sError) *sError = errorMsg;
                            delete pProcessor;
                            delete m_pProcessorThreads[i];
                            m_pProcessorThreads[i] = NULL;
                            allSuccess = false;
                            continue;
                        }
                    }
                    catch (const std::exception& e) {
                        QString errorMsg = QString("天线%1创建新线程异常: %2").arg(i).arg(e.what());
                        ErrorLog(errorMsg);
                        if (sError) *sError = errorMsg;
                        allSuccess = false;
                        continue;
                    }
                    
                    // 等待线程启动完成
                    SleeperThread::msleep(100);
                    
                    // 在线程启动后调用天线设备的ReInitRsu方法
                    bool isSuccess = false;
                    try {
                        isSuccess = pDev->ReInitRsu();
                    }
                    catch (const std::exception& e) {
                        QString errorMsg = QString("天线%1重新初始化异常: %2").arg(i).arg(e.what());
                        ErrorLog(errorMsg);
                        if (sError) *sError = errorMsg;
                        allSuccess = false;
                        continue;
                    }
                    
                    // 更新天线状态
                    if (isSuccess) {
                        // 成功状态
                        m_pFrmDevStatus->Devstatus(nDevId, 0);
                        Ptr_ETCCtrl->OnDevStatusChangedEvent(nDevId, 0);
                        
                        // 调用OnRsuState以进行完整的状态处理
                        OnRsuState(i, true);
                        
                        DebugLog(QString("天线%1重新初始化成功").arg(i));
                    } else {
                        // 失败状态
                        m_pFrmDevStatus->Devstatus(nDevId, 1);
                        Ptr_ETCCtrl->OnDevStatusChangedEvent(nDevId, 1);
                        
                        // 调用OnRsuState以进行完整的状态处理
                        OnRsuState(i, false);
                        
                        QString errorMsg = QString("天线%1重新初始化失败").arg(i);
                        ErrorLog(errorMsg);
                        if (sError) *sError = errorMsg;
                        allSuccess = false;
                    }
                }
                catch (const std::exception& e) {
                    QString errorMsg = QString("天线%1初始化过程发生未知异常: %2").arg(i).arg(e.what());
                    ErrorLog(errorMsg);
                    if (sError) *sError = errorMsg;
                    allSuccess = false;
                }
            }
            return allSuccess;
        } else {
            // 初始化指定天线
            if (nRsuIndex < 0 || nRsuIndex > 1) {
                if (sError) *sError = "天线索引无效";
                return false;
            }
            
            CRsuDev *pDev = CDeviceFactory::GetRsuDev(nRsuIndex);
            if (!pDev) {
                QString errorMsg = QString("天线%1设备不存在").arg(nRsuIndex);
                if (sError) *sError = errorMsg;
                return false;
            }
            
            // 先检查天线设备状态
            int nDevId = (nRsuIndex == 0) ? DEV_RSU : DEV_RSU1;
            
            // 先关闭原有线程
            try {
                if (m_pProcessorThreads[nRsuIndex] && m_pProcessorThreads[nRsuIndex]->isRunning()) {
                    m_pProcessorThreads[nRsuIndex]->quit();
                    if (!m_pProcessorThreads[nRsuIndex]->wait(1000)) {
                        // 如果等待超时，强制终止
                        m_pProcessorThreads[nRsuIndex]->terminate();
                        m_pProcessorThreads[nRsuIndex]->wait();
                    }
                    delete m_pProcessorThreads[nRsuIndex];
                    m_pProcessorThreads[nRsuIndex] = NULL;
                }
            }
            catch (const std::exception& e) {
                QString errorMsg = QString("天线%1关闭原有线程异常: %2").arg(nRsuIndex).arg(e.what());
                ErrorLog(errorMsg);
                if (sError) *sError = errorMsg;
                return false;
            }
            
            // 重新创建处理线程
            try {
                m_pProcessorThreads[nRsuIndex] = new QThread();
                if (!m_pProcessorThreads[nRsuIndex]) {
                    QString errorMsg = QString("天线%1创建新线程失败").arg(nRsuIndex);
                    ErrorLog(errorMsg);
                    if (sError) *sError = errorMsg;
                    return false;
                }
                
                CRsuDataProcessor *pProcessor = new CRsuDataProcessor();
                if (!pProcessor) {
                    QString errorMsg = QString("天线%1创建数据处理器失败").arg(nRsuIndex);
                    ErrorLog(errorMsg);
                    if (sError) *sError = errorMsg;
                    delete m_pProcessorThreads[nRsuIndex];
                    m_pProcessorThreads[nRsuIndex] = NULL;
                    return false;
                }

                if (!QObject::connect(pDev, SIGNAL(NotifyRsuEvent(int, quint32, qint32, quint8, quint8)),
                                      pProcessor, SLOT(OnRsuEvent(int, quint32, qint32, quint8, quint8)))) {
                    QString errorMsg = QString("天线%1信号槽连接失败").arg(nRsuIndex);
                    ErrorLog(errorMsg);
                    if (sError) *sError = errorMsg;
                    
                    // 更新天线状态为异常
                    m_pFrmDevStatus->Devstatus(nDevId, 1);
                    Ptr_ETCCtrl->OnDevStatusChangedEvent(nDevId, 1);
                    
                    delete pProcessor;
                    delete m_pProcessorThreads[nRsuIndex];
                    m_pProcessorThreads[nRsuIndex] = NULL;
                    return false;
                }

                connect(m_pProcessorThreads[nRsuIndex], SIGNAL(finished()), pProcessor, SLOT(deleteLater()));
                connect(pProcessor, SIGNAL(NotifyPsamAuthEvent(int, int, int)), this,
                        SLOT(OnPsamAuthEvent(int, int, int)));

                pProcessor->moveToThread(m_pProcessorThreads[nRsuIndex]);
                
                // 启动线程
                try {
                    m_pProcessorThreads[nRsuIndex]->start();
                    if (!m_pProcessorThreads[nRsuIndex]->isRunning()) {
                        QString errorMsg = QString("天线%1线程启动失败").arg(nRsuIndex);
                        ErrorLog(errorMsg);
                        if (sError) *sError = errorMsg;
                        delete pProcessor;
                        delete m_pProcessorThreads[nRsuIndex];
                        m_pProcessorThreads[nRsuIndex] = NULL;
                        return false;
                    }
                }
                catch (const std::exception& e) {
                    QString errorMsg = QString("天线%1线程启动异常: %2").arg(nRsuIndex).arg(e.what());
                    ErrorLog(errorMsg);
                    if (sError) *sError = errorMsg;
                    delete pProcessor;
                    delete m_pProcessorThreads[nRsuIndex];
                    m_pProcessorThreads[nRsuIndex] = NULL;
                    return false;
                }
            }
            catch (const std::exception& e) {
                QString errorMsg = QString("天线%1创建新线程异常: %2").arg(nRsuIndex).arg(e.what());
                ErrorLog(errorMsg);
                if (sError) *sError = errorMsg;
                return false;
            }
            
            // 等待线程启动完成
            SleeperThread::msleep(100);
            
            // 在线程启动后调用天线设备的ReInitRsu方法
            bool isSuccess = false;
            try {
                isSuccess = pDev->ReInitRsu();
            }
            catch (const std::exception& e) {
                QString errorMsg = QString("天线%1重新初始化异常: %2").arg(nRsuIndex).arg(e.what());
                ErrorLog(errorMsg);
                if (sError) *sError = errorMsg;
                return false;
            }
            
            // 更新天线状态
            if (isSuccess) {
                // 成功状态
                m_pFrmDevStatus->Devstatus(nDevId, 0);
                Ptr_ETCCtrl->OnDevStatusChangedEvent(nDevId, 0);
                
                // 调用OnRsuState以进行完整的状态处理
                OnRsuState(nRsuIndex, true);
                
                DebugLog(QString("天线%1重新初始化成功").arg(nRsuIndex));
                return true;
            } else {
                // 失败状态
                m_pFrmDevStatus->Devstatus(nDevId, 1);
                Ptr_ETCCtrl->OnDevStatusChangedEvent(nDevId, 1);
                
                // 调用OnRsuState以进行完整的状态处理
                OnRsuState(nRsuIndex, false);
                
                QString errorMsg = QString("天线%1重新初始化失败").arg(nRsuIndex);
                ErrorLog(errorMsg);
                if (sError) *sError = errorMsg;
                return false;
            }
        }
    }
    catch (const std::exception& e) {
        QString errorMsg = QString("天线重新初始化过程发生未知异常: %1").arg(e.what());
        ErrorLog(errorMsg);
        if (sError) *sError = errorMsg;
        return false;
    }
    catch (...) {
        QString errorMsg = "天线重新初始化过程发生未知异常";
        ErrorLog(errorMsg);
        if (sError) *sError = errorMsg;
        return false;
    }
}

bool QDlgMain::ReinitializeRsuNew(int nRsuIndex, QString *sError)
{
    try {
        // 参数-1表示初始化所有天线
        if (nRsuIndex == -1) {
            // 初始化所有天线
            bool allSuccess = true;
            for (int i = 0; i < 2; ++i) {
                try {
                    CRsuDev *pDev = CDeviceFactory::GetRsuDev(i);
                    if (!pDev) continue;
                    
                    // 先检查天线设备状态
                    int nDevId = (i == 0) ? DEV_RSU : DEV_RSU1;
                    
                    bool threadNeedsRestart = false;
                    CRsuDataProcessor *pProcessor = NULL;
                    
                    // 检查线程状态
                    if (!m_pProcessorThreads[i] || !m_pProcessorThreads[i]->isRunning()) {
                        // 线程不存在或者未运行，需要重新创建
                        threadNeedsRestart = true;
                        
                        // 先尝试清理已有的线程资源
                        try {
                            if (m_pProcessorThreads[i]) {
                                if (m_pProcessorThreads[i]->isRunning()) {
                                    m_pProcessorThreads[i]->quit();
                                    if (!m_pProcessorThreads[i]->wait(1000)) {
                                        // 如果等待超时，强制终止
                                        m_pProcessorThreads[i]->terminate();
                                        m_pProcessorThreads[i]->wait();
                                    }
                                }
                                delete m_pProcessorThreads[i];
                                m_pProcessorThreads[i] = NULL;
                            }
                        }
                        catch (const std::exception& e) {
                            QString errorMsg = QString("天线%1关闭原有线程异常: %2").arg(i).arg(e.what());
                            ErrorLog(errorMsg);
                            if (sError) *sError = errorMsg;
                            allSuccess = false;
                            continue;
                        }
                    }
                    
                    // 如果需要重新创建线程
                    if (threadNeedsRestart) {
                        try {
                            // 创建新线程
                            m_pProcessorThreads[i] = new QThread();
                            if (!m_pProcessorThreads[i]) {
                                QString errorMsg = QString("天线%1创建新线程失败").arg(i);
                                ErrorLog(errorMsg);
                                if (sError) *sError = errorMsg;
                                allSuccess = false;
                                continue;
                            }
                            
                            // 创建数据处理器
                            pProcessor = new CRsuDataProcessor();
                            if (!pProcessor) {
                                QString errorMsg = QString("天线%1创建数据处理器失败").arg(i);
                                ErrorLog(errorMsg);
                                if (sError) *sError = errorMsg;
                                delete m_pProcessorThreads[i];
                                m_pProcessorThreads[i] = NULL;
                                allSuccess = false;
                                continue;
                            }

                            // 连接信号和槽
                            if (!QObject::connect(pDev, SIGNAL(NotifyRsuEvent(int, quint32, qint32, quint8, quint8)),
                                                  pProcessor, SLOT(OnRsuEvent(int, quint32, qint32, quint8, quint8)))) {
                                QString errorMsg = QString("天线%1信号槽连接失败").arg(i);
                                ErrorLog(errorMsg);
                                if (sError) *sError = errorMsg;
                                
                                // 更新天线状态为异常
                                m_pFrmDevStatus->Devstatus(nDevId, 1);
                                Ptr_ETCCtrl->OnDevStatusChangedEvent(nDevId, 1);
                                
                                delete pProcessor;
                                delete m_pProcessorThreads[i];
                                m_pProcessorThreads[i] = NULL;
                                allSuccess = false;
                                continue;
                            }

                            connect(m_pProcessorThreads[i], SIGNAL(finished()), pProcessor, SLOT(deleteLater()));
                            connect(pProcessor, SIGNAL(NotifyPsamAuthEvent(int, int, int)), this,
                                    SLOT(OnPsamAuthEvent(int, int, int)));

                            pProcessor->moveToThread(m_pProcessorThreads[i]);
                            
                            // 启动线程
                            try {
                                m_pProcessorThreads[i]->start();
                                if (!m_pProcessorThreads[i]->isRunning()) {
                                    QString errorMsg = QString("天线%1线程启动失败").arg(i);
                                    ErrorLog(errorMsg);
                                    if (sError) *sError = errorMsg;
                                    delete pProcessor;
                                    delete m_pProcessorThreads[i];
                                    m_pProcessorThreads[i] = NULL;
                                    allSuccess = false;
                                    continue;
                                }
                            }
                            catch (const std::exception& e) {
                                QString errorMsg = QString("天线%1线程启动异常: %2").arg(i).arg(e.what());
                                ErrorLog(errorMsg);
                                if (sError) *sError = errorMsg;
                                delete pProcessor;
                                delete m_pProcessorThreads[i];
                                m_pProcessorThreads[i] = NULL;
                                allSuccess = false;
                                continue;
                            }
                        }
                        catch (const std::exception& e) {
                            QString errorMsg = QString("天线%1创建新线程异常: %2").arg(i).arg(e.what());
                            ErrorLog(errorMsg);
                            if (sError) *sError = errorMsg;
                            allSuccess = false;
                            continue;
                        }
                    } else {
                        DebugLog(QString("天线%1线程正常运行，无需重新创建").arg(i));
                    }
                    
                    // 等待线程启动完成
                    SleeperThread::msleep(100);
                    
                    // 在线程启动后调用天线设备的ReInitRsu方法
                    bool isSuccess = false;
                    try {
                        isSuccess = pDev->ReInitRsu();
                    }
                    catch (const std::exception& e) {
                        QString errorMsg = QString("天线%1重新初始化异常: %2").arg(i).arg(e.what());
                        ErrorLog(errorMsg);
                        if (sError) *sError = errorMsg;
                        allSuccess = false;
                        continue;
                    }
                    
                    // 更新天线状态
                    if (isSuccess) {
                        // 成功状态
                        m_pFrmDevStatus->Devstatus(nDevId, 0);
                        Ptr_ETCCtrl->OnDevStatusChangedEvent(nDevId, 0);
                        
                        // 调用OnRsuState以进行完整的状态处理
                        OnRsuState(i, true);
                        
                        DebugLog(QString("天线%1重新初始化成功").arg(i));
                    } else {
                        // 失败状态
                        m_pFrmDevStatus->Devstatus(nDevId, 1);
                        Ptr_ETCCtrl->OnDevStatusChangedEvent(nDevId, 1);
                        
                        // 调用OnRsuState以进行完整的状态处理
                        OnRsuState(i, false);
                        
                        QString errorMsg = QString("天线%1重新初始化失败").arg(i);
                        ErrorLog(errorMsg);
                        if (sError) *sError = errorMsg;
                        allSuccess = false;
                    }
                }
                catch (const std::exception& e) {
                    QString errorMsg = QString("天线%1初始化过程发生未知异常: %2").arg(i).arg(e.what());
                    ErrorLog(errorMsg);
                    if (sError) *sError = errorMsg;
                    allSuccess = false;
                }
            }
            return allSuccess;
        } else {
            // 初始化指定天线
            if (nRsuIndex < 0 || nRsuIndex > 1) {
                if (sError) *sError = "天线索引无效";
                return false;
            }
            
            CRsuDev *pDev = CDeviceFactory::GetRsuDev(nRsuIndex);
            if (!pDev) {
                QString errorMsg = QString("天线%1设备不存在").arg(nRsuIndex);
                if (sError) *sError = errorMsg;
                return false;
            }
            
            // 先检查天线设备状态
            int nDevId = (nRsuIndex == 0) ? DEV_RSU : DEV_RSU1;
            
            bool threadNeedsRestart = false;
            CRsuDataProcessor *pProcessor = NULL;
            
            // 检查线程状态
            if (!m_pProcessorThreads[nRsuIndex] || !m_pProcessorThreads[nRsuIndex]->isRunning()) {
                // 线程不存在或者未运行，需要重新创建
                threadNeedsRestart = true;
                ErrorLog(QString("天线线程不存在或者未运行，需要重新创建"));
                // 先尝试清理已有的线程资源
                try {
                    if (m_pProcessorThreads[nRsuIndex]) {
                        if (m_pProcessorThreads[nRsuIndex]->isRunning()) {
                            m_pProcessorThreads[nRsuIndex]->quit();
                            if (!m_pProcessorThreads[nRsuIndex]->wait(1000)) {
                                // 如果等待超时，强制终止
                                m_pProcessorThreads[nRsuIndex]->terminate();
                                m_pProcessorThreads[nRsuIndex]->wait();
                            }
                        }
                        delete m_pProcessorThreads[nRsuIndex];
                        m_pProcessorThreads[nRsuIndex] = NULL;
                    }
                }
                catch (const std::exception& e) {
                    QString errorMsg = QString("天线%1关闭原有线程异常: %2").arg(nRsuIndex).arg(e.what());
                    ErrorLog(errorMsg);
                    if (sError) *sError = errorMsg;
                    return false;
                }
            }
            
            // 如果需要重新创建线程
            if (threadNeedsRestart) {
                try {
                    // 创建新线程
                    m_pProcessorThreads[nRsuIndex] = new QThread();
                    if (!m_pProcessorThreads[nRsuIndex]) {
                        QString errorMsg = QString("天线%1创建新线程失败").arg(nRsuIndex);
                        ErrorLog(errorMsg);
                        if (sError) *sError = errorMsg;
                        return false;
                    }
                    
                    // 创建数据处理器
                    pProcessor = new CRsuDataProcessor();
                    if (!pProcessor) {
                        QString errorMsg = QString("天线%1创建数据处理器失败").arg(nRsuIndex);
                        ErrorLog(errorMsg);
                        if (sError) *sError = errorMsg;
                        delete m_pProcessorThreads[nRsuIndex];
                        m_pProcessorThreads[nRsuIndex] = NULL;
                        return false;
                    }

                    // 连接信号和槽
                    if (!QObject::connect(pDev, SIGNAL(NotifyRsuEvent(int, quint32, qint32, quint8, quint8)),
                                          pProcessor, SLOT(OnRsuEvent(int, quint32, qint32, quint8, quint8)))) {
                        QString errorMsg = QString("天线%1信号槽连接失败").arg(nRsuIndex);
                        ErrorLog(errorMsg);
                        if (sError) *sError = errorMsg;
                        
                        // 更新天线状态为异常
                        m_pFrmDevStatus->Devstatus(nDevId, 1);
                        Ptr_ETCCtrl->OnDevStatusChangedEvent(nDevId, 1);
                        
                        delete pProcessor;
                        delete m_pProcessorThreads[nRsuIndex];
                        m_pProcessorThreads[nRsuIndex] = NULL;
                        return false;
                    }

                    connect(m_pProcessorThreads[nRsuIndex], SIGNAL(finished()), pProcessor, SLOT(deleteLater()));
                    connect(pProcessor, SIGNAL(NotifyPsamAuthEvent(int, int, int)), this,
                            SLOT(OnPsamAuthEvent(int, int, int)));

                    pProcessor->moveToThread(m_pProcessorThreads[nRsuIndex]);
                    
                    // 启动线程
                    try {
                        m_pProcessorThreads[nRsuIndex]->start();
                        if (!m_pProcessorThreads[nRsuIndex]->isRunning()) {
                            QString errorMsg = QString("天线%1线程启动失败").arg(nRsuIndex);
                            ErrorLog(errorMsg);
                            if (sError) *sError = errorMsg;
                            delete pProcessor;
                            delete m_pProcessorThreads[nRsuIndex];
                            m_pProcessorThreads[nRsuIndex] = NULL;
                            return false;
                        }
                    }
                    catch (const std::exception& e) {
                        QString errorMsg = QString("天线%1线程启动异常: %2").arg(nRsuIndex).arg(e.what());
                        ErrorLog(errorMsg);
                        if (sError) *sError = errorMsg;
                        delete pProcessor;
                        delete m_pProcessorThreads[nRsuIndex];
                        m_pProcessorThreads[nRsuIndex] = NULL;
                        return false;
                    }
                }
                catch (const std::exception& e) {
                    QString errorMsg = QString("天线%1创建新线程异常: %2").arg(nRsuIndex).arg(e.what());
                    ErrorLog(errorMsg);
                    if (sError) *sError = errorMsg;
                    return false;
                }
            } else {
                DebugLog(QString("天线%1线程正常运行，无需重新创建").arg(nRsuIndex));
            }
            
            // 等待线程启动完成
            SleeperThread::msleep(100);
            
            // 在线程启动后调用天线设备的ReInitRsu方法
            bool isSuccess = false;
            try {
                isSuccess = pDev->ReInitRsu();
            }
            catch (const std::exception& e) {
                QString errorMsg = QString("天线%1重新初始化异常: %2").arg(nRsuIndex).arg(e.what());
                ErrorLog(errorMsg);
                if (sError) *sError = errorMsg;
                return false;
            }
            
            // 更新天线状态
            if (isSuccess) {
                // 成功状态
                m_pFrmDevStatus->Devstatus(nDevId, 0);
                Ptr_ETCCtrl->OnDevStatusChangedEvent(nDevId, 0);
                
                // 调用OnRsuState以进行完整的状态处理
                OnRsuState(nRsuIndex, true);
                
                DebugLog(QString("天线%1重新初始化成功").arg(nRsuIndex));
                return true;
            } else {
                // 失败状态
                m_pFrmDevStatus->Devstatus(nDevId, 1);
                Ptr_ETCCtrl->OnDevStatusChangedEvent(nDevId, 1);
                
                // 调用OnRsuState以进行完整的状态处理
                OnRsuState(nRsuIndex, false);
                
                QString errorMsg = QString("天线%1重新初始化失败").arg(nRsuIndex);
                ErrorLog(errorMsg);
                if (sError) *sError = errorMsg;
                return false;
            }
        }
    }
    catch (const std::exception& e) {
        QString errorMsg = QString("天线重新初始化过程发生未知异常: %1").arg(e.what());
        ErrorLog(errorMsg);
        if (sError) *sError = errorMsg;
        return false;
    }
    catch (...) {
        QString errorMsg = "天线重新初始化过程发生未知异常";
        ErrorLog(errorMsg);
        if (sError) *sError = errorMsg;
        return false;
    }
}
/**
 * @brief 重新初始化天线函数（安全版本，防止系统崩溃）
 * @param nRsuIndex 天线索引：-1表示所有天线，0表示前天线，1表示后天线
 * @param sError 错误信息指针，可为空
 * @return 成功返回true，失败返回false
 * @note 增加了多重安全检查，防止系统崩溃
 *       此函数直接使用已有的数据处理线程，不会重新创建或关闭线程
 *       适用于线程运行正常但需要重新初始化天线设备的场景
 */
bool QDlgMain::ReinitializeRsuNew2(int nRsuIndex, QString *sError)
{
    try {
        // 检查交易状态，如果有交易在进行中则不允许初始化
        if (nRsuIndex != -1) { // 对于单个天线进行检查
            if (!CheckCanInitRsu(nRsuIndex)) {
                QString errorMsg = QString("天线%1交易进行中，不能重新初始化").arg(nRsuIndex == 0 ? "前" : "后");
                if (sError) *sError = errorMsg;
                return false;
            }
        }
        
        // 参数-1表示初始化所有天线
        if (nRsuIndex == -1) {
            // 初始化所有天线
            bool allSuccess = true;
            DebugLog("开始重新初始化所有天线（使用已有线程）");
            
            for (int i = 0; i < 2; ++i) {
                try {
                    // 检查天线交易状态
                    if (!CheckCanInitRsu(i)) {
                        QString errorMsg = QString("天线%1交易进行中，跳过重新初始化").arg(i);
                        DebugLog(errorMsg);
                        if (sError) *sError = errorMsg;
                        allSuccess = false;
                        continue;
                    }
                    
                    // 多重安全检查
                    CRsuDev *pDev = CDeviceFactory::GetRsuDev(i);
                    if (!pDev) {
                        DebugLog(QString("天线%1设备不存在，跳过重新初始化").arg(i));
                        continue;
                    }
                    
                    // 验证设备对象有效性
                    try {
                        QString devId = pDev->GetDevId();
                        if (devId.isEmpty()) {
                            QString errorMsg = QString("天线%1设备对象无效").arg(i);
                            ErrorLog(errorMsg);
                            if (sError) *sError = errorMsg;
                            allSuccess = false;
                            continue;
                        }
                        DebugLog(QString("天线%1设备ID: %2").arg(i).arg(devId));
                    }
                    catch (...) {
                        QString errorMsg = QString("天线%1设备对象访问异常").arg(i);
                        ErrorLog(errorMsg);
                        if (sError) *sError = errorMsg;
                        allSuccess = false;
                        continue;
                    }
                    
                    // 获取设备ID
                    int nDevId = (i == 0) ? DEV_RSU : DEV_RSU1;
                    
                    // 检查数据处理线程是否正常运行
                    if (!m_pProcessorThreads[i] || !m_pProcessorThreads[i]->isRunning()) {
                        QString errorMsg = QString("天线%1数据处理线程未运行，无法进行重新初始化").arg(i);
                        ErrorLog(errorMsg);
                        if (sError) *sError = errorMsg;
                        allSuccess = false;
                        continue;
                    }
                    
                    // 安全检查设备当前状态
                    bool bCurrentInitStatus = false;
                    quint8 nPsamStatus = 0;
                    try {
                        bCurrentInitStatus = pDev->bIniteOk();
                        nPsamStatus = pDev->GetPsamStatus();
                    }
                    catch (const std::exception& e) {
                        QString errorMsg = QString("天线%1状态读取异常: %2").arg(i).arg(e.what());
                        ErrorLog(errorMsg);
                        if (sError) *sError = errorMsg;
                        allSuccess = false;
                        continue;
                    }
                    catch (...) {
                        QString errorMsg = QString("天线%1状态读取发生未知异常").arg(i);
                        ErrorLog(errorMsg);
                        if (sError) *sError = errorMsg;
                        allSuccess = false;
                        continue;
                    }
                    
                    DebugLog(QString("天线%1当前状态：初始化状态=%2，PSAM状态=%3")
                             .arg(i)
                             .arg(bCurrentInitStatus ? "已初始化" : "未初始化")
                             .arg(nPsamStatus));
                    
                    // 如果设备已经初始化且PSAM状态正常，记录但仍执行重新初始化
                    if (bCurrentInitStatus && nPsamStatus == PSAM_NORMAL) {
                        DebugLog(QString("天线%1已正常初始化，仍将执行重新初始化").arg(i));
                    } else {
                        DebugLog(QString("天线%1需要重新初始化：初始化状态=%2，PSAM状态=%3")
                                 .arg(i)
                                 .arg(bCurrentInitStatus ? "正常" : "异常")
                                 .arg(nPsamStatus));
                    }
                    
                    DebugLog(QString("天线%1开始重新初始化（使用已有线程）").arg(i));
                    
                    // 安全调用天线设备的ReInitRsu方法
                    bool isSuccess = false;
                    try {
                        isSuccess = pDev->ReInitRsu();
                    }
                    catch (const std::exception& e) {
                        QString errorMsg = QString("天线%1重新初始化标准异常: %2").arg(i).arg(e.what());
                        ErrorLog(errorMsg);
                        if (sError) *sError = errorMsg;
                        allSuccess = false;
                        continue;
                    }
                    catch (...) {
                        QString errorMsg = QString("天线%1重新初始化发生未知异常，可能硬件故障").arg(i);
                        ErrorLog(errorMsg);
                        if (sError) *sError = errorMsg;
                        allSuccess = false;
                        continue;
                    }
                    
                    // 等待初始化完成
                    SleeperThread::msleep(200);
                    
                    // 安全验证重新初始化后的状态
                    bool bNewInitStatus = false;
                    quint8 nNewPsamStatus = 0;
                    try {
                        bNewInitStatus = pDev->bIniteOk();
                        nNewPsamStatus = pDev->GetPsamStatus();
                    }
                    catch (const std::exception& e) {
                        QString errorMsg = QString("天线%1初始化后状态检查标准异常: %2").arg(i).arg(e.what());
                        ErrorLog(errorMsg);
                        if (sError) *sError = errorMsg;
                        allSuccess = false;
                        continue;
                    }
                    catch (...) {
                        QString errorMsg = QString("天线%1初始化后状态检查异常").arg(i);
                        ErrorLog(errorMsg);
                        if (sError) *sError = errorMsg;
                        allSuccess = false;
                        continue;
                    }
                    
                    DebugLog(QString("天线%1重新初始化后状态：返回值=%2，初始化状态=%3，PSAM状态=%4")
                             .arg(i)
                             .arg(isSuccess ? "成功" : "失败")
                             .arg(bNewInitStatus ? "已初始化" : "未初始化")
                             .arg(nNewPsamStatus));
                    
                    // 综合判断初始化是否真正成功
                    bool bRealSuccess = isSuccess && bNewInitStatus;
                    
                    // 更新天线状态
                    try {
                        if (bRealSuccess) {
                            // 成功状态
                            m_pFrmDevStatus->Devstatus(nDevId, 0);
                            Ptr_ETCCtrl->OnDevStatusChangedEvent(nDevId, 0);
                            
                            // 调用OnRsuState以进行完整的状态处理
                            OnRsuState(i, true);
                            
                            DebugLog(QString("天线%1重新初始化成功").arg(i));
                        } else {
                            // 失败状态
                            m_pFrmDevStatus->Devstatus(nDevId, 1);
                            Ptr_ETCCtrl->OnDevStatusChangedEvent(nDevId, 1);
                            
                            // 调用OnRsuState以进行完整的状态处理
                            OnRsuState(i, false);
                            
                            QString errorMsg = QString("天线%1重新初始化失败：函数返回=%2，设备状态=%3")
                                    .arg(i)
                                    .arg(isSuccess ? "成功" : "失败")
                                    .arg(bNewInitStatus ? "已初始化" : "未初始化");
                            ErrorLog(errorMsg);
                            if (sError) *sError = errorMsg;
                            allSuccess = false;
                        }
                    }
                    catch (const std::exception& e) {
                        QString errorMsg = QString("天线%1状态更新标准异常: %2").arg(i).arg(e.what());
                        ErrorLog(errorMsg);
                        if (sError) *sError = errorMsg;
                        allSuccess = false;
                    }
                    catch (...) {
                        QString errorMsg = QString("天线%1状态更新异常").arg(i);
                        ErrorLog(errorMsg);
                        if (sError) *sError = errorMsg;
                        allSuccess = false;
                    }
                }
                catch (const std::exception& e) {
                    QString errorMsg = QString("天线%1初始化过程发生标准异常: %2").arg(i).arg(e.what());
                    ErrorLog(errorMsg);
                    if (sError) *sError = errorMsg;
                    allSuccess = false;
                }
                catch (...) {
                    QString errorMsg = QString("天线%1初始化过程发生严重异常").arg(i);
                    ErrorLog(errorMsg);
                    if (sError) *sError = errorMsg;
                    allSuccess = false;
                }
            }
            
            DebugLog(QString("所有天线重新初始化完成，结果: %1").arg(allSuccess ? "成功" : "部分失败"));
            return allSuccess;
        } else {
            // 初始化指定天线
            if (nRsuIndex < 0 || nRsuIndex > 1) {
                QString errorMsg = "天线索引无效，必须为0（前天线）或1（后天线）";
                ErrorLog(errorMsg);
                if (sError) *sError = errorMsg;
                return false;
            }
            
            DebugLog(QString("开始重新初始化天线%1（使用已有线程）").arg(nRsuIndex));
            
            // 多重安全检查
            CRsuDev *pDev = CDeviceFactory::GetRsuDev(nRsuIndex);
            if (!pDev) {
                QString errorMsg = QString("天线%1设备不存在").arg(nRsuIndex);
                ErrorLog(errorMsg);
                if (sError) *sError = errorMsg;
                return false;
            }
            
            // 验证设备对象有效性
            try {
                QString devId = pDev->GetDevId();
                if (devId.isEmpty()) {
                    QString errorMsg = QString("天线%1设备对象无效").arg(nRsuIndex);
                    ErrorLog(errorMsg);
                    if (sError) *sError = errorMsg;
                    return false;
                }
                DebugLog(QString("天线%1设备ID: %2").arg(nRsuIndex).arg(devId));
            }
            catch (const std::exception& e) {
                QString errorMsg = QString("天线%1设备对象访问异常: %2").arg(nRsuIndex).arg(e.what());
                ErrorLog(errorMsg);
                if (sError) *sError = errorMsg;
                return false;
            }
            catch (...) {
                QString errorMsg = QString("天线%1设备对象访问异常").arg(nRsuIndex);
                ErrorLog(errorMsg);
                if (sError) *sError = errorMsg;
                return false;
            }
            
            // 获取设备ID
            int nDevId = (nRsuIndex == 0) ? DEV_RSU : DEV_RSU1;
            
            // 检查数据处理线程是否正常运行
            if (!m_pProcessorThreads[nRsuIndex] || !m_pProcessorThreads[nRsuIndex]->isRunning()) {
                QString errorMsg = QString("天线%1数据处理线程未运行，无法进行重新初始化").arg(nRsuIndex);
                ErrorLog(errorMsg);
                if (sError) *sError = errorMsg;
                return false;
            }
            
            DebugLog(QString("天线%1数据处理线程运行正常").arg(nRsuIndex));
            
            // 安全检查设备当前状态
            bool bCurrentInitStatus = false;
            quint8 nPsamStatus = 0;
            try {
                bCurrentInitStatus = pDev->bIniteOk();
                nPsamStatus = pDev->GetPsamStatus();
            }
            catch (const std::exception& e) {
                QString errorMsg = QString("天线%1状态读取标准异常: %2").arg(nRsuIndex).arg(e.what());
                ErrorLog(errorMsg);
                if (sError) *sError = errorMsg;
                return false;
            }
            catch (...) {
                QString errorMsg = QString("天线%1状态读取发生未知异常").arg(nRsuIndex);
                ErrorLog(errorMsg);
                if (sError) *sError = errorMsg;
                return false;
            }
            
            DebugLog(QString("天线%1当前状态：初始化状态=%2，PSAM状态=%3")
                     .arg(nRsuIndex)
                     .arg(bCurrentInitStatus ? "已初始化" : "未初始化")
                     .arg(nPsamStatus));
            
            // 如果设备已经初始化且PSAM状态正常，记录但仍执行重新初始化
            if (bCurrentInitStatus && nPsamStatus == PSAM_NORMAL) {
                DebugLog(QString("天线%1已正常初始化，仍将执行重新初始化").arg(nRsuIndex));
            } else {
                DebugLog(QString("天线%1需要重新初始化：初始化状态=%2，PSAM状态=%3")
                         .arg(nRsuIndex)
                         .arg(bCurrentInitStatus ? "正常" : "异常")
                         .arg(nPsamStatus));
            }
            
            DebugLog(QString("天线%1开始执行重新初始化").arg(nRsuIndex));
            
            // 安全调用天线设备的ReInitRsu方法
            bool isSuccess = false;
            try {
                isSuccess = pDev->ReInitRsu();
            }
            catch (const std::exception& e) {
                QString errorMsg = QString("天线%1重新初始化标准异常: %2").arg(nRsuIndex).arg(e.what());
                ErrorLog(errorMsg);
                if (sError) *sError = errorMsg;
                return false;
            }
            catch (...) {
                QString errorMsg = QString("天线%1重新初始化发生未知异常，可能硬件故障").arg(nRsuIndex);
                ErrorLog(errorMsg);
                if (sError) *sError = errorMsg;
                return false;
            }
            
            // 等待初始化完成
            SleeperThread::msleep(200);
            
            // 安全验证重新初始化后的状态
            bool bNewInitStatus = false;
            quint8 nNewPsamStatus = 0;
            try {
                bNewInitStatus = pDev->bIniteOk();
                nNewPsamStatus = pDev->GetPsamStatus();
            }
            catch (const std::exception& e) {
                QString errorMsg = QString("天线%1初始化后状态检查标准异常: %2").arg(nRsuIndex).arg(e.what());
                ErrorLog(errorMsg);
                if (sError) *sError = errorMsg;
                return false;
            }
            catch (...) {
                QString errorMsg = QString("天线%1初始化后状态检查异常").arg(nRsuIndex);
                ErrorLog(errorMsg);
                if (sError) *sError = errorMsg;
                return false;
            }
            
            DebugLog(QString("天线%1重新初始化后状态：返回值=%2，初始化状态=%3，PSAM状态=%4")
                     .arg(nRsuIndex)
                     .arg(isSuccess ? "成功" : "失败")
                     .arg(bNewInitStatus ? "已初始化" : "未初始化")
                     .arg(nNewPsamStatus));
            
            // 综合判断初始化是否真正成功
            bool bRealSuccess = isSuccess && bNewInitStatus;
            
            // 更新天线状态
            try {
                if (bRealSuccess) {
                    // 成功状态
                    m_pFrmDevStatus->Devstatus(nDevId, 0);
                    Ptr_ETCCtrl->OnDevStatusChangedEvent(nDevId, 0);
                    
                    // 调用OnRsuState以进行完整的状态处理
                    OnRsuState(nRsuIndex, true);
                    
                    DebugLog(QString("天线%1重新初始化成功").arg(nRsuIndex));
                    return true;
                } else {
                    // 失败状态
                    m_pFrmDevStatus->Devstatus(nDevId, 1);
                    Ptr_ETCCtrl->OnDevStatusChangedEvent(nDevId, 1);
                    
                    // 调用OnRsuState以进行完整的状态处理
                    OnRsuState(nRsuIndex, false);
                    
                    QString errorMsg = QString("天线%1重新初始化失败：函数返回=%2，设备状态=%3")
                            .arg(nRsuIndex)
                            .arg(isSuccess ? "成功" : "失败")
                            .arg(bNewInitStatus ? "已初始化" : "未初始化");
                    ErrorLog(errorMsg);
                    if (sError) *sError = errorMsg;
                    return false;
                }
            }
            catch (const std::exception& e) {
                QString errorMsg = QString("天线%1状态更新标准异常: %2").arg(nRsuIndex).arg(e.what());
                ErrorLog(errorMsg);
                if (sError) *sError = errorMsg;
                return false;
            }
            catch (...) {
                QString errorMsg = QString("天线%1状态更新异常").arg(nRsuIndex);
                ErrorLog(errorMsg);
                if (sError) *sError = errorMsg;
                return false;
            }
        }
    }
    catch (const std::exception& e) {
        QString errorMsg = QString("天线重新初始化过程发生标准异常: %1").arg(e.what());
        ErrorLog(errorMsg);
        if (sError) *sError = errorMsg;
        return false;
    }
    catch (...) {
        QString errorMsg = "天线重新初始化过程发生严重未知异常，可能硬件故障";
        ErrorLog(errorMsg);
        if (sError) *sError = errorMsg;
        return false;
    }
}
void QDlgMain::ShowRsuSelectionDialog()
{
    // 检查天线数量
    int rsuCount = 0;
    for (int i = 0; i < 2; ++i) {
        if (CDeviceFactory::GetRsuDev(i)) {
            rsuCount++;
        }
    }
    
    if (rsuCount <= 1) {
        // 只有一个天线，直接初始化后天线
        ReinitializeRsuAsync(1);
        return;
    }
    
    // 使用ListDlg创建菜单界面
    QList<CListData> lstMenuItem;
    
    // 添加菜单项（双天线情况下显示前天线和后天线选项）
    lstMenuItem.push_back(CListData(0, "前天线", "重新初始化前天线", NULL, true, false));
    lstMenuItem.push_back(CListData(1, "后天线", "重新初始化后天线", NULL, true, false));
    
    // 创建菜单对话框
    CListDlg dlg("天线初始化", "请按【数字】键选择", true, true, this);
    dlg.ShowList(lstMenuItem, 0);
    
    // 允许远程控制
    Ptr_RemoteCtrl->MenuSelectBegin("天线初始化", lstMenuItem, true, &dlg, SLOT(OnMenuSelected(int)));
    
    // 显示菜单并获取用户选择
    int nResult = dlg.doModalShow();
    
    // 结束远程控制
    Ptr_RemoteCtrl->MenuSelectEnd(&dlg, SLOT(OnMenuSelected(int)));
    
    // 处理用户选择（使用异步方式）
    switch (nResult) {
    case 1:  // 前天线
        ReinitializeRsuAsync(0);
        break;
    case 2:  // 后天线
        ReinitializeRsuAsync(1);
        break;
    default:
        // 用户取消或关闭对话框
        break;
    }
}

/**
 * @brief 异步重新初始化天线
 * @param nRsuIndex 天线索引：-1表示所有天线，0表示前天线，1表示后天线
 * @note 该函数立即返回，初始化结果通过信号 rsuInitCompleted 通知
 */
void QDlgMain::ReinitializeRsuAsync(int nRsuIndex)
{
    try {
        // 参数-1表示初始化所有天线
        if (nRsuIndex == -1) {
            DebugLog("开始异步重新初始化所有天线");
            
            for (int i = 0; i < 2; ++i) {
                CRsuDev *pDev = CDeviceFactory::GetRsuDev(i);
                if (!pDev) {
                    DebugLog(QString("天线%1设备不存在，跳过").arg(i));
                    continue;
                }
                
                // 异步初始化单个天线
                ReinitializeRsuAsync(i);
            }
            return;
        }
        
        // 检查参数合法性
        if (nRsuIndex < 0 || nRsuIndex >= 2) {
            QString errorMsg = QString("无效的天线索引: %1").arg(nRsuIndex);
            ErrorLog(errorMsg);
            emit rsuInitCompleted(nRsuIndex, false, errorMsg);
            return;
        }
        
        // 检查是否已有初始化任务在进行
        if (m_mapInitThreads.contains(nRsuIndex)) {
            QThread *pThread = m_mapInitThreads[nRsuIndex];
            if (pThread && pThread->isRunning()) {
                QString errorMsg = QString("天线%1正在初始化中，请稍后再试").arg(nRsuIndex);
                ShowWarning(errorMsg);
                return;
            }
        }
        
        // 获取天线设备
        CRsuDev *pDev = CDeviceFactory::GetRsuDev(nRsuIndex);
        if (!pDev) {
            QString errorMsg = QString("天线%1设备不存在").arg(nRsuIndex);
            ErrorLog(errorMsg);
            emit rsuInitCompleted(nRsuIndex, false, errorMsg);
            return;
        }
        
        // 检查数据处理线程是否正常
        if (!m_pProcessorThreads[nRsuIndex] || !m_pProcessorThreads[nRsuIndex]->isRunning()) {
            QString errorMsg = QString("天线%1数据处理线程未运行，请先启动线程").arg(nRsuIndex);
            ErrorLog(errorMsg);
            emit rsuInitCompleted(nRsuIndex, false, errorMsg);
            return;
        }
        
        // 检查是否有交易在进行中，如果有则不允许初始化天线
        if (!CheckCanInitRsu(nRsuIndex)) {
            QString errorMsg = QString("%1天线交易进行中，不能重新初始化").arg(nRsuIndex == 0 ? "前" : "后");
            ShowWarning(errorMsg);
            DebugLog(errorMsg);
            return;
        }
        
        // 创建工作线程和工作对象
        QThread *pThread = new QThread();
        RsuInitWorker *pWorker = new RsuInitWorker(pDev, nRsuIndex);
        
        // 移动工作对象到线程
        pWorker->moveToThread(pThread);
        
        // 连接信号槽
        connect(pThread, SIGNAL(started()), pWorker, SLOT(doInit()));
        connect(pWorker, SIGNAL(initFinished(int,bool,QString)), 
                this, SLOT(OnRsuInitFinished(int,bool,QString)));
        connect(pWorker, SIGNAL(initFinished(int,bool,QString)), 
                pThread, SLOT(quit()));
        connect(pThread, SIGNAL(finished()), pWorker, SLOT(deleteLater()));
        connect(pThread, SIGNAL(finished()), pThread, SLOT(deleteLater()));
        
        // 保存线程引用
        m_mapInitThreads[nRsuIndex] = pThread;
        
        // 启动线程
        pThread->start();
        
        DebugLog(QString("天线%1异步初始化任务已启动").arg(nRsuIndex));
        
        // 显示提示信息
        ShowPromptMsg(QString("%1天线重新初始化中...").arg(nRsuIndex == 0 ? "前" : "后"), false, true);
    }
    catch (const std::exception& e) {
        QString errorMsg = QString("启动天线%1异步初始化异常: %2").arg(nRsuIndex).arg(e.what());
        ErrorLog(errorMsg);
        emit rsuInitCompleted(nRsuIndex, false, errorMsg);
    }
    catch (...) {
        QString errorMsg = QString("启动天线%1异步初始化发生未知异常").arg(nRsuIndex);
        ErrorLog(errorMsg);
        emit rsuInitCompleted(nRsuIndex, false, errorMsg);
    }
}

/**
 * @brief 天线异步初始化完成槽函数
 * @param nRsuIndex 天线索引
 * @param bSuccess 是否成功
 * @param sError 错误信息
 */
void QDlgMain::OnRsuInitFinished(int nRsuIndex, bool bSuccess, const QString &sError)
{
    try {
        // 移除已完成的线程引用
        if (m_mapInitThreads.contains(nRsuIndex)) {
            m_mapInitThreads.remove(nRsuIndex);
        }
        
        // 获取设备ID
        int nDevId = (nRsuIndex == 0) ? DEV_RSU : DEV_RSU1;
        
        if (bSuccess) {
            // 更新天线状态为正常
            m_pFrmDevStatus->Devstatus(nDevId, 0);
            Ptr_ETCCtrl->OnDevStatusChangedEvent(nDevId, 0);
            
            // 显示成功提示
            QString successMsg = QString("%1天线重新初始化成功").arg(nRsuIndex == 0 ? "前" : "后");
            ShowPromptMsg(successMsg, false, true);
            DebugLog(successMsg);
        } else {
            // 更新天线状态为异常
            m_pFrmDevStatus->Devstatus(nDevId, 1);
            Ptr_ETCCtrl->OnDevStatusChangedEvent(nDevId, 1);
            
            // 显示错误提示
            QString errorMsg = QString("%1天线重新初始化失败：%2").arg(nRsuIndex == 0 ? "前" : "后").arg(sError);
            ShowWarning(errorMsg);
            ErrorLog(errorMsg);
        }
        
        // 发送完成信号
        emit rsuInitCompleted(nRsuIndex, bSuccess, sError);
    }
    catch (const std::exception& e) {
        ErrorLog(QString("处理天线%1初始化结果异常: %2").arg(nRsuIndex).arg(e.what()));
    }
    catch (...) {
        ErrorLog(QString("处理天线%1初始化结果发生未知异常").arg(nRsuIndex));
    }
}

/**
 * @brief 检查是否可以初始化天线（交易状态检查）
 * @param nRsuIndex 天线索引：0表示前天线，1表示后天线
 * @return 可以初始化返回true，否则返回false
 * @note 如果有交易正在进行中（状态 >= Ts_IsReadingIcc），则不允许初始化
 */
bool QDlgMain::CheckCanInitRsu(int nRsuIndex)
{
    try {
        // 根据 nRsuIndex 确定要检查的设备索引
        int nDevIndex = (nRsuIndex == 0) ? DevIndex_First : DevIndex_Second;
        QString sAntennaName = (nRsuIndex == 0) ? "前" : "后";
        
        // 只检查要初始化的天线是否正在交易
        CTransInfo *pTargetTransInfo = Ptr_ETCCtrl->GetCurTransInfo(nDevIndex);
        if (pTargetTransInfo) {
            if ((pTargetTransInfo->transState >= CTransInfo::Ts_IsReadingIcc) && (pTargetTransInfo->transState < CTransInfo::Ts_WaitToSave)) {
                DebugLog(QString("%1天线正在交易中，交易状态：%2，不允许初始化")
                         .arg(sAntennaName).arg(pTargetTransInfo->transState));
                return false;
            }
        }
        
        DebugLog(QString("交易状态检查通过，可以初始化%1天线，当前交易状态：%2")
                 .arg(sAntennaName)
                 .arg(pTargetTransInfo ? QString::number(pTargetTransInfo->transState) : "无交易信息"));
        
        return true;
    }
    catch (const std::exception& e) {
        ErrorLog(QString("检查天线%1交易状态异常: %2").arg(nRsuIndex).arg(e.what()));
        return false; // 异常情况下为安全起见，不允许初始化
    }
    catch (...) {
        ErrorLog(QString("检查天线%1交易状态发生未知异常").arg(nRsuIndex));
        return false; // 异常情况下为安全起见，不允许初始化
    }
}

