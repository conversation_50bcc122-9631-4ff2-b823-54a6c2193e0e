﻿#include "videocard.h"
#include "log4qt/ilogmsg.h"
#include "common/globalutils.h"
#include <time.h>
#ifdef Q_OS_WIN32
#include <windows.h>
#endif
#include <QByteArray>

#ifdef Q_OS_UNIX
#define WINAPI
#define HWND int
#endif

/*
typedef qint32 (WINAPI *Func_VC_Init)(HWND ShowWndHandled,qint32 nDisLeft, qint32 nDisTop,
                                qint32 nDisWidth, qint32 nDisHeight,
                                qint32 nCapWidth, qint32 nCapHeight);
                                */

typedef qint32 (WINAPI *Func_VC_Init)(HWND ShowWndHandled,
                                qint32 nDisWidth, qint32 nDisHeight,
                                qint32 nCapWidth, qint32 nCapHeight);



typedef qint32 (WINAPI *Func_VC_Close)();
typedef qint32 (WINAPI *Func_VC_Start)(qint32 AShowType);
typedef qint32 (WINAPI *Func_VC_SaveAsBitmap)(const char *szFileName);

/*
typedef qint32 (WINAPI *Func_VC_SaveInfo)(CCapInfo& CapInfo,const char* bmpFileName,const char* jpgFileName);
typedef qint32 (WINAPI *Func_VC_ReSize)(int Width,int Height);

typedef qint32 (WINAPI *Func_VC_Init2)(HWND ShowWndHandled,
                                      qint32 nDisWidth, qint32 nDisHeight,
                                      qint32 nCapWidth, qint32 nCapHeight);
                                      */

//江西视频卡接口

typedef qint32 (WINAPI *Proc_VC_ConnectIPNC)(HWND hwnd,char *szIP,
                                             qint32 nDisplayLeft,qint32 nDisplayTop,
                                             qint32 nDisplayWidth,qint32 nDisplayHeight,bool bShowWindow);
typedef qint32 (WINAPI *Proc_VC_DisConnectIPNC)();
typedef qint32 (WINAPI *Proc_VC_CaptureImage)(char *szFileName,char *szPlate,char *Color);


//std 视频流
//>0 打开设备成功，返回值为设备句柄号  -100  设备无响应  -1000 传入参数错误  -1001 设备被占用
//nType Int 4
//连接方式：0=板卡，1=网络连接，2=车 牌识别共用
//sParas char* 4
//连接信息： -串口：填“板卡” 例“VideCard0” -网络：填“网址，端口，用户名，密码” 例“************,8000,admin,password” -车牌识别共用 例“车牌识别句柄”
typedef qint32 (WINAPI *Func_VC_Init_Std)(qint32 nType,char* sParas);
// 0 关闭设备成功  -100 设备无响应  -1000 传入参数错误  -2000 其它错误
typedef qint32 (WINAPI *Func_VC_Deinit_Std)(qint32 nHandle);
// 0 操作成功  -100 设备无响应 -1000 传入参数错误  -1003 设置显示错误  -2000 其它错误
typedef qint32 (WINAPI *Func_VC_StartDisplay_Std)(qint32 nHandle,qint32 nWidth,qint32 nHeight,qint32 nFHandle);
// 0 操作成功  -100 设备无响应  -1000 传入参数错误  -1005 停止显示错误  -2000 其它错误
typedef qint32 (WINAPI *Func_VC_StopDisplay_Std)( qint32 nHandle);
// 0 操作成功  -100 设备无响应 -1000 传入参数错误  -1006 获取图片错误  -2000 其它错误
//获取图片
typedef qint32 (WINAPI *Func_VC_GetImage_Std)( qint32 nHandle,qint32 nFormat,char* sImage,qint32* nLength );
// 0 操作成功  -100 设备无响应  -1000 传入参数错误  -1007 获取图片错误  -2000 其它错误
// 获取图片文件   nFormat获取图片的格式，0:bmp,1:jpeg,其他 保留
typedef qint32 (WINAPI *Func_VC_GetImageFile_Std)(qint32 nHandle,qint32 nFormat,char* sFileName);
//字符叠加
/*typedef qint32 (WINAPI *Func_VC_TVPDisplay_Std)(qint32 nHandle, qint32 nRow,qint32 nCol,char* sText);
//从(row,col)位置开始清除 length 个字符；
//row=0 时表示清屏； col=0 时表示清除一行
// 0 操作成功  -100 设备无响应  -1000 传入参数错误  -2000 其它错误
typedef qint32 (WINAPI *Func_VC_TVPClear_Std)(qint32 nHandle, qint32 nRow, qint32 nCol, qint32 nLength);

typedef qint32 (WINAPI *Func_VC_SyncTime_Std)( qint32 nHandle, char* sSysTime );

typedef qint32 (WINAPI *Func_VC_ShowTime_Std)( qint32 nHandle, qint32 nStyle );

typedef qint32 (WINAPI *Func_VC_GetStatus_Std)(qint32 nHandle, qint32* pStatusCode);

typedef qint32 (WINAPI *Func_VC_GetStatusMsg_Std)( qint32 nStatusCode, char* sStatusMsg, qint32 nStatusMsgLen ) ;

typedef qint32 (WINAPI *Func_VC_GetHWVersion_Std)(qint32 nHandle, char* sHWVersion, qint32 nHWVerMaxLen,char* sAPIVersion,qint32 nAPIVerMaxLen);
*/
bool CVideoCard::m_bDriverLoaded=false;
QLibrary CVideoCard::m_hLibModule;
QString m_DefaultBmpFileName = QString("CapTmp.bmp");
QString m_DefaultJpgFileName =QString("videocap.jpg");

Func_VC_Init VC_Init=NULL;
Func_VC_Close VC_Close=NULL;
Func_VC_Start VC_Start=NULL;
Func_VC_SaveAsBitmap VC_SaveAsBitmap=NULL;

//std 动态库加载
Func_VC_Init_Std VC_Init_Std=NULL;
Func_VC_Deinit_Std VC_Deinit_Std=NULL;
Func_VC_StartDisplay_Std VC_StartDisplay_Std=NULL;
Func_VC_StopDisplay_Std VC_StopDisplay_Std=NULL;
Func_VC_GetImage_Std VC_GetImage_Std=NULL;
Func_VC_GetImageFile_Std VC_GetImageFile_Std=NULL;


/*Func_VC_TVPDisplay_Std VC_TVPDisplay_Std=NULL;
Func_VC_TVPClear_Std VC_TVPClear_Std=NULL;
Func_VC_SyncTime_Std VC_SyncTime_Std=NULL;
Func_VC_ShowTime_Std VC_ShowTime_Std=NULL;
Func_VC_GetStatus_Std VC_GetStatus_Std=NULL;
Func_VC_GetStatusMsg_Std VC_GetStatusMsg_Std=NULL;
Func_VC_GetHWVersion_Std VC_GetHWVersion_Std=NULL;
*/
Proc_VC_ConnectIPNC     VC_ConnectIPNC=NULL;
Proc_VC_DisConnectIPNC  VC_DisConnectIPNC=NULL;
Proc_VC_CaptureImage    VC_CaptureImage=NULL;


CVideoCard::CVideoCard():
    CAbstractDev()
{
    m_bInited = false;
    m_bHasPreparedImg = false;
    m_pCapThread = new CCaptureThread(this,NULL);
}

CVideoCard::~CVideoCard()
{
    // ReleaseDriver();
    // if(m_pCapThread)
    //     delete m_pCapThread;
    // 安全地关闭设备和停止线程
    CloseDev();
    
    // 安全地停止和删除线程
    if (m_pCapThread) {
        try {
            if (m_pCapThread->isRunning()) {
                m_pCapThread->StopThread();
                // 等待线程安全退出，最多等待3秒
                if (!m_pCapThread->wait(3000)) {
                    DebugLog("视频采集线程退出超时，强制终止");
                    m_pCapThread->terminate();
                    m_pCapThread->wait(1000);  // 再等1秒
                }
            }
            delete m_pCapThread;
            m_pCapThread = NULL;
        } catch (...) {
            ErrorLog("视频采集线程停止时发生异常");
            if (m_pCapThread) {
                delete m_pCapThread;
                m_pCapThread = NULL;
            }
        }
    }
    
    // 最后释放驱动
    ReleaseDriver();
}

bool CVideoCard::LoadDriver()
{
    if (m_bDriverLoaded)
    {
        //避免重复加载
        return true;
    }
    QString loadfile = QApplication::applicationDirPath()+"/"+m_sDriver;
    DebugLog(QString("加载视频卡动态库%1").arg(m_sDriver));
    m_hLibModule.setFileName(loadfile);
    if(!m_hLibModule.load())
    {
        ErrorLog(QString("加载视频设备动态库[%1]失败，System Error Code=%2")
                  .arg(m_sDriver).arg(m_hLibModule.errorString()));
        return false;
    }

    bool bRlt = false;
    if(1==m_bStd){
        bRlt = LoadStdDll();
    }else if(0==m_bStd){
        bRlt = LoadVideoCardDll();
    }else if(2==m_bStd){
        bRlt = loadIpNc();
    }

    if(!bRlt){
        m_hLibModule.unload();
        return false;
    }

    m_bDriverLoaded=true;
    DebugLog(QString("加载视频卡动态库%1成功").arg(m_sDriver));
    //第一个字符串，存储保存图像文件的绝对目录！
    //创建需要的路径
    CreatePath(m_sConnStr1);
    if(m_sConnStr1 != NULL)
        n_video_flag = true;//网口
    else
        n_video_flag = false;//视屏卡
    return true;
}

void CVideoCard::ReleaseDriver()
{
    if (!m_bDriverLoaded) {
        return;
    }
    
    try {
        // 先关闭设备，再设置驱动未加载标志
        // 注意：不在这里调用CloseDev()，因为析构函数已经调用了
        
        // 设置驱动未加载标志
        m_bDriverLoaded = false;

        // 清空函数指针
        VC_ConnectIPNC = NULL;
        VC_CaptureImage = NULL;
        VC_DisConnectIPNC = NULL;

        VC_Init = NULL;
        VC_Close = NULL;
        VC_Start = NULL;
        VC_SaveAsBitmap = NULL;

        //std
        VC_Init_Std = NULL;
        VC_Deinit_Std = NULL;
        VC_StartDisplay_Std = NULL;
        VC_StopDisplay_Std = NULL;
        VC_GetImage_Std = NULL;
        VC_GetImageFile_Std = NULL;

        // 卸载动态库
        if (m_hLibModule.isLoaded()) {
            m_hLibModule.unload();
        }
        
        DebugLog("视频采集卡驱动释放完毕");
        
    } catch (...) {
        ErrorLog("释放视频采集卡驱动时发生异常");
    }
}

bool CVideoCard::LoadStdDll()
{
    VC_Init_Std=(Func_VC_Init_Std)m_hLibModule.resolve("VC_Init");
    VC_Deinit_Std=(Func_VC_Deinit_Std)m_hLibModule.resolve("VC_Deinit");
    VC_StartDisplay_Std=(Func_VC_StartDisplay_Std)m_hLibModule.resolve("VC_StartDisplay");
    VC_StopDisplay_Std=(Func_VC_StopDisplay_Std)m_hLibModule.resolve("VC_StopDisplay");
    VC_GetImage_Std=(Func_VC_GetImage_Std)m_hLibModule.resolve("VC_GetImage");
    VC_GetImageFile_Std=(Func_VC_GetImageFile_Std)m_hLibModule.resolve("VC_GetImageFile");

   bool bErrorFun = false;

    if (NULL == VC_Init_Std )
    {
        bErrorFun = true;
        ErrorLog(QString("VC_Init get error !!"));
    }
    else if (NULL == VC_Deinit_Std )
    {
        bErrorFun = true;
        ErrorLog(QString("VC_Deinit get error !!"));
    }
    else if (NULL == VC_StartDisplay_Std )
    {
        bErrorFun = true;
        ErrorLog(QString("VC_StartDisplay get error !!"));
    }
    else if (NULL == VC_StopDisplay_Std )
    {
        bErrorFun = true;
        ErrorLog(QString("VC_StopDisplay get error !!"));
    }
    else if (NULL == VC_GetImage_Std )
    {
        bErrorFun = true;
        ErrorLog(QString("VC_GetImage get error !!"));
    }
    else if (NULL == VC_GetImageFile_Std )
    {
        bErrorFun = true;
        ErrorLog(QString("VC_GetImageFile get error !!"));
    }

    if(bErrorFun)
    {
        ErrorLog(QString("加载标准化视频设备动态库[%1]中的函数错误！").arg(m_sDriver));
        //m_hLibModule.unload();
        return false;
    }
    return  true;
}

bool CVideoCard::loadIpNc()
{
    VC_ConnectIPNC = (Proc_VC_ConnectIPNC)m_hLibModule.resolve("ConnectIPNC");
    VC_CaptureImage =(Proc_VC_CaptureImage)m_hLibModule.resolve("CaptureImage");
    VC_DisConnectIPNC =(Proc_VC_DisConnectIPNC)m_hLibModule.resolve("DisConnectIPNC");

    QString sError;
    if(!VC_ConnectIPNC){
        sError =QString("VC_ConnectIPNC,");
    }
    if(!VC_CaptureImage)
    {
        sError =QString("VC_CaptureImage,");
    }
    if(!VC_DisConnectIPNC){
        sError=QString("VC_DisConnectIPNC");
    }
    if(sError.length()>0){
        ErrorLog(QString("加载IPNC视频设备动态库[%1]中的函数错误,Error:%2").arg(m_sDriver).arg(sError));
        return false;
    }
    DebugLog(QString("加载IPnc视频卡动态库%1成功").arg(m_sDriver));
    return true;
}

bool CVideoCard::LoadVideoCardDll()
{
    VC_Init = (Func_VC_Init)m_hLibModule.resolve("VC_Init");
    VC_Close = (Func_VC_Close)m_hLibModule.resolve("VC_Close");
    VC_Start = (Func_VC_Start)m_hLibModule.resolve("VC_Start");
    VC_SaveAsBitmap = (Func_VC_SaveAsBitmap)m_hLibModule.resolve("VC_SaveAsBitmap");


    if (NULL == VC_Init ||
            NULL == VC_Close ||
            NULL == VC_Start ||
            NULL == VC_SaveAsBitmap)
    {
        ErrorLog(QString("加载视频卡动态库[%1]中的函数错误！").arg(m_sDriver));
        return false;
    }
    DebugLog(QString("视频卡动态库%1加载成功").arg(m_sDriver));
    return true;
}

//视频设备不使用函数启动设备
bool CVideoCard::StartDev()
{
    if (!m_bDriverLoaded)
    {
        return false;
    }
    return true;
}

void CVideoCard::CloseDev()
{
    try {
        // 先停止线程，避免在关闭设备时线程仍在运行
        if (m_pCapThread) {
            m_pCapThread->StopThread();
        }
        
        if (!m_bDriverLoaded) {
            return;
        }
        
        // 关闭设备
        Close();
        
    } catch (...) {
        ErrorLog("关闭视频采集设备时发生异常");
    }
}

bool CVideoCard::Init(HWND ShowWndHandled, qint32 nDisLeft, qint32 nDisTop, qint32 nDisWidth, qint32 nDisHeight, qint32 nCapWidth, qint32 nCapHeight)
{
    if (m_bInited)
    {
        return true;
    }
    if (!LoadDriver())
    {
        return false;
    }

    m_hDisplayWnd       = ShowWndHandled;
    m_nDisplayWidth     = nDisWidth;
    m_nDisplayHeight    = nDisHeight;
    m_nCapWidth     = nCapWidth;
    m_nCapHeight    = nCapHeight;
    qint32 nRet     = 0;
    m_bVideoDisplay = false;

    if(1==m_bStd){
        if(VC_Init_Std){
            nRet = VC_Init_Std(m_btype,m_sConnStr1.toLocal8Bit().data());
            if(nRet>0){
                DebugLog(QString("标准化视频初始化成功"));
                m_nHandle=nRet;

            }else{
               DebugLog(QString("标准化视频初始化失败，返回值=%1").arg(nRet));
               return false;
            }
        }
    }else if (0==m_bStd){
        if(NULL != VC_Init)
        {
            nRet=VC_Init(ShowWndHandled,
                          m_nDisplayWidth, m_nDisplayHeight, m_nCapWidth, m_nCapHeight);
        }

        if (0!=nRet)
        {
            DebugLog(QString("视频采集卡初始化失败，返回值=%1").arg(nRet));
        }
        else
            DebugLog("视频采集卡初始化成功");
    }else if(2==m_bStd){
        if(VC_ConnectIPNC){
            nRet =VC_ConnectIPNC(ShowWndHandled,m_sConnStr1.toLocal8Bit().data(),nDisLeft,nDisTop,nDisWidth,nDisHeight,true);
                DebugLog(QString("ConncectIPNC,返回值 %1").arg(nRet));
            if(0!=nRet){
                DebugLog(QString("IpNc 视频初始化失败"));
                return false;
            }else{
                DebugLog("IPNC 初始化成功");
            }
        }
    }
    DebugLog(QString("初始化视屏卡结果为：%1").arg(nRet));
    m_bInited=true;
    return true;
}

bool CVideoCard::Display()
{
    m_bVideoDisplay = false;
    if (!m_bInited){
        DebugLog("视频接口未初始化成功，无法打开显示");
        return false;
    }

    if(1==m_bStd){
        if(VC_StartDisplay_Std&&(0==VC_StartDisplay_Std(m_nHandle,m_nDisplayWidth,m_nDisplayHeight,qint32(m_hDisplayWnd)))){
            DebugLog(QString("标准化视频初始化成功"));            
        }else{
            DebugLog(QString("标准化视频初始化成功,显示失败"));
        }
    }else if(0==m_bStd)
    {
        qint32 nRet=VC_Start(0);
        DebugLog(QString("启动视频采集返回值=%1").arg(nRet));
        if (0!=nRet)
        {
            DebugLog(QString("启动视频采集卡失败-%1").arg(nRet));
        }

    }else if(2==m_bStd){

    }
    m_bVideoDisplay = true;
    return true;
}

//关闭显示
bool CVideoCard::StopDisplay()
{
    if (!m_bInited){
        DebugLog("视频接口未初始化成功，无法关闭显示");
        return false;
    }
    if (!m_bVideoDisplay){
        DebugLog("视频接口状态为未显示，无法关闭显示");
        return false;
    }

    if(1==m_bStd && VC_StopDisplay_Std) {
        qint32 nRet = VC_StopDisplay_Std(m_nHandle);
        DebugLog(QString("标准化视频关闭显示成功,返回：%1").arg(nRet));
    } else {
        DebugLog("非标准化视频接口，无法关闭显示");
    }
    m_bVideoDisplay = false;
    return true;
}


bool CVideoCard::Capture(QString &sJpgFileName)
{
    if(!m_bInited)
        return false;
    // 图像抓拍时的DateTime
    static time_t lastCapTime =0;
    time_t curTime = time(NULL);
    long res = qAbs(curTime-lastCapTime);
    if (0<res && res<1)
	{
		return m_bHasPreparedImg;
	}
    lastCapTime = curTime;

    /*char szTmpFileName[128]={0};
    char szPlate[32]={0};
    char szColor[8] ={0};

    if(0==VC_CaptureImage(szTmpFileName,szPlate,szColor)){
        sJpgFileName =QString::fromLocal8Bit(szTmpFileName);
        return true;
    }
    return false;
    */
    // 保存抓拍时间
    char szCapFileName[256]={0};
    m_CapTime=QDateTime::currentDateTime();
    if(1==m_bStd){
        RemoveFile(m_DefaultJpgFileName);
        
        if(0==VC_GetImageFile_Std(m_nHandle,0,m_DefaultJpgFileName.toLocal8Bit().data())){
            DebugLog(QString("标准化图片抓拍成功,%1").arg(m_DefaultJpgFileName));
            m_bHasPreparedImg=true;
            sJpgFileName = m_DefaultJpgFileName;
            return true;
        }else{
            sJpgFileName = "";
            DebugLog(QString("标准化图片抓拍失败"));
            m_bHasPreparedImg=false;
            return false;
        }
    }else if(0==m_bStd){
        RemoveFile(m_DefaultBmpFileName);
        qsnprintf(szCapFileName,sizeof szCapFileName,"%s",m_DefaultBmpFileName.toAscii().constData());
        if (0 == VC_SaveAsBitmap(szCapFileName)){
            DebugLog(QString("图片抓拍成功"));
            m_bHasPreparedImg=true;
            RemoveFile(m_DefaultJpgFileName);
            return SaveAsJpgFile(m_DefaultBmpFileName,m_DefaultJpgFileName);
        } else {
            sJpgFileName = "";
            DebugLog(QString("图片抓拍失败"));
            m_bHasPreparedImg=false;
            return false;
        }
    } else if(2==m_bStd){
        if(VC_CaptureImage){
            RemoveFile(m_DefaultJpgFileName);
            char szFileName[64]={0};
            qsnprintf(szFileName,sizeof szFileName,"%s",m_DefaultJpgFileName.toLocal8Bit().data());
            char szVLp[16]={0};
            char szColor[4]={0};
            VC_CaptureImage(m_DefaultJpgFileName.toLocal8Bit().data(),szVLp,szColor);
            m_bHasPreparedImg = true;
            sJpgFileName = QString::fromLocal8Bit(szFileName);
            return true;
        }else
            m_bHasPreparedImg = false;
    }
    return false;
}

bool CVideoCard::AutoCapture()
{
    QString sFileName;
    if(Capture(sFileName))
    {
        emit NotifyCaptureEvent(sFileName);
        return true;
    }
    return false;
}

void CVideoCard::NotifyToCapture()
{
    if(m_pCapThread)
        m_pCapThread->ResumeThread();
}

bool CVideoCard::Close()
{
    if (!m_bInited) {
        return true;
    }

    m_bInited = false;

    try {
        qint32 nRet = -1;
        if (1 == m_bStd) {
            if (VC_Deinit_Std) {
                nRet = VC_Deinit_Std(m_nHandle);
                DebugLog(QString("标准化视频设备关闭，返回值:%1").arg(nRet));
            } else {
                DebugLog("VC_Deinit_Std函数指针为空");
                return false;
            }
        } else if (0 == m_bStd) {
            if (VC_Close) {
                nRet = VC_Close();
                DebugLog(QString("非标准化视频设备关闭，返回值:%1").arg(nRet));
            } else {
                DebugLog("VC_Close函数指针为空");
            }
        } else if (2 == m_bStd) {
            if (VC_DisConnectIPNC) {
                nRet = VC_DisConnectIPNC();
                DebugLog(QString("IPNC视频设备断开连接，返回值:%1").arg(nRet));
            } else {
                DebugLog("VC_DisConnectIPNC函数指针为空");
            }
        }
    } catch (...) {
        ErrorLog("关闭视频设备时发生异常");
        return false;
    }

    m_bInited = false;
    return true;
}

bool CVideoCard::SaveCapFile()
{
    if(!m_bHasPreparedImg)
    {
        if(!AutoCapture())
            return false;
    }
    return true;
}

//取要保存的抓拍图片
bool CVideoCard::GetCapFileToLast(const QString &sFileName)
{
    RemoveFile(sFileName);
    if(!m_bHasPreparedImg){
        DebugLog(QString("当前无视频抓拍图片"));
        return false;
    }
    bool flag = Movefile(m_DefaultJpgFileName,sFileName);
    m_bHasPreparedImg = false;
    return flag;
}

bool CVideoCard::HasCapturedPic() const
{
    return m_bHasPreparedImg;
}

void CVideoCard::ClearCapturePic()
{
    m_bHasPreparedImg=false;
}

QString CVideoCard::GetPrepareFileName()
{
    if(m_bHasPreparedImg)
        return m_DefaultBmpFileName;
    else
        return QString("");
}

CCaptureThread::CCaptureThread(CVideoCard *pVideoCard,QObject *parent)
    :CPausableThread(parent),m_pVideoCard(pVideoCard)
{

    setObjectName(QString("CaptureThread"));
}

CCaptureThread::~CCaptureThread()
{

}

bool CCaptureThread::RunOnce()
{
    if(m_pVideoCard)
        m_pVideoCard->AutoCapture();
    this->PauseThread();
    return true;
}
