/***********定义计重相关的类型***************/
#ifndef WEIGHTTYPE_H
#define WEIGHTTYPE_H
#include <QtCore>
#include <QList>

//单个轴组重量
class CAxisData
{
public:
    CAxisData(){
        m_nAxisType=0;
        m_nAxisWeight=0;
        m_nSpeed=0;
    }
public:
    quint32 m_nAxisType;		//轴型
    qint32 m_nAxisWeight;		//原始轴重
    qint32 m_nSpeed;			//轴速
    bool operator ==(const CAxisData &CAxisData);
    bool operator !=(const CAxisData &CAxisData);
};


//单个车辆的轴组信息
class CVehAxisInfo
{
private:
    qint32 m_nVehSpeed;
    //原始轴重信息
    QList<CAxisData> m_RawAxisList;
    //最终确认的轴重信息
    QList<CAxisData> m_ConfirmedAxisList;
    //确认限载(人工修改过才有值，否则是0)
    quint32 m_nConfirmLimit;
    //车辆外轮廓尺寸(单位：毫米)
    qint32 m_nVehLength;
    qint32 m_nVehWidth;
    qint32 m_nVehHeight;
public:
    CVehAxisInfo()
    {
        Clear();
    }
public:
    //是否为空车
    bool IsNullVeh() const
    {
        return 0==m_ConfirmedAxisList.size();
    }
    //原始轴组组成
    quint32 GetRawAxisGroup() const
    {
        return GetAxisGroupOf(m_RawAxisList);
    }
    //最终确认的轴组组成
    quint32 GetConfirmedAxisGroup() const
    {
        return GetAxisGroupOf(m_ConfirmedAxisList);
    }
    //车速
    qint32 GetSpeed() const
    {
        return m_nVehSpeed;
    }
    //获取超限率
    qint32 GetOverWeightRate() const;
    //获取专项作业车的超限率
    qint32 GetOverWeightRateForSpecial() const;
    //原始重量
    qint32 GetRawTotalRawWeight() const
    {
        return GetRawWeightOf(m_RawAxisList);
    }
    //最终确认的重量
    qint32 GetConfirmedTotalRawWeight() const
    {
        return GetRawWeightOf(m_ConfirmedAxisList);
    }

    //原始单轴数(非轴组数)
    qint32 GetRawSingleAxisNum() const
    {
        return GetSingleAxisNumOf(m_RawAxisList);
    }
    //最终确认的单轴数(非轴组数)
    quint32 GetConfirmedSingleAxisNum() const
    {
        return GetSingleAxisNumOf(m_ConfirmedAxisList);
    }
    //原始轴组信息(发送报文用)
    void GetRawAxisInfo(QString &sAxisInfo) const
    {
        GetAxisInfoOf(sAxisInfo,m_RawAxisList);
    }
    //最终确认的轴组信息(发送报文用)
    void GetConfirmedAxisInfo(QString &sAxisInfo) const
    {
        GetAxisInfoOf(sAxisInfo,m_ConfirmedAxisList);
    }
    //最终确认的轴组信息(发送报文用)
    void GetAllConfirmedAxisInfoForTrans(QString &szAxisInfo, quint32 *pdwAxisGroup,
                                         qint32 *pnTotalFavWeight, qint32 *pnSingleAxisNum) const
    {
        GetAllAxisInfoForTransOf(szAxisInfo,pdwAxisGroup,
                                 pnTotalFavWeight, pnSingleAxisNum, m_ConfirmedAxisList);
    }
    //获取原始轴重信息
    void GetRawAxisList(QList<CAxisData> *pList) const
    {
        *pList=m_RawAxisList;
    }
    //获取最终确认的轴重信息
    void GetConfirmedAxisList(QList<CAxisData> *pList) const
    {
        *pList=m_ConfirmedAxisList;
    }
    //拆分车辆
    bool Split(CVehAxisInfo *pVeh1, CVehAxisInfo *pVeh2,quint32 dwFirstAxisGroup,
               qint32 nFirstWeight, quint32 dwSecondAxisGroup) const;
    //等值判断
    bool operator ==(const CVehAxisInfo &VehAxisInfo) const
    {
        return ((this->m_RawAxisList==VehAxisInfo.m_RawAxisList) &&
                (this->m_ConfirmedAxisList==VehAxisInfo.m_ConfirmedAxisList));
    }
    //是否能切割
    bool CanSplit() const
    {
        return this->GetConfirmedTotalRawWeight()>0;
    }

    //普通车限载
    quint32 GetWeightStandard() const;
    //专项作业车限载
    quint32 GetWeightStandardForSpecial() const;
    //获取车辆外轮廓尺寸信息(单位：毫米)
    qint32 GetVehLength() const
    {
        return m_nVehLength;
    }
    qint32 GetVehWidth() const
    {
        return m_nVehWidth;
    }
    qint32 GetVehHeight() const
    {
        return m_nVehHeight;
    }

    //设置车辆外尺寸信息（单位：毫米）
    void SetVehLength(qint32 nVehLength)
    {
        m_nVehLength = nVehLength;
    }
    void SetVehWidth(qint32 nVehWidth)
    {
        m_nVehWidth = nVehWidth;
    }
    void SetVehHeight(qint32 nVehHeight)
    {
        m_nVehHeight = nVehHeight;
    }

    void SetVehOutLine(qint32 nLength, qint32 nWidth, qint32 nHeight)
    {
        SetVehLength(nLength);
        SetVehWidth(nWidth);
        SetVehHeight(nHeight);
    }

    //设置车辆外轮廓尺寸信息（从分米转毫米）
    void SetVehOutlineFromDecimeter(qint32 nLength, qint32 nWidth, qint32 nHeight)
    {
        SetVehLength(nLength*100);
        SetVehWidth(nWidth*100);
        SetVehHeight(nHeight*100);
    }
    //获取车辆外轮廓尺寸信息(单位：米)
    QString GetVehLengthInMeter() const
    {
        return QString::number(m_nVehLength/1000.0, 'f', 2);
    }
    QString GetVehWidthInMeter() const
    {
        return QString::number(m_nVehWidth/1000.0, 'f', 2);
    }
    QString GetVehHeightInMeter() const
    {
        return QString::number(m_nVehHeight/1000.0, 'f', 2);
    }
public:
    //清空原有信息
    void Clear()
    {
        m_nVehSpeed=0;
        m_RawAxisList.clear();
        m_ConfirmedAxisList.clear();
        m_nConfirmLimit = 0;
        m_nVehLength = 0;
        m_nVehWidth = 0;
        m_nVehHeight = 0;
    }
    //增加原始轴信息
    void AddRawAxis(qint32 nAxisType, qint32 nAxisWeight, qint32 nAxisSpeed);
    //合并车辆
    void CombineVeh(const CVehAxisInfo &VehAxisInfo)
    {
        QList<CAxisData>::const_iterator it;
        for (it=VehAxisInfo.m_RawAxisList.begin();it!=VehAxisInfo.m_RawAxisList.end();it++)
        {
            this->m_RawAxisList.push_back(*it);
        }
        for (it=VehAxisInfo.m_ConfirmedAxisList.begin();it!=VehAxisInfo.m_ConfirmedAxisList.end();it++)
        {
            this->m_ConfirmedAxisList.push_back(*it);
        }
    }
    //修改轴组组成
    void ModifyAxisGroup(quint32 dwNewAxisGroup);
    //修改复称总重
    void SetVehTotalWeight(quint32 dwNewAxisGroup,qint32 nVehTotalWeight);
    //修改限载
    void SetVehLimitWeight(quint32 dwLimitWeight);
private:
    //获取轴组组成，例如125
    static quint32 GetAxisGroupOf(const QList<CAxisData> &AxisList);
    //获取原始总重
    static qint32 GetRawWeightOf(const QList<CAxisData> &AxisList);
    //获取轴数
    static qint32 GetSingleAxisNumOf(const QList<CAxisData> &AxisList);
    //获取轴组信息，轴型+轴组重量
    static void GetAxisInfoOf(QString &sAxisInfo,const QList<CAxisData> &AxisList);
    static void GetAllAxisInfoForTransOf(QString &szAxisInfo,quint32 *pdwAxisGroup,
                                         qint32 *pnTotalWeight, qint32 *pnSingleAxisNum,
                                         const QList<CAxisData> &AxisList);
    static void MakeAxisList(QList<CAxisData> *pAxisList, quint32 dwAxisGroup,
                             qint32 nFirstAxisRawWeight, qint32 nSpeed);
};
#endif // WEIGHTTYPE_H
