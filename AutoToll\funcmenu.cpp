﻿#include "funcmenu.h"

#include "MobilePayNet.h"
#include "authfm.h"
#include "cmonreqmgr.h"
#include "devicefactory.h"
#include "dlgmain.h"
#include "etclanectrl.h"
#include "forminputaxistype.h"
#include "forminputlimit.h"
#include "forminputsplitweight.h"
#include "forminputweight.h"
#include "globalutils.h"
#include "laneinfo.h"
#include "messagedialog.h"
#include "paramfile.h"
#include "paramfilemgr.h"
#include "sysparamdlg.h"
#include "utypereducefm.h"
#include "vehweightinfo.h"
#include "remotemsgmgr.h"
#include "remotecontrolmgr.h"

bool CFuncMenu::m_bViolateMenuShow = false;

CFuncMenu::CFuncMenu()
{
    if (Ptr_Info->IsETCLane()) {
        IsETCShow = true;

    } else {
        IsETCShow = false;
    }
    IsETCClose = false;
}

CFuncMenu::~CFuncMenu() {}

// 获取设置菜单的状态--是否可用
QList<bool> CFuncMenu::GetSettingItemsStatus()
{
    bool bUseCheckCard = false;
    bool bUseTollByVClass = false;
    bool bUseReset = true;
    bool bUseVPR = true;
    if (Ptr_Info->IsEntryLane()) bUseCheckCard = true;
    if (Ptr_Info->IsExitLane()) bUseTollByVClass = true;

    bool bUseLedTrans = true;
    if (Ptr_Info->IsETCLane()) {
        bUseLedTrans = false;
    }
    bool bIsFlag[SETITEM_COUNT] = {bUseCheckCard, bUseReset, bUseVPR, true, bUseLedTrans};

    QList<bool> list;
    list.clear();
    bool bStatus = false;
    for (int i = 0; i < SETITEM_COUNT; i++) {
        bStatus = bIsFlag[i];
        list.push_back(bStatus);
    }

    return list;
}
// 获取设置菜单的状态--是否可用
QList<bool> CFuncMenu::GetRSUItemsStatus()
{
    bool bUseRSU = Ptr_Info->IsETCLane();
    bool bIsFlag[RSUITEM_COUNT] = {bUseRSU, bUseRSU, bUseRSU, bUseRSU};

    QList<bool> list;
    list.clear();
    bool bStatus = false;
    for (int i = 0; i < RSUITEM_COUNT; i++) {
        bStatus = bIsFlag[i];
        list.push_back(bStatus);
    }

    return list;
}
// 选中后设置菜单
int CFuncMenu::SetSettingStatus(int nIndex)
{
    LaneEventID laneEvt = LEV_None;
    switch (nIndex) {
    case 0: {
        break;
    }
    case 1: {
        break;
    }
    case 2: {
        break;
    }
    case 3: {
        if (!Ptr_Info->IsETCLane()) {
            CDeviceFactory::GetLEDFareDisPlayer()->ShowWelcomeInfo(1);
            if (IsETCShow) {
                IsETCShow = false;
            } else {
                IsETCShow = true;
            }
        } else {
            if (IsETCClose) {
                CDeviceFactory::GetLEDFareDisPlayer()->ShowWelcomeInfo(1);
                IsETCClose = false;
            } else {
                CDeviceFactory::GetLEDFareDisPlayer()->ShowWelcomeInfo(0);
                IsETCClose = true;
            }
        }
        break;
    }
    case 4: {
        if (IsETCShow) {
            CDeviceFactory::GetLEDFareDisPlayer()->ShowWelcomeInfo(5);
            IsETCShow = false;
        } else {
            CDeviceFactory::GetLEDFareDisPlayer()->ShowWelcomeInfo(0);
            IsETCShow = true;
        }
        break;
    }
    default:
        break;
    }
    return laneEvt;
}
// 选中后RSU菜单
int CFuncMenu::SetRSUStatus(int nIndex)
{
    //    CMonReqID nMonReqId = mqUnKnow;
    LaneEventID laneEvt = LEV_None;

    return laneEvt;
}
// 获取设置功能菜单
void CFuncMenu::GetSettingItems(const QList<bool> &Statuslist, QList<CListData> &MenuList)
{
    MenuList.clear();
    bool bUseUncheckCard = false;
    bool bUseReset = false;
    bool bUseVPR = true;
    // bool bUseTollByVClass  = Ptr_Info->bChargeByVehClass();
    // bool bUseCardMgr = Ptr_Info->bUseAutoMach();
    bool bUse[SETITEM_COUNT] = {bUseUncheckCard, bUseReset, bUseVPR, IsETCClose, IsETCShow};

    int nStatSize = Statuslist.size();
    bool bStatus = true;
    CListData data;
    for (int i = 0; i < SETITEM_COUNT; i++) {
        if (SETITEM_COUNT == nStatSize)
            bStatus = Statuslist[i];
        else
            bStatus = true;

        data.nIndex = 0;
        data.sDispName = GetSettingMenuItems(i, bUse[i]);
        data.sMsg = data.sDispName;
        data.bIsEnable = Statuslist[i];

        MenuList.push_back(data);
    }
    return;
}
void CFuncMenu::GetRSUItems(const QList<bool> &Statuslist, QList<CListData> &MenuList)
{
    MenuList.clear();
    bool bUseRSU = Ptr_Info->IsETCLane();
    bool bUse[RSUITEM_COUNT] = {bUseRSU, bUseRSU, bUseRSU, bUseRSU};

    int nStatSize = Statuslist.size();
    bool bStatus = true;
    CListData data;
    for (int i = 0; i < RSUITEM_COUNT; i++) {
        if (RSUITEM_COUNT == nStatSize)
            bStatus = Statuslist[i];
        else
            bStatus = true;

        data.nIndex = 0;
        data.sDispName = GetRSUMenuItems(i, bUse[i]);
        data.sMsg = data.sDispName;
        data.bIsEnable = Statuslist[i];

        MenuList.push_back(data);
    }
    return;
}
int CFuncMenu::GetListFirstTrue(QList<CListData> *list)
{
    QList<CListData>::iterator it;
    int i = 0;
    for (it = list->begin(); it != list->end(); it++) {
        CListData data = *it;
        if (data.bIsEnable) return i;
        i++;
    }
    return 0;
}

void CFuncMenu::CreateLoginFunMenus(QList<CListData> &MenuItems)
{
    int len = m_FunMenuItems.size();
    for (int i = 0; i < len - 1; ++i) {
        CListData MenuItem = m_FunMenuItems.at(i);
        MenuItems.push_back(MenuItem);
    }
}

// 未登录状态下的功能菜单
void CFuncMenu::GetUnlogFuncMenuItems(QList<CListData> &MenuList)
{
    MenuList.clear();
    CListData data;
    for (int i = 0; i < UNLOGINMENU_COUNT; i++) {
        data.nIndex = 0;
        data.sDispName = GetUnLogItemName(i);
        data.sMsg = data.sDispName;
        data.bIsEnable = true;

        MenuList.push_back(data);
    }
    return;
}
void DoFuncMenu();
// 功能菜单
void CFuncMenu::GetFeaturesMenuItems(QList<CListData> &MenuList)
{
    MenuList.clear();
    CListData data;
    for (int i = 0; i < 4; i++) {
        data.nIndex = 0;
        data.sDispName = GetFeaturesItemName(i);
        data.sMsg = data.sDispName;
        data.bIsEnable = true;

        MenuList.push_back(data);
    }
    return;
}
// 维护菜单
void CFuncMenu::GetMaintenanceMenuItems(QList<CListData> &MenuList)
{
    MenuList.clear();
    CListData data;
    for (int i = 0; i < 4; i++) {
        data.nIndex = 0;
        data.sDispName = GetMaintenanceItemName(i);
        data.sMsg = data.sDispName;
        data.bIsEnable = true;

        MenuList.push_back(data);
    }
    return;
}

void CFuncMenu::GetEditWeightList(QList<CListData> &MenuList)
{
    MenuList.clear();
    return;
}

int CFuncMenu::DoLoginFuncMenu()
{
    QList<CListData> MenuItems;
    CreateLoginFunMenus(MenuItems);
    CListDlg dlg("功能菜单", "请按【数字】键选择，按【确定】键确认");
    dlg.ShowList(MenuItems, 0);
    int nResult = dlg.doModalShow();

    if (0 == nResult) return 0;

    int nLaneEvent = LEV_None;
    CMonReqID monReqId = mqUnKnow;
    if (FunMenu_Setting != nResult) {
        nLaneEvent = GetEventByMenuId(nResult, monReqId);
        if (!DoRoleAuth(monReqId)) {
            return 0;
        }
    }

    switch (nResult) {
    case FunMenu_Features:
        DoFeaturesFuncMenu();
        break;
    case FunMenu_Maintenance:
        DoMaintenanceFuncMenu();
        break;
    case FunMenu_System:
        ShowMenu_UnLogin();
        break;

    case FunMenu_EditerWeigh:
        break;
        // case FunMenu_Setting:
        // nResult=DoSettingMenu();
        // return nResult;
    case FunMenu_ReConnectReader:
        return ReConnReader();
    case FunMenu_ChangeCardBox:
        break;
    case FunMenu_ProcessLongCar:
        break;
    case FunMenu_ParamDownLoad:
        GetMainDlg()->ReqParamFun();
        break;
    case FunMenu_EditFlag:
        break;
    case FunMenu_RSU:
        nResult = DoRSUMenu();
        return nResult;
    case FunMenu_ParamVersionDetail:
        ShowParamVersionDetail();
        break;
    case FunMenu_BarrierUp:
        CDeviceFactory::GetIOCard()->SetWimBar(true);
        // case FunMenu_BarrierDown:
        // CDeviceFactory::GetIOCard()->SetWimBar(false);
        break;
    default:
        break;
    }
    //重新处理，不发送事件报文
    /*if(LEV_ReDo != nLaneEvent && nLaneEvent<LEV_ExEvent){
        Ptr_Ctrl->SaveLaneEventMsg(nLaneEvent);
    }*/
    return nLaneEvent;
}
void CFuncMenu::ShowParamVersionDetail()
{
    CParamInfoDlg paramInfoDlg;
    paramInfoDlg.InitUI();
    paramInfoDlg.ShowParamVersionDetail();
}

void CFuncMenu::DoFeaturesFuncMenu()
{
    // 初始化参数
    QList<CListData> list_appexit;
    GetFeaturesMenuItems(list_appexit);
    CListDlg dlg("功能", "请按【数字】键选择");
    dlg.ShowList(list_appexit, 0);
    list_appexit.clear();
    int nResult = dlg.doModalShow();

    while (0 != nResult) {
        switch (nResult) {
        case 1:  //导出未上传数据
        {
            ExportData();
            return;
        }
        case 2:  //导入参数
        {
            ImportParam();
            return;
        }
        case 3:  //查询参数情况
        {
            GetMainDlg()->CheckAndDownLoadParam();
            return;
        }
        case 4:  //查询流水情况
        {
            ShowWasteInfo();
            return;
        }
        default:
            break;
        }
        if (0 == nResult) return;
    }
}

void CFuncMenu::DoMaintenanceFuncMenu()
{
    // 初始化参数
    QList<CListData> list_appexit;
    GetMaintenanceMenuItems(list_appexit);
    CListDlg dlg("维护", "请按【数字】键选择");
    dlg.ShowList(list_appexit, 0);
    list_appexit.clear();
    int nResult = dlg.doModalShow();
    while (0 != nResult) {
        switch (nResult) {
        case 1:  //设置顶棚情报信息
        {
            return;
        }
        case 2:  //设置天线功率
        {
            SetRsuPower();
            return;
        }
        case 3:  //设置天线模式
        {
            // SetRsuModel();
            return;
        }
        case 4:  //上传日志
        {
            Ptr_ETCCtrl->UploadLog1();
            return;
        }
        default:
            break;
        }
        if (0 == nResult) return;
    }
}

void CFuncMenu::ShowMenu_UnLogin()
{
    // 初始化参数
    QList<CListData> list_appexit;
    list_appexit.push_back(CListData(0, "退出程序", "退出程序", 0, true, false));
    list_appexit.push_back(CListData(1, "重启程序", "重启程序", 0, true, false));
    list_appexit.push_back(CListData(2, "关闭计算机", "关闭计算机", 0, true, false));
    list_appexit.push_back(CListData(3, "重启计算机", "重启计算机", 0, true, false));

    CListDlg dlg("系统", "请按【数字】键选择");
    dlg.ShowList(list_appexit, 0);
    list_appexit.clear();
    int nResult = dlg.doModalShow();
    while (0 != nResult) {
        switch (nResult) {
        case 1:  // 退出程序
        {
            try {
                //                Ptr_Ctrl->SaveAppStarMsg(false);
                //                DelayTime(3000);
                GetMainDlg()->AppDestroy();
                DebugLog("执行退出程序操作！");
            } catch (...) {
                ErrorLog("退出程序:资源释放出异常!");
            }
            qApp->exit(RETCODE_QUIT);
            return;
        }
        case 2:  // 重启程序
        {
            try {
                //                Ptr_Ctrl->SaveAppStarMsg(false);
                //                GetMainDlg()->AppDestroy();
                DebugLog("执行程序重启操作！");
            } catch (...) {
                ErrorLog("重启程序:资源释放出异常!");
            }
            // SleeperThread::msleep(3000);
            qApp->exit(RETCODE_RESTART);
            return;
        }
        case 3:  // 关闭计算机
        {
            try {
                //                Ptr_ETCCtrl->SaveAppStarMsg(false);
                GetMainDlg()->AppDestroy();
                DebugLog("执行关闭计算机操作！");
            } catch (...) {
                ErrorLog("关闭计算机:资源释放出异常!");
            }

            ShutDownWindows();
            return;
        }
        case 4:  //重启计算机
        {
            try {
                //                Ptr_ETCCtrl->SaveAppStarMsg(false);
                GetMainDlg()->AppDestroy();
                DebugLog("执行重启计算机操作！");
            } catch (...) {
                ErrorLog("重启计算机:资源释放出异常!");
            }

            RestartWindows();
            return;
        }
        default:
            break;
        }

        if (0 == nResult) return;
    }
}

void CFuncMenu::ShowMenu_ChangeAxle()
{
    // 初始化参数
    VehWeightInfo::GetVehWeightInfo()->SetEditing(true);
    QList<CListData> lstMenuItem;
    lstMenuItem.push_back(CListData(0, "分离车辆", "分离车辆", 0,
                                    VehWeightInfo::GetVehWeightInfo()->CanSplit(), false));
    lstMenuItem.push_back(CListData(1, "合并车辆", "合并车辆", 0,
                                    VehWeightInfo::GetVehWeightInfo()->CanCombine(), false));
    lstMenuItem.push_back(CListData(2, "删除首辆车", "删除首辆车", 0,
                                    VehWeightInfo::GetVehWeightInfo()->GetVehCount() > 0, false));
    lstMenuItem.push_back(CListData(3, "删除全部车辆", "删除全部车辆", 0,
                                    VehWeightInfo::GetVehWeightInfo()->GetVehCount() > 0, false));
    lstMenuItem.push_back(CListData(4, "恢复车辆", "恢复车辆", 0,
                                    VehWeightInfo::GetVehWeightInfo()->CanRestore(), false));
    lstMenuItem.push_back(CListData(5, "增加空车", "增加空车", 0,
                                    VehWeightInfo::GetVehWeightInfo()->CanAddNullVeh(), false));
    lstMenuItem.push_back(CListData(6, "查看详细轴重信息", "查看详细轴重信息", 0,
                                    VehWeightInfo::GetVehWeightInfo()->GetVehCount() > 0, false));
    lstMenuItem.push_back(CListData(7, "修改轴重信息", "修改轴重信息", 0,
                                    VehWeightInfo::GetVehWeightInfo()->GetVehCount() > 0, false));
    lstMenuItem.push_back(CListData(8, "修改限载信息", "修改限载信息", 0,
                                    VehWeightInfo::GetVehWeightInfo()->GetVehCount() > 0, false));
    if(Ptr_Info->IsExitLane())
    {
        CWtSysDev_ZC *pDev = CDeviceFactory::GetWeightDev();
        if (pDev) {
            if (pDev->bUploadWeightAgain())
                lstMenuItem.push_back(CListData(9, "人工称重", "人工称重", 0, true, false));
        }
    }

    CListDlg dlg("修改计重信息", "请按【数字】键选择");
    dlg.ShowList(lstMenuItem, 0);
    int nResult = dlg.doModalShow();
    switch (nResult) {
    case 1:  //分离车辆
    {
        CVehAxisInfo vai;
        VehWeightInfo::GetVehWeightInfo()->GetFirstVeh(&vai);
        FormInputSplitWeight dlgSplit(GetMainDlg());
        if (dlgSplit.SplitVeh(vai.GetConfirmedAxisGroup(), vai.GetConfirmedTotalRawWeight())) {
            quint32 dwAxisGroup1 = 0, dwAxisGroup2 = 0;
            qint32 nWeight1;
            dlgSplit.GetSplitResult(&dwAxisGroup1, &nWeight1, &dwAxisGroup2);
            VehWeightInfo::GetVehWeightInfo()->SplitVeh(dwAxisGroup1, nWeight1, dwAxisGroup2);
        }
    } break;
    case 2:  //合并车辆
        VehWeightInfo::GetVehWeightInfo()->CombineFirst2Veh();
        break;
    case 3:  //删除首辆车
        VehWeightInfo::GetVehWeightInfo()->RemoveFirst();
        break;
    case 4:  //删除全部车辆
        VehWeightInfo::GetVehWeightInfo()->RemoveAll();
        break;
    case 5:  //恢复车辆
        VehWeightInfo::GetVehWeightInfo()->RestoreVeh();
        break;
    case 6:  //增加空车
        VehWeightInfo::GetVehWeightInfo()->AddNullVeh();
        break;
    case 7:  //查看详细轴重信息
        break;
    case 8:  //修改轴重信息
    {
        CVehAxisInfo vai;
        VehWeightInfo::GetVehWeightInfo()->GetFirstVeh(&vai);
        quint32 nAxisGroup = vai.GetConfirmedAxisGroup();
        FormInputAxisType dlgAxleInput(GetMainDlg());
        //先改轴组
        if (dlgAxleInput.EditAxisType(nAxisGroup)) {
            nAxisGroup = dlgAxleInput.GetInputResult();
            VehWeightInfo::GetVehWeightInfo()->ModifyAxisGroup(nAxisGroup);
        }
        //再改重量
        FormInputWeight dlgWeightInput(GetMainDlg());
        //先改轴组
        if (dlgWeightInput.EditWeight(vai.GetConfirmedTotalRawWeight())) {
            quint32 newWeight = dlgWeightInput.GetInputResult();
            VehWeightInfo::GetVehWeightInfo()->SetVehTotalWeight(nAxisGroup, newWeight);
        }
        break;
    }
    case 9:  //修改限载
    {
        FormInputLimit dlgLimitInput(GetMainDlg());
        if (dlgLimitInput.EditLimit(0)) {
            quint32 nLimit = dlgLimitInput.GetInputResult();
            VehWeightInfo::GetVehWeightInfo()->SetVehLimitWeight(nLimit);
        }
        break;
    }
    case 10: {
        CWtSysDev_ZC *pWeightDev = CDeviceFactory::GetWeightDev();
        if (pWeightDev) {
            pWeightDev->UploadVehWeightAgain();
        }
        break;
    }
    default:
        break;
    }
    VehWeightInfo::GetVehWeightInfo()->SetEditing(false);
    return;
}

//显示选择专项作业车及 货6的菜单
CVehClass CFuncMenu::ShowMenu_SelTruck6()
{
    // 初始化参数
    QList<CListData> lstMenuItem;
    lstMenuItem.push_back(CListData(0, "一类专项作业车", "一类专项作业车", 0, true, false));
    lstMenuItem.push_back(CListData(1, "二类专项作业车", "二类专项作业车", 0, true, false));
    lstMenuItem.push_back(CListData(2, "三类专项作业车", "三类专项作业车", 0, true, false));
    lstMenuItem.push_back(CListData(3, "四类专项作业车", "四类专项作业车", 0, true, false));
    lstMenuItem.push_back(CListData(4, "五类专项作业车", "五类专项作业车", 0, true, false));
    lstMenuItem.push_back(CListData(5, "六类专项作业车", "六类专项作业车", 0, true, false));
    lstMenuItem.push_back(CListData(6, "货6", "货6", 0, true, false));

    CListDlg dlg("选择车型", "请按【数字】键选择", false, true, GetMainDlg());
    dlg.ShowList(lstMenuItem, 0);
    int nResult = dlg.doModalShow();
    switch (nResult) {
    case 1:  //
        return VC_YJ1;
        break;
    case 2:
        return VC_YJ2;
        break;
    case 3:
        return VC_YJ3;
        break;
    case 4:
        return VC_YJ4;
        break;
    case 5:
        return VC_YJ5;
        break;
    case 6:
        return VC_YJ6;
        break;
    case 7:
        return VC_Truck6;
        break;
    }
    return VC_None;
}

int CFuncMenu::ShowMenu_OtherFree(CUnionVehType &vehType, bool bExit, bool bOnlyTruck)
{
    QList<CListData> lstMenuItem;
    qint32 nVehTypes[UVT_MAX];
    for (int i = 0; i < UVT_MAX; ++i) {
        nVehTypes[i] = i;
    }
    bool bEnabled = true;
    if (bOnlyTruck) bEnabled = false;

    lstMenuItem.push_back(CListData(0, GetUnionVehTypeName(UVT_YJVeh),
                                    GetUnionVehTypeName(UVT_YJVeh), (void *)&nVehTypes[UVT_YJVeh],
                                    bEnabled, false));
    lstMenuItem.push_back(CListData(1, GetUnionVehTypeName(UVT_EmergencyRescue),
                                    GetUnionVehTypeName(UVT_EmergencyRescue),
                                    (void *)&nVehTypes[UVT_EmergencyRescue], bEnabled, false));

    lstMenuItem.push_back(CListData(2, GetUnionVehTypeName(UVT_Emergency),
                                    GetUnionVehTypeName(UVT_Emergency),
                                    (void *)&nVehTypes[UVT_Emergency], bEnabled, false));

    lstMenuItem.push_back(CListData(3, GetUnionVehTypeName(UVT_CombinHarvester),
                                    GetUnionVehTypeName(UVT_CombinHarvester),
                                    (void *)&nVehTypes[UVT_CombinHarvester], bEnabled, false));

    lstMenuItem.push_back(CListData(4, GetUnionVehTypeName(UVT_TRUCK),
                                    GetUnionVehTypeName(UVT_TRUCK), (void *)&nVehTypes[UVT_TRUCK],
                                    true, false));

    lstMenuItem.push_back(CListData(5, GetUnionVehTypeName(UVT_Normal),
                                    GetUnionVehTypeName(UVT_Normal), (void *)&nVehTypes[UVT_Normal],
                                    true, false));

    if (bExit) {
        lstMenuItem.push_back(CListData(6, GetUnionVehTypeName(UVT_Vaccin),
                                        GetUnionVehTypeName(UVT_Vaccin),
                                        (void *)&nVehTypes[UVT_Vaccin], bEnabled, false));

    } else {
        lstMenuItem.push_back(CListData(6, GetUnionVehTypeName(UVT_Reverse),
                                        GetUnionVehTypeName(UVT_Reverse),
                                        (void *)&nVehTypes[UVT_Reverse], bEnabled, false));

        lstMenuItem.push_back(CListData(7, GetUnionVehTypeName(UVT_Holiday),
                                        GetUnionVehTypeName(UVT_Holiday),
                                        (void *)&nVehTypes[UVT_Holiday], bEnabled, false));
    }

    CListDlg dlg("其他免征", "请按<数字>键选择", false, true, GetMainDlg());
    dlg.ShowList(lstMenuItem, 0);
    int nResult = dlg.doModalShow();
    if (0 == nResult) return nResult;

    vehType = UVT_Normal;
    CListData *pSel = dlg.GetCurSelected(nResult);
    if (pSel) {
        if (pSel->pData) {  //普通车返回是0，所以不判断是否为0
            int nVehType = (int)*((int *)pSel->pData);
            vehType = (CUnionVehType)nVehType;
            return nResult;
        }
        // }
    }
    return 0;
}

CEntryQryResult *CFuncMenu::ShowMenu_qryEntryStation(QList<CEntryQryResult> &entryList)
{
    QList<CListData> lstMenuItem;
    QList<CEntryQryResult>::iterator it = entryList.begin();
    int nIndex = 0;
    for (; it != entryList.end(); ++it) {
        lstMenuItem.push_back(
                    CListData(nIndex, it->sEnStationName, it->sEnStationName, (void *)&(*it), true, false));
        nIndex++;
    }
    CListDlg dlg(QString("查询返回入口站"), "请按<数字>键选择", false, true, GetMainDlg());
    dlg.ShowList(lstMenuItem, 0);
    int nResult = dlg.doModalShow();
    if (0 == nResult) {
        return NULL;
    }
    CListData *pSel = dlg.GetCurSelected(nResult);
    if (pSel->pData) return (CEntryQryResult *)pSel->pData;
    return NULL;
}

/**
 * @brief
 * @param
 * @return 1-未补费 2-已补费
 */
int CFuncMenu::ShowMenu_PrePayBList()
{
    QList<CListData> lstMenuItem;
    lstMenuItem.push_back(CListData(0, QString("追缴名单车辆未补费"), QString(""), 0, true, false));
    lstMenuItem.push_back(CListData(1, QString("追缴名单车辆已补费"), QString(""), 0, true, false));
    CListDlg dlg(QString("追缴名单车辆"), QString("请按<数字>键选择"), false, true, GetMainDlg());
    dlg.ShowList(lstMenuItem, 0);

    RemoteMsgMgr::GetSingleInst()->SendLaneReportEventReq(1, CSpEventMgr::SpEvent_BlackCar,
                                                          QString("黑名单车辆"));
    //远控处理
    Ptr_RemoteCtrl->MenuSelectBegin("追缴名单车辆", lstMenuItem, true, &dlg,
                                    SLOT(OnMenuSelected(int)));
    int nRlt = dlg.doModalShow();
    Ptr_RemoteCtrl->MenuSelectEnd(&dlg, SLOT(OnMenuSelected(int)));

    return nRlt;
}

int CFuncMenu::ShowMenu_Reprint()
{
    QList<CListData> lstMenuItem;
    lstMenuItem.push_back(CListData(0, QString("重打不改变票号"), QString(""), 0, true, false));
    lstMenuItem.push_back(CListData(1, QString("重打增加票号"), QString(""), 0, true, false));
    CListDlg dlg(QString("重打发票"), QString("请按<数字>键选择"), false, true, GetMainDlg());
    dlg.ShowList(lstMenuItem, 0);
    int nRlt = dlg.doModalShow();
    return nRlt;
}

int CFuncMenu::GetEventByMenuId(int nMenuId, CMonReqID &monReqId)
{
    monReqId = mqUnKnow;
    LaneEventID laneEvt = LEV_None;
    switch (nMenuId) {
    case FunMenu_ReGranCard:  // 8 补卡
        monReqId = mqRepairCard;
        laneEvt = LEV_ReGrantCard;
        break;
    case FunMenu_EditerWeigh:  // 4 编辑称重信息
        monReqId = mqEditWeightInfo;
        laneEvt = LEV_EditWeight;
        break;
    case FunMenu_USelectStation:  // U型车选站
        break;
    case FunMenu_UCarReduce:  // 5 U型通行费减免
        monReqId = mqUFree;
        laneEvt = LEV_UFreeFee;
        break;
        // case FunMenu_ReConnectReader:     //
        break;
    case FunMenu_ReConnectPrinter:  //
        break;
    case FunMenu_ChangeBills:
        break;
    case FunMenu_ChangeCardBox:
        break;
    case FunMenu_ProcessLongCar:  // 10 超长车处理
        laneEvt = LEV_LongVeh;
        break;
    case FunMenu_DevTest:  //
        break;
    case FunMenu_EditFlag:
        laneEvt = LEV_EditFlag;
        break;
        // case FunMenu_ReconnectVLP:
        break;
    case FunMenu_ReconnectMobilePay:
        break;
    }
    return laneEvt;
}

int CFuncMenu::ReConnReader() { return 0; }

int CFuncMenu::DoSettingMenu()
{
    QList<bool> list_setstatus;
    list_setstatus = GetSettingItemsStatus();
    QList<CListData> list_setmenu;
    GetSettingItems(list_setstatus, list_setmenu);
    CListDlg dlgSetup("设置", "请按【数字】键选择");
    int i = GetListFirstTrue(&list_setmenu);
    dlgSetup.ShowList(list_setmenu, i);
    int nRet = dlgSetup.doModalShow();
    if (0 != nRet) {
        nRet = SetSettingStatus(nRet - 1);  // 选择之后设置变量
        list_setmenu.clear();
    }
    return nRet;
}
int CFuncMenu::DoRSUMenu()
{
    QList<bool> list_setstatus;
    list_setstatus = GetRSUItemsStatus();
    QList<CListData> list_setmenu;
    GetRSUItems(list_setstatus, list_setmenu);
    CListDlg dlgSetup("天线", "请按【数字】键选择");
    int i = GetListFirstTrue(&list_setmenu);
    dlgSetup.ShowList(list_setmenu, i);
    int nRet = dlgSetup.doModalShow();
    if (0 != nRet) {
        nRet = SetRSUStatus(nRet - 1);  // 选择之后设置变量
        list_setmenu.clear();
    }
    return nRet;
}
void CFuncMenu::UpDateFunMenuStatusByStateId(int /*nStateId*/)
{
    UpdateLoginFunMenuItemStatus(FunMenu_Setting, true);
    UpdateLoginFunMenuItemStatus(FunMenu_ReConnectReader, true);
    UpdateLoginFunMenuItemStatus(FunMenu_ReconnectVLP, true);
    UpdateLoginFunMenuItemStatus(FunMenu_ParamDownLoad, true);
    UpdateLoginFunMenuItemStatus(FunMenu_RSU, true);
    UpdateLoginFunMenuItemStatus(FunMenu_ParamVersionDetail, true);
    UpdateLoginFunMenuItemStatus(FunMenu_BarrierUp, true);
    UpdateLoginFunMenuItemStatus(FunMenu_BarrierDown, true);

    return;
}

void CFuncMenu::ShowUnloginFuncMenu()
{
    // 初始化参数
    QList<CListData> list_unlogin;
    GetUnlogFuncMenuItems(list_unlogin);
    CListDlg dlg("功能菜单", "请按【数字】键选择");
    dlg.ShowList(list_unlogin, 0);
    list_unlogin.clear();
    int nResult = dlg.doModalShow();
    while (0 != nResult) {
        switch (nResult) {
        case 1:  // 1-更新参数    //1-重启程序
        {
            CParamInfoDlg paramInfoDlg;
            paramInfoDlg.InitUI();
            paramInfoDlg.SetFuncMenu(true);

            nResult = paramInfoDlg.UpdateCfgFile();

            return;
        }
        case 2:  // 2-导出全部数据        // 2-重启车道机
        {
            // SysShutDown();
            return;
        }
        case 3:  // 3-只导出未上传的数据    3-重新初始化移动支付
        {
            return;

            /* TODO:
                    const CSysParamInfo *pParamInfo=CParamFileMgr::GetSysParamInfo();
                    Pos_LocalParams LocalParam;
                    memset(&LocalParam,0,sizeof (LocalParam));
                    LocalParam.nStationid=pParamInfo->m_nStationID;
                    LocalParam.nLaneType=Ptr_Info->GetLaneType();
                    LocalParam.nLaneId=Ptr_Info->GetLaneId();
                    Raw2HexStr(LocalParam.szTerminateNo,sizeof
                   LocalParam.szTerminateNo,Ptr_Ctrl->GetTermId(),6); if
                   (CDeviceFactory::GetPayMgr()){
                        if(!CDeviceFactory::GetPayMgr()->ConnectPos(LocalParam))
                        {
                            //Cmessagelabel::showError("连接POS机失败");
                            ErrorLog("重新连接POS机失败");
                        } else {
                            DebugLog("重新连接移动支付Pos成功");
                        }
                    }
                    return;
                    */
        }
        default:
            break;
        }

        if (0 == nResult) return;
    }
}

bool CFuncMenu::DoRoleAuth(int nLaneEvent)
{
    CAuthFm fm;
    COperInfo operInfo;
    bool bRet = fm.DoRoleAuth(nLaneEvent, operInfo);
    return bRet;
}

CFuncMenu *CFuncMenu::GetFunMenu(int nStateId)
{
    static CFuncMenu funcMenu;
    funcMenu.InitLoginFuncMenu();
    // funcMenu.UpDateFunMenuStatusByStateId(nStateId);
    return &funcMenu;
}

int CFuncMenu::DoRepayMenu()
{
    QList<CListData> RepayMenus;
    CListData MenuItem;
    MenuItem.nIndex = 0;
    MenuItem.sDispName = "逃费补票";
    MenuItem.sMsg = "逃费车辆补票";
    MenuItem.bIsEnable = true;
    RepayMenus.push_back(MenuItem);

    MenuItem.nIndex = 1;
    MenuItem.sDispName = "其他补票";
    MenuItem.sMsg = "其他情况补票";
    MenuItem.bIsEnable = true;
    RepayMenus.push_back(MenuItem);

    CListDlg listDlg("补费菜单", "前选择补费");
    listDlg.ShowList(RepayMenus, 0);
    return listDlg.doModalShow();
}

int CFuncMenu::DoViolateMenu(int nViolateType)
{
    m_bViolateMenuShow = true;
    QList<CListData> ViolateMenu;
    CListData MenuItem;
    MenuItem.nIndex = 0;
    MenuItem.sDispName = "闯关";
    MenuItem.sMsg = "车辆闯关离开";
    MenuItem.bIsEnable = true;

    ViolateMenu.push_back(MenuItem);
    MenuItem.nIndex = 1;
    MenuItem.sDispName = "误报警";
    MenuItem.sMsg = "设备误报警";
    MenuItem.bIsEnable = nViolateType == Violate_LoopTrigger;
    ViolateMenu.push_back(MenuItem);

    //    MenuItem.nIndex = 2;
    //    MenuItem.sDispName = "闯关被拦截";
    //    MenuItem.sMsg = "闯关车辆被拦截,可继续交费";
    //    MenuItem.bIsEnable =true;
    //    ViolateMenu.push_back(MenuItem);

    CListDlg listDlg("违章菜单", "", false, Violate_ViolateKey == nViolateType);
    listDlg.ShowList(ViolateMenu, 0);
    int nRlt = listDlg.doModalShow();
    m_bViolateMenuShow = false;
    //    switch (nRlt) {
    //        case 1:  return ViolateT_Violate;
    //        case 2:  return ViolateT_FalseAlarm;
    //    }
    return nRlt;
}

// CVehType CFuncMenu::DoLvTongMenu()
//{
//    QList<CListData> ltMenu;
//    CListData MenuItem;
//    MenuItem.nIndex =0;
//    MenuItem.sDispName =QString("省内绿通车");
//    MenuItem.bIsEnable = true;
//    MenuItem.sDispName = "省内绿通车辆";
//    ltMenu.push_back(MenuItem);

//    MenuItem.nIndex =1;
//    MenuItem.sDispName =QString("省外绿通车");
//    MenuItem.bIsEnable = true;
//    MenuItem.sDispName = "省外绿通车辆";
//    ltMenu.push_back(MenuItem);

//    CListDlg MenuDlg("绿通车","",false,true);
//    MenuDlg.ShowList(ltMenu,0);
//    int nRlt=  MenuDlg.doModalShow();
//    if(1==nRlt){
//        return VT_LvTongIn;
//    }else if(2==nRlt){
//        return VT_LvTongOut;
//    }else
//        return VT_None;
//}

int CFuncMenu::DoUSelectMenu(const QString &sTitle)
{
    CListData data;
    QList<CListData> list_data;
    data.nIndex = 0;
    data.sDispName = "U型车选站";
    list_data.append(data);
    data.nIndex = 1;
    data.sDispName = "U型车减免通行费";
    list_data.append(data);

    CListDlg dlg(sTitle, "请选择处理方式");
    dlg.ShowList(list_data, 0);
    int nResult = dlg.doModalShow();
    return nResult;
}

// 其他免征
// void CFuncMenu::GetOtherXParamMenu(int nLaneType,bool bPayState,QList<CListData> &MenuList)
//{
//    MenuList.clear();
//    CListData data;

//    bool bEnabled = true;
//    QString sMenuName;
//    for(int i=0; i<OTHERXMENU_COUNT; i++) {
//        switch (i) {
//        case 0:{
//            sMenuName= "救灾车";
//            bEnabled = true;
//            break;
//        }
//        case 1:{
//            sMenuName = "红十字会车";
//            bEnabled = true;
//            break;
//        }
//        case 2:{
//            sMenuName= "森林消防车";
//            bEnabled = true;
//            break;
//        }
//        case 3:{

//            sMenuName= "联合收割车";
//            bEnabled = true;
//            break;
//        }
//        case 4:{
//            sMenuName="跨境物流车";
//            bEnabled = false;//(Ptr_Info->IsMEExitLane()) && bPayState;
//            break;
//        }
//        case 5:{
//            sMenuName= "超限运输车";
//            bEnabled = false;//(Ptr_Info->IsMEExitLane()) && bPayState;
//            break;
//        }
//        case 6:{
//            sMenuName= "冷链运输车";
//            bEnabled = false; //(Ptr_Info->IsMEExitLane()) && bPayState;
//            break;
//        }
//        case 7:{
//            sMenuName= "甩挂运输车";
//            bEnabled= false; //(Ptr_Info->IsMEExitLane()) && bPayState;
//            break;
//        }
//        case 8:{
//            sMenuName= "应急救援车";
//            bEnabled = bPayState;
//            break;
//        }
//        case 9:{
//            sMenuName= "其他车";
//            bEnabled = true;
//            break;
//        }
//        default:
//            sMenuName="";
//            break;
//        }
//        data.nIndex = i;
//        data.sDispName = sMenuName;
//        data.bIsEnable = bEnabled;
//        MenuList.push_back(data);
//    }
//    return;
//}
// 车牌颜色
void CFuncMenu::GetVehPlateColorParamMenu(QList<CListData> &MenuList)
{
    MenuList.clear();
    CListData data;
    for (int i = 0; i < COLORMENU_COUNT; i++) {
        data.nIndex = i;
        data.sDispName = GetVehPlateColorMenuItems(i);
        data.bIsEnable = true;
        MenuList.push_back(data);
    }
    return;
}
// 车牌颜色
void CFuncMenu::GetVehClassParamMenu(QList<CListData> &MenuList)
{
    MenuList.clear();
    CListData data;
    for (int i = 0; i < CLASSMENU_COUNT; i++) {
        data.nIndex = i;
        data.sDispName = GetVehClassMenuItems(i);
        data.bIsEnable = true;
        MenuList.push_back(data);
    }
    return;
}
void CFuncMenu::InitLoginFuncMenu()
{
    m_FunMenuItems.clear();
    for (int i = FunMenu_None + 1; i <= FunMenu_End; ++i) {
        CListData MenuItem;
        MenuItem.nIndex = i;
        MenuItem.bIsEnable = true;
        MenuItem.bValue = false;
        MenuItem.sDispName = GetLogFunMenuItemName(i, MenuItem.bValue);
        m_FunMenuItems.push_back(MenuItem);
    }
    return;
}

void CFuncMenu::UpdateLoginFunMenuItemStatus(int nItemId, bool bEnable)
{
    m_FunMenuItems[nItemId - 1].bIsEnable = bEnable;
    return;
}

void CFuncMenu::UpdateLoginFunMenuItemValue(int nIndex, bool bValue)
{
    m_FunMenuItems[nIndex - 1].bValue = bValue;
    m_FunMenuItems[nIndex - 1].sDispName = GetLogFunMenuItemName(nIndex - 1, bValue);
}

///**
// * @brief  显示其他优惠车
// * @param vehType -当前输入的车种  sMsg-提示信息 nLaneType-车道类型 bPayState -是否为出口支付状态
// * @return 当前选中的车种 取消返回VT_Normal
// */
// CVehType CFuncMenu::ShowOtherX(CVehType VehType, const QString& sMsg,int nLaneType,bool
// bPayState)
//{
//    QList<CListData> list_loginmenu;
//    GetOtherXParamMenu(nLaneType,bPayState,list_loginmenu);
//    int nCurNo = GetSelectedIndex(VehType);
//    CListDlg dlg("其他优惠车", sMsg);
//    dlg.ShowList(list_loginmenu, nCurNo);
//    int nResult = dlg.doModalShow();
//    return VT_Normal;
//}
// 输入车种类型和提示信息
VP_COLOR CFuncMenu::ShowVehPlateColor()
{
    QString sMsg = "请按【数字】键选择确认";
    QList<CListData> list_loginmenu;
    GetVehPlateColorParamMenu(list_loginmenu);
    int nCurNo = 0;
    CListDlg dlg("车牌颜色菜单", sMsg);
    dlg.ShowList(list_loginmenu, nCurNo);
    int nResult = dlg.doModalShow();

    if (0 != nResult) {
        switch (nResult) {
        case 1:
            return VP_COLOR_BLUE;
        case 2:
            return VP_COLOR_YELLOW;
        case 3:
            return VP_COLOR_BLACK;
        case 4:
            return VP_COLOR_WHITE;
        case 5:
            return VP_COLOR_GREEN;
        case 6:
            return VP_COLOR_YELLOWGREEN;
        case 7:
            return VP_COLOR_BLUEWHITE;
        default:
            return VP_COLOR_NONE;
        }

    } else {
        return VP_COLOR_NONE;
    }
}
// 输入车种类型和提示信息
CVehClass CFuncMenu::ShowVehClass()
{
    QString sMsg = "请按【数字】键选择确认";
    QList<CListData> list_loginmenu;
    GetVehClassParamMenu(list_loginmenu);
    int nCurNo = 0;
    CListDlg dlg("车牌颜色菜单", sMsg);
    dlg.ShowList(list_loginmenu, nCurNo);
    int nResult = dlg.doModalShow();
    if (0 != nResult) {
        switch (nResult) {
        case 1:
            return VC_Car5;
        case 2:
            return VC_Car6;
        case 3:
            return VC_Truck6;
        case 4:
            return VC_YJ1;
        case 5:
            return VC_YJ2;
        case 6:
            return VC_YJ3;
        case 7:
            return VC_YJ4;
        case 8:
            return VC_YJ5;
        case 9:
            return VC_YJ6;
        case 10:
            return VC_Car1;
        case 11:
            return VC_Car2;
        case 12:
            return VC_Car3;
        case 13:
            return VC_Car4;
        case 14:
            return VC_Truck1;
        case 15:
            return VC_Truck2;
        case 16:
            return VC_Truck3;
        case 17:
            return VC_Truck4;
        case 18:
            return VC_Truck5;
        default:
            return VC_None;
        }
    } else {
        return VC_None;
    }
}
QString GetSettingMenuItems(qint8 nItemId, bool bIsUse)
{
    switch (nItemId) {
    case 0: {
        if (!bIsUse)
            return "启用前线圈(有车发卡)";
        else
            return "禁用前线圈(无车发卡)";
    }
    case 1: {
        if (!bIsUse)
            return "启用【模拟】键";
        else
            return "禁用【模拟】键";
    }
    case 2: {
        if (!bIsUse)
            return "启用车牌识别";
        else
            return "禁用车牌识别";
    }
    case 3: {
        if (!bIsUse)
            return "车道关闭";
        else
            return "车道开启";
    }
    case 4: {
        if (!bIsUse)
            return "ETC专用";
        else
            return "人工/ETC";
    }
    default:
        return "";
    }
}
QString GetRSUMenuItems(qint8 nItemId, bool bIsUse)
{
    switch (nItemId) {
    case 0: {
        bIsUse = true;
        return "天线重新连接";
    }
    case 1: {
        return "天线重新初始化";
    }
    case 2: {
        return "天线关闭";
    }
    case 3: {
        return "天线打开";
    }
    default:
        return "";
    }
}
QString GetLogFunMenuItemName(qint8 nItemId, bool bIsUse)
{
    switch (nItemId) {
    case FunMenu_Features:
        return QString("功能");
    case FunMenu_Maintenance:
        return QString("维护");
    case FunMenu_System:
        return QString("系统");
        /*case FunMenu_WasteDetail:
            return QString("查询流水情况");
        case FunMenu_SetInfo:
            return QString("设置顶棚情报信息");
        case FunMenu_SetRSUPower:
            return QString("设置天线功率");
        case FunMenu_SetRSUModel:
            return QString("设置天线模式");
        case FunMenu_Setting:               // 1 设置
            return QString("设备测试");
        case FunMenu_ReGranCard:            // 8 补卡
            return QString("补卡");
        case FunMenu_EditerWeigh:           // 4 编辑称重信息
            return QString("编辑称重信息");
        case FunMenu_USelectStation:         // U型车选站
            return QString("U型车选站");
        case FunMenu_UCarReduce:             // 5 U型通行费减免
            return QString("U型车减免通行费");
        case FunMenu_ReConnectReader:         //
            bIsUse=true;
            return QString("重连卡读写器");
        case FunMenu_ReConnectPrinter:        //
            return QString("重连打印机");
        case FunMenu_ChangeBills:
            return QString("更换票据");
        case FunMenu_ChangeCardBox:
            bIsUse=true;
            return QString("更换卡盒");
        case FunMenu_ProcessLongCar:            // 10 超长车处理
            return QString("超长车处理");
        case FunMenu_DevTest:
            return QString("设备测试");
        case FunMenu_EditFlag:
            return QString("编辑标识站");
        case FunMenu_ReconnectVLP:
            return QString("重连车牌识别");
        case FunMenu_ReconnectMobilePay:
            return QString("重连移动支付设备");
        case FunMenu_ParamDownLoad:
            return QString("重新下载参数");
        case FunMenu_RSU:
            bIsUse=true;
            return QString("天线设置");
        case FunMenu_ParamVersionDetail:
            return QString("查看参数版本");
        case FunMenu_BarrierUp:
            return QString("手动抬杆");
        case FunMenu_BarrierDown:
            return QString("手动落杆");*/
    default:
        return "";
    }
}

QString GetUnLogItemName(qint8 nItemId)
{
    switch (nItemId) {
    case 0:
        return "更新参数";
    case 1:
        return "导出全部数据";
    case 2:
        return "只导出未上传的数据";
    default:
        return "";
    }
}

QString GetFeaturesItemName(qint8 nItemId)
{
    switch (nItemId) {
    case 0:
        return "导出未上传数据";
    case 1:
        return "导入参数";
    case 2:
        return "查询参数情况";
    case 3:
        return "查询流水情况";
    default:
        return "";
    }
}
QString GetMaintenanceItemName(qint8 nItemId)
{
    switch (nItemId) {
    case 0:
        return "设置顶棚情报信息";
    case 1:
        return "设置天线功率";
    case 2:
        return "设置天线模式";
    case 3:
        return "上传上一天日志";
    default:
        return "";
    }
}

QString GetEditWeightItems(qint8 nItemId)
{
    switch (nItemId) {
    case 0:
        return "编辑当前称重信息";
    case 1:
        return "删除当前称重信息";
    case 2:
        return "切割当前称重信息";
    case 3:
        return "合并称重信息";
    case 4:
        return "插入称重信息";
    case 5:
        return "清空称重信息";
    default:
        return "";
    }
}

QString GetVehPlateColorMenuItems(qint8 nItemId)
{
    switch (nItemId) {
    case 0:
        return "蓝";
    case 1:
        return "黄";
    case 2:
        return "黑";
    case 3:
        return "白";
    case 4:
        return "渐变绿色";
    case 5:
        return "黄绿";
    case 6:
        return "蓝白";
    default:
        return "";
    }
}
QString GetVehClassMenuItems(qint8 nItemId)
{
    switch (nItemId) {
    case 0:
        return "客5";
    case 1:
        return "客6";
    case 2:
        return "货6";
    case 3:
        return "专1";
    case 4:
        return "专2";
    case 5:
        return "专3";
    case 6:
        return "专4";
    case 7:
        return "专5";
    case 8:
        return "专6";
    case 9:
        return "客1";
    case 10:
        return "客2";
    case 11:
        return "客3";
    case 12:
        return "客4";
    case 13:
        return "货1";
    case 14:
        return "货2";
    case 15:
        return "货3";
    case 16:
        return "货4";
    case 17:
        return "货5";
    default:
        return "";
    }
}
QString GetWeightMenuItems(qint8 nItemId)
{
    switch (nItemId) {
    case 0:
        return "修改当前称重信息";
    case 1:
        return "删除当前称重信息";
    case 2:
        return "切割当前称重信息";
    case 3:
        return "合并称重信息";
    case 4:
        return "插入称重信息";
    case 5:
        return "清空称重信息";
    default:
        return "";
    }
}

void ExportData()
{
    QList<QDataToSave> list;
    Ptr_ETCCtrl->GetDataMgr()->GetUnSendWaste(list);
    QString str = QString("%1export/").arg(GetCurrentPath());
    CreatePath(str);
    for (int i = 0; i < list.size(); i++) {
        const QDataToSave &data = list.at(i);
        QFile file(str + data.szAppFiles);
        QByteArray ba = uncompress(data.pMsg);
        if (file.open(QIODevice::WriteOnly)) {
            file.write(ba);
            file.close();
        }
    }
}

void ImportParam()
{
    ImportParamDlg dlg;
    dlg.ImportParam();
}

void ShowWasteInfo() {}

void SetRsuPower()
{
    RSUPowerDlg dlg;
    int nPower = dlg.GetRSUPower();
    CRsuDev *pRsuDev = NULL;
    for (int i = 0; i < 2; ++i) {
        pRsuDev = CDeviceFactory::GetRsuDev(i);
        if (!pRsuDev) continue;
        pRsuDev->SetRsuPower(nPower);
        pRsuDev->StartRsuDev(Ptr_Info->GetLaneType());
    }
}

void SetRsuModel()
{
    RSUModelDlg dlg;
    int nModel = dlg.GetRSUModel();
    for (int i = 0; i < 2; ++i) {
        CRsuDev *pRsuDev = CDeviceFactory::GetRsuDev(i);
        if (!pRsuDev) continue;
        // pRsuDev->SetRsuModel(nModel);
        pRsuDev->StartRsuDev(Ptr_Info->GetLaneType());
    }
}

int ImportParamDlg::ImportParam()
{
    InitUI();
    QLabel *pLabel = new QLabel("参数文件名:", this);
    pLabel->resize(100, 30);
    pLabel->move(40, 150);
    pLabel = new QLabel("参数编码:", this);
    pLabel->resize(100, 30);
    pLabel->move(55, 200);
    m_pText = new QLineEdit(this);
    m_pText->resize(200, 30);
    m_pText->move(120, 150);
    m_pText->installEventFilter(this);
    m_pTextCode = new QLineEdit(this);
    m_pTextCode->resize(200, 30);
    m_pTextCode->move(120, 200);
    m_pTextCode->installEventFilter(this);
    m_pBtn = new QPushButton(QString("选择文件"), this);
    m_pBtn->resize(80, 30);
    m_pBtn->move(350, 150);
    m_pBtn->installEventFilter(this);

    QObject::connect(m_pBtn, SIGNAL(clicked()), this, SLOT(SelectFileInfo()));
    return doModalShow();
}

int ImportParamDlg::mtcKeyPressed(MtcKeyPressedEvent *mtcKeyEvent)
{
    if (mtcKeyEvent->isFuncKey() && (mtcKeyEvent->func() == KeyEsc)) {
        CBaseOpWidget::OnCancel();
    } else if (mtcKeyEvent->isNumKey()) {
        QString sText = m_pTextCode->text();
        mtcKeyEvent->setKeyType(KC_Number);
        sText.append(QString(QChar(mtcKeyEvent->ascii())));
        m_pTextCode->setText(sText);
    } else if (KeyDel == mtcKeyEvent->func()) {
        QString sText = m_pTextCode->text();
        mtcKeyEvent->setKeyType(KC_Number);
        if (sText.length() > 0) sText.remove(sText.length() - 1, 1);
        m_pTextCode->setText(sText);
    } else if (mtcKeyEvent->isFuncKey() && (mtcKeyEvent->func() == KeyConfirm)) {
        int nFileID = m_pTextCode->text().toInt();
        CBaseParamFile *pParamFile = CParamFileMgr::GetParamFileByFileID(nFileID);
        if (!pParamFile) {
            SetMessage("参数不存在");
            RefreshMessage();
            return -1;
        } else {
            QString sFileName = m_pText->text();
            QString sLocalPath = pParamFile->GetFilePath();
            if (FileExists(sFileName))
                CopyFile_Replace(sFileName, QString("%1/tmp.zip").arg(sLocalPath));
            else {
                SetMessage("文件不存在");
                RefreshMessage();
                return -1;
            }
        }
        QString sError;
        if (!CParamFileMgr::MoveTmpFileAndUnCompress(nFileID, true, QString("tmp.zip"), sError)) {
            SetMessage(sError);
            RefreshMessage();
            return -1;
        }
        if (!pParamFile->LoadLocalParamFile()) {
            SetMessage("参数加载失败");
            RefreshMessage();
            return -1;
        }
        m_pText->clear();
        m_pTextCode->clear();
        setModalResult(0);
    }
    return 0;
}

void ImportParamDlg::SelectFileInfo()
{
    QString sFileName = GetFileName(QString::fromUtf8("压缩文件(*.zip)"));
    m_pText->setText(sFileName);
}

int RSUPowerDlg::GetRSUPower()
{
    InitUI();
    QLabel *pLabel = new QLabel("天线功率:", this);
    pLabel->resize(100, 30);
    pLabel->move(55, 150);
    m_pText = new QLineEdit(this);
    m_pText->resize(200, 30);
    m_pText->move(135, 150);
    m_pText->installEventFilter(this);
    return doModalShow();
}

int RSUPowerDlg::mtcKeyPressed(MtcKeyPressedEvent *mtcKeyEvent)
{
    if (mtcKeyEvent->isFuncKey() && (mtcKeyEvent->func() == KeyEsc)) {
        CBaseOpWidget::OnCancel();
    } else if (mtcKeyEvent->isFuncKey() && (mtcKeyEvent->func() == KeyConfirm)) {
        QString sText = m_pText->text();
        if (!sText.isEmpty()) {
            int ret = sText.toInt();
            if (ret > 28)
                ret = 28;
            else if (ret <= 0)
                ret = 5;
            setModalResult(ret);
        }
    } else if (mtcKeyEvent->isNumKey()) {
        QString sText = m_pText->text();
        mtcKeyEvent->setKeyType(KC_Number);
        sText.append(QString(QChar(mtcKeyEvent->ascii())));
        m_pText->setText(sText);
    } else if (KeyDel == mtcKeyEvent->func()) {
        QString sText = m_pText->text();
        mtcKeyEvent->setKeyType(KC_Number);
        if (sText.length() > 0) sText.remove(sText.length() - 1, 1);
        m_pText->setText(sText);
    }
    return 0;
}

int RSUModelDlg::GetRSUModel()
{
    InitUI();
    QLabel *pLabel = new QLabel("天线模式:", this);
    pLabel->resize(100, 30);
    pLabel->move(55, 150);
    m_pComboBox = new QComboBox(this);
    m_pComboBox->addItem(QString("入口"));
    m_pComboBox->addItem(QString("出口"));
    m_pComboBox->resize(200, 30);
    m_pComboBox->move(135, 150);
    m_pComboBox->installEventFilter(this);
    return doModalShow();
}

int RSUModelDlg::mtcKeyPressed(MtcKeyPressedEvent *mtcKeyEvent)
{
    if (mtcKeyEvent->isFuncKey() && (mtcKeyEvent->func() == KeyEsc)) {
        CBaseOpWidget::OnCancel();
    } else if (mtcKeyEvent->isFuncKey() && (mtcKeyEvent->func() == KeyConfirm)) {
        int nIndex = m_pComboBox->currentIndex();
        if (0 == nIndex)
            setModalResult(3);
        else
            setModalResult(4);
    }
    return 0;
}

void CFuncMenu::ShowMenu_ChangeAxleNum()
{
    // 初始化参数
    VehWeightInfo::GetVehWeightInfo()->SetEditing(true);
    QList<CListData> lstMenuItem;
    lstMenuItem.push_back(CListData(0, "修改轴型信息", "修改轴型信息", 0,
                                    VehWeightInfo::GetVehWeightInfo()->GetVehCount() > 0, false));

    CListDlg dlg("修改轴型信息", "请按【数字】键选择");
    dlg.ShowList(lstMenuItem, 0);
    int nResult = dlg.doModalShow();
    switch (nResult) {
    case 1:  //修改轴重信息
    {
        CVehAxisInfo vai;
        VehWeightInfo::GetVehWeightInfo()->GetFirstVeh(&vai);
        quint32 nAxisGroup = vai.GetConfirmedAxisGroup();
        FormInputAxisType dlgAxleInput(GetMainDlg());
        //先改轴组
        if (dlgAxleInput.EditAxisType(nAxisGroup)) {
            nAxisGroup = dlgAxleInput.GetInputResult();
            VehWeightInfo::GetVehWeightInfo()->ModifyAxisGroup(nAxisGroup);
        }
        break;
    }
    default:
        break;
    }
    VehWeightInfo::GetVehWeightInfo()->SetEditing(false);
    return;
}
void CFuncMenu::ShowMenu_ChangeAxleNumEx()
{
    // 初始化参数
    VehWeightInfo::GetVehWeightInfo()->SetEditing(true);
    QList<CListData> lstMenuItem;
    lstMenuItem.push_back(CListData(0, "分离车辆", "分离车辆", 0,
                                    VehWeightInfo::GetVehWeightInfo()->CanSplit(), false));
    lstMenuItem.push_back(CListData(1, "合并车辆", "合并车辆", 0,
                                    VehWeightInfo::GetVehWeightInfo()->CanCombine(), false));
    lstMenuItem.push_back(CListData(2, "删除首辆车", "删除首辆车", 0,
                                    VehWeightInfo::GetVehWeightInfo()->GetVehCount() > 0, false));
    lstMenuItem.push_back(CListData(3, "删除全部车辆", "删除全部车辆", 0,
                                    VehWeightInfo::GetVehWeightInfo()->GetVehCount() > 0, false));
    lstMenuItem.push_back(CListData(4, "恢复车辆", "恢复车辆", 0,
                                    VehWeightInfo::GetVehWeightInfo()->CanRestore(), false));
    lstMenuItem.push_back(CListData(5, "增加空车", "增加空车", 0,
                                    VehWeightInfo::GetVehWeightInfo()->CanAddNullVeh(), false));
    lstMenuItem.push_back(CListData(6, "查看详细轴重信息", "查看详细轴重信息", 0,
                                    VehWeightInfo::GetVehWeightInfo()->GetVehCount() > 0, false));
    lstMenuItem.push_back(CListData(7, "修改轴组信息", "修改轴组信息", 0,
                                    VehWeightInfo::GetVehWeightInfo()->GetVehCount() > 0, false));
    lstMenuItem.push_back(CListData(8, "修改限载信息", "修改限载信息", 0,
                                    VehWeightInfo::GetVehWeightInfo()->GetVehCount() > 0, false));
    if(Ptr_Info->IsExitLane())
    {
        CWtSysDev_ZC *pDev = CDeviceFactory::GetWeightDev();
        if (pDev) {
            if (pDev->bUploadWeightAgain())
                lstMenuItem.push_back(CListData(9, "人工称重", "人工称重", 0, true, false));
        }
    }

    CListDlg dlg("修改计重信息", "请按【数字】键选择");
    dlg.ShowList(lstMenuItem, 0);
    int nResult = dlg.doModalShow();
    switch (nResult) {
    case 1:  //分离车辆
    {
        CVehAxisInfo vai;
        VehWeightInfo::GetVehWeightInfo()->GetFirstVeh(&vai);
        FormInputSplitWeight dlgSplit(GetMainDlg());
        if (dlgSplit.SplitVeh(vai.GetConfirmedAxisGroup(), vai.GetConfirmedTotalRawWeight())) {
            quint32 dwAxisGroup1 = 0, dwAxisGroup2 = 0;
            qint32 nWeight1;
            dlgSplit.GetSplitResult(&dwAxisGroup1, &nWeight1, &dwAxisGroup2);
            VehWeightInfo::GetVehWeightInfo()->SplitVeh(dwAxisGroup1, nWeight1, dwAxisGroup2);
        }
    } break;
    case 2:  //合并车辆
        VehWeightInfo::GetVehWeightInfo()->CombineFirst2Veh();
        break;
    case 3:  //删除首辆车
        VehWeightInfo::GetVehWeightInfo()->RemoveFirst();
        break;
    case 4:  //删除全部车辆
        VehWeightInfo::GetVehWeightInfo()->RemoveAll();
        break;
    case 5:  //恢复车辆
        VehWeightInfo::GetVehWeightInfo()->RestoreVeh();
        break;
    case 6:  //增加空车
        VehWeightInfo::GetVehWeightInfo()->AddNullVeh();
        break;
    case 7:  //查看详细轴重信息
        break;
    case 8:  //修改轴重信息
    {
        CVehAxisInfo vai;
        VehWeightInfo::GetVehWeightInfo()->GetFirstVeh(&vai);
        quint32 nAxisGroup = vai.GetConfirmedAxisGroup();
        FormInputAxisType dlgAxleInput(GetMainDlg());
        //先改轴组
        if (dlgAxleInput.EditAxisType(nAxisGroup)) {
            nAxisGroup = dlgAxleInput.GetInputResult();
            VehWeightInfo::GetVehWeightInfo()->ModifyAxisGroup(nAxisGroup);
        }
        break;
    }
    case 9:  //修改限载
    {
        FormInputLimit dlgLimitInput(GetMainDlg());
        if (dlgLimitInput.EditLimit(0)) {
            quint32 nLimit = dlgLimitInput.GetInputResult();
            VehWeightInfo::GetVehWeightInfo()->SetVehLimitWeight(nLimit);
        }
        break;
    }
    case 10: {
        CWtSysDev_ZC *pWeightDev = CDeviceFactory::GetWeightDev();
        if (pWeightDev) {
            pWeightDev->UploadVehWeightAgain();
        }
        break;
    }
    default:
        break;
    }
    VehWeightInfo::GetVehWeightInfo()->SetEditing(false);
    return;
}
