#include "parafileold.h"

#include <QSqlError>
#include <QSqlQuery>

SpParaTable::SpParaTable()
{
    QString sFieldNames = QString("paraid,value,verusetime,expiretime");
    InitFieldsInfo(sFieldNames);
    m_nStationId = 0;
    m_MaxFeeMap.clear();
}

bool SpParaTable::Separate_Key_Value(const QStringList &FieldValues, quint32 &Key, SpPara &value)
{
    QString sValue;
    if (!GetFiledValueByName("paraid", FieldValues, sValue)) return false;
    value.paraid = sValue.toInt();
    Key = value.paraid;
    if (!GetFiledValueByName("value", FieldValues, sValue)) return false;
    value.value = sValue;
    if (!GetFiledValueByName("verusetime", FieldValues, sValue)) return false;
    value.verusetime = sValue;
    if (!GetFiledValueByName("expiretime", FieldValues, sValue)) return false;
    value.expiretime = sValue;
    return true;
}

bool SpParaTable::MssqlSelect(QSqlDatabase *mssqlConn) { return true; }

/**
 * @brief 从特殊参数内读取开放式收费站信息。
 * @param
 * @return  如果存在 105 或109参数，则返回true 否则返回false
 */
bool SpParaTable::InitVirStationInfo(quint32 nStationId, QList<COpenGantryParam> &gantryHex,
                                     bool bNew)
{
    SpPara sPara;
    int nId = bNew ? 109 : 105;
    if (!FindKey(nId, sPara)) {
        return false;
    }
    m_nStationId = nStationId;
    QStringList StationList = sPara.value.split("|");
    if (StationList.isEmpty()) return true;
    QStringList::iterator it = StationList.begin();
    for (; it != StationList.end(); ++it) {
        QString sStationInfo = *it;
        QStringList VirStationList = sStationInfo.split("&");
        if (VirStationList.isEmpty()) continue;
        if (VirStationList.at(0) == QString::number(nStationId)) {
            VirStationList.removeAt(0);
            foreach (QString sHex, VirStationList) {
                if (bNew) {
                    COpenGantryParam param;
                    int nIndex = sHex.indexOf(QString("*"));
                    if (-1 == nIndex) {
                        param.sGantryHex = sHex;
                        param.nMaxTime = 30 * 60;
                        gantryHex.push_back(param);
                    } else {
                        param.sGantryHex = sHex.left(nIndex);
                        sHex.remove(0, nIndex + 1);
                        param.nMaxTime = sHex.toUInt() * 60;  //单位秒
                        gantryHex.push_back(param);
                    }
                } else {
                    COpenGantryParam param;
                    param.sGantryHex = sHex;
                    param.nMaxTime = 30 * 60;  // 30分钟
                    gantryHex.push_back(param);
                }
            }
            break;
        }
    }
    return true;  // gantryHex.size() > 0;
}

bool SpParaTable::InitVirStationInfo(quint32 nStationId, QStringList &GantryHexList, bool bNew)
{
    SpPara sPara;
    int nId = bNew ? 109 : 105;
    if (!FindKey(nId, sPara)) {
        return false;
    }
    m_nStationId = nStationId;
    QStringList StationList = sPara.value.split("|");
    if (StationList.isEmpty()) return true;
    QStringList::iterator it = StationList.begin();
    for (; it != StationList.end(); ++it) {
        QString sStationInfo = *it;
        QStringList VirStationList = sStationInfo.split("&");
        if (VirStationList.isEmpty()) continue;
        if (VirStationList.at(0) == QString::number(nStationId)) {
            VirStationList.removeAt(0);
            if (!bNew) {
                GantryHexList = VirStationList;
            } else {
                foreach (QString sHex, VirStationList) {
                    int nIndex = sHex.indexOf(QString("*"));
                    GantryHexList.push_back(sHex.left(nIndex));
                }
            }
            break;
        }
    }
    return true;  // GantryHexList.size() > 0;
}

bool SpParaTable::InitMaxFee()
{
    SpPara spPara;
    if (!FindKey(106, spPara)) {
        return false;
    }

    QStringList sValues = spPara.value.split("&");
    if (sValues.isEmpty()) {
        return false;
    }
    QMap<int, CMaxFeeInfo> maxFeeMap;
    foreach (QString str, sValues) {
        QStringList sValue = str.split("|");
        if (sValue.isEmpty() || sValue.size() < 2) continue;
        CMaxFeeInfo feeInfo;
        feeInfo.nVehClass = sValue.at(0).toInt();
        feeInfo.nMaxFee = sValue.at(1).toUInt();
        feeInfo.baseFee = 0;

        maxFeeMap.insert(feeInfo.nVehClass, feeInfo);  //(feeInfo.nVehClass,feeInfo);
    }

    if (maxFeeMap.isEmpty()) {
        DebugLog("特殊参数内不存在最大费率");
        return false;
    }

    /*
        if(!FindKey(107,spPara)){
            DebugLog("特殊参数没有查到基础费率标准");
        }else{
            sValues.clear();
            sValues = spPara.value.split("&");
            if(sValues.size()>0){
                foreach (const QString &str, sValues) {
                    QStringList sValue= str.split("|");
                    if(sValue.isEmpty() || sValue.size()<2){
                        continue;
                    }
                    int nVehClass = sValue.at(0).toInt();
                    double dBaseFee = sValue.at(1).toDouble();
                    QMap<int,CMaxFeeInfo>::iterator it= maxFeeMap.find(nVehClass);
                    if(it!= maxFeeMap.end()){
                        it.value().baseFee = dBaseFee;
                    }else{
                        DebugLog(QString("特殊参数内最大费额和基础费率不匹配,vehClass%1").arg(nVehClass));
                    }
                }
            }
        }*/

    QMap<int, CMaxFeeInfo>::iterator it = maxFeeMap.begin();
    while (it != maxFeeMap.end()) {
        QString sLog;
        sLog = QString("最大费率,%1,%2,%3,%4")
                   .arg(it.key())
                   .arg(it.value().nVehClass)
                   .arg(it.value().baseFee)
                   .arg(it.value().nMaxFee);
        DebugLog(sLog);
        it++;
    }

    QMutexLocker locker(&m_maxFeeMutex);
    m_MaxFeeMap.swap(maxFeeMap);
    return true;
}

bool ParseMinFeePercent(int nType, const QString &sParam, QList<CMinFeePerCent> &percentList)
{
    QStringList sValues = sParam.split("&");
    if (sValues.isEmpty()) {
        return false;
    }
    foreach (QString str, sValues) {
        QStringList sValue = str.split("|");
        if (sValue.isEmpty() || sValue.size() < 2) continue;
        CMinFeePerCent minFeePercent;

        minFeePercent.nType = nType;
        minFeePercent.nVehClass = sValue.at(0).toInt();
        QStringList sPertcents = sValue.at(1).split("*");
        if (sPertcents.size() < 4) continue;
        minFeePercent.LowPercent_CPC = qint32(sPertcents.at(0).toDouble() * 100);
        minFeePercent.HightPercent_CPC = qint32(sPertcents.at(1).toDouble() * 100);
        minFeePercent.LowPercent_ETC = qint32(sPertcents.at(2).toDouble() * 100);
        minFeePercent.HightPercent_ETC = qint32(sPertcents.at(3).toDouble() * 100);

        percentList.push_back(minFeePercent);
        /*
        minFeePercentMap.insert(minFeePercent.nVehClass,
                                minFeePercent);  //(feeInfo.nVehClass,feeInfo);
                                */
    }
    return percentList.size() > 0;
}

bool SpParaTable::InitMinFeePercent()
{
    SpPara spPara;
    if (!FindKey(110, spPara)) {
        return false;
    }

    QStringList sValues = spPara.value.split("&");
    if (sValues.isEmpty()) {
        return false;
    }
    QMap<int, CMinFeePerCent> minFeePercentMap;
    foreach (QString str, sValues) {
        QStringList sValue = str.split("|");
        if (sValue.isEmpty() || sValue.size() < 2) continue;
        CMinFeePerCent minFeePercent;

        minFeePercent.nVehClass = sValue.at(0).toInt();
        QStringList sPertcents = sValue.at(1).split("*");
        if (sPertcents.size() < 4) continue;
        minFeePercent.LowPercent_CPC = qint32(sPertcents.at(0).toDouble() * 100);
        minFeePercent.HightPercent_CPC = qint32(sPertcents.at(1).toDouble() * 100);
        minFeePercent.LowPercent_ETC = qint32(sPertcents.at(2).toDouble() * 100);
        minFeePercent.HightPercent_ETC = qint32(sPertcents.at(3).toDouble() * 100);

        minFeePercentMap.insert(minFeePercent.nVehClass,
                                minFeePercent);  //(feeInfo.nVehClass,feeInfo);
    }

    if (minFeePercentMap.isEmpty()) {
        DebugLog("特殊参数内不存在最大费率");
        return false;
    }

    QMap<int, CMinFeePerCent>::iterator it = minFeePercentMap.begin();
    while (it != minFeePercentMap.end()) {
        QString sLog;
        sLog = QString("最小费额参数,车型：%1,LowCPC%2,HighCPC%3,LowETC%4,HighETC:%5")
                   .arg(it.key())
                   .arg(it.value().LowPercent_CPC)
                   .arg(it.value().HightPercent_CPC)
                   .arg(it.value().LowPercent_ETC)
                   .arg(it.value().HightPercent_ETC);
        DebugLog(sLog);
        it++;
    }
    QMutexLocker locker(&m_minFeePercentMt);
    m_minFeePercentMap.swap(minFeePercentMap);
    return true;
}

bool SpParaTable::InitMinFeePercent_New()
{
    SpPara spPara;
    QList<CMinFeePerCent> percentList;
    if (FindKey(110, spPara)) {
        bool bRlt = ParseMinFeePercent(0, spPara.value, percentList);
        if (!bRlt) {
            DebugLog(QString("本省最小费额系数解析失败,%1").arg(spPara.value));
        }
    } else {
        DebugLog(QString("未查询到代码110的本省最小费额系数"));
    }

    SpPara spPara_InterProv;
    QList<CMinFeePerCent> interProvPercentList;
    if (FindKey(112, spPara_InterProv)) {
        bool bRlt = ParseMinFeePercent(1, spPara.value, interProvPercentList);
        if (!bRlt) {
            DebugLog(QString("跨省最下费额系数解析失败,%1").arg(spPara.value));
        } else {
            percentList.append(interProvPercentList);
        }
    } else {
        DebugLog(QString("未查询到代码112的跨省最小费额系数"));
    }

    QList<CMinFeePerCent>::iterator it = percentList.begin();
    while (it != percentList.end()) {
        QString sLog;
        sLog = QString("最小费额参数,type:%1,vehclass:%2,LowCPC:%3,HighCPC:%4,LowETC%5,HighETC:%6")
                   .arg(it->nType)
                   .arg(it->nVehClass)
                   .arg(it->LowPercent_CPC)
                   .arg(it->HightPercent_CPC)
                   .arg(it->LowPercent_ETC)
                   .arg(it->HightPercent_ETC);
        DebugLog(sLog);
        it++;
    }
    QMutexLocker locker(&m_minFeePercentMt);
    // m_minFeePercentMap.swap(minFeePercentMap);
    m_minFeePercentList.clear();
    m_minFeePercentList = percentList;
    return true;
}

bool SpParaTable::InitDifferentFeeParam()
{
    SpPara spPara;
    if (!FindKey(113, spPara)) {
        DebugLog(QString("特殊参数字典中不存在差异化参数113"));
        return false;
    }

    DebugLog(QString("差异化计费参数:%1,%2,%3")
                 .arg(spPara.value)
                 .arg(spPara.verusetime)
                 .arg(spPara.expiretime));
    QStringList sStationList;
    sStationList = spPara.value.split(QString("&"));
    if (sStationList.isEmpty()) {
        DebugLog(QString("差异化计费参数为空"));
        return false;
    }

    QMutexLocker locker(&m_differentFeeMt);
    m_differentFeePara = spPara;
    return true;
}

bool SpParaTable::IsLocalVirtual(const QString &sGantryHex)
{
    QMutexLocker locker(&m_CS);
    if (m_VirStationList.isEmpty()) return false;

    QStringList::iterator it = m_VirStationList.begin();
    for (; it != m_VirStationList.end(); ++it) {
        if (sGantryHex == *it) {
            return true;
        }
    }
    return false;
}

bool SpParaTable::QryMaxFee(int nVehClass, quint32 &nMaxFee, double &baseFee)
{
    QMutexLocker locker(&m_maxFeeMutex);
    if (m_MaxFeeMap.find(nVehClass) != m_MaxFeeMap.end()) {
        nMaxFee = m_MaxFeeMap[nVehClass].nMaxFee;
        baseFee = m_MaxFeeMap[nVehClass].baseFee;
        return true;
    }
    return false;
}

bool SpParaTable::QryMinFeePercent(bool bLocal, int nVehClass, CMinFeePerCent &minFeePercent)
{
    int nType = bLocal ? 0 : 1;
    bool bFinded = false;

    QMutexLocker locker(&m_minFeePercentMt);
    foreach (CMinFeePerCent value, m_minFeePercentList) {
        if (value.nType == nType && value.nVehClass == nVehClass) {
            minFeePercent = value;
            bFinded = true;
            break;
        }
    }


    if (bFinded) {
        DebugLog(QString("查询type:%1,vehclass:%2,最小费额系数,cpc:%3,%4,etc:%5,%6")
                     .arg(nType)
                     .arg(nVehClass)
                     .arg(minFeePercent.LowPercent_CPC)
                     .arg(minFeePercent.HightPercent_CPC)
                     .arg(minFeePercent.LowPercent_ETC)
                     .arg(minFeePercent.HightPercent_ETC));

        return true;
    }

    minFeePercent.nVehClass = nVehClass;
    minFeePercent.nType = nType;
    if (bLocal) {
        minFeePercent.LowPercent_CPC = 100;
        minFeePercent.LowPercent_ETC = 100;
    } else {
        minFeePercent.LowPercent_CPC = 90;
        minFeePercent.LowPercent_ETC = 90;
    }
    minFeePercent.HightPercent_CPC = 150;
    minFeePercent.HightPercent_ETC = 200;

    DebugLog(QString("查询type:%1,vehclass:%2,最小费额系数失败,取缺省值cpc:%3,%4,etc:%5,%6")
                 .arg(nType)
                 .arg(nVehClass)
                 .arg(minFeePercent.LowPercent_CPC)
                 .arg(minFeePercent.HightPercent_CPC)
                 .arg(minFeePercent.LowPercent_ETC)
                 .arg(minFeePercent.HightPercent_ETC));

    return true;
}

bool SpParaTable::IsDifferentFeeStation(quint32 nStationId)
{
    QString sStationId = QString::number(nStationId);
    QMutexLocker locker(&m_differentFeeMt);
    if (m_differentFeePara.value.isEmpty()) return false;
#ifndef QT_DEBUG
    QString sCurTime = QDateTime::currentDateTime().toString(QString("yyyy-MM-dd hh:mm:ss"));
    if (sCurTime < m_differentFeePara.verusetime || sCurTime > m_differentFeePara.expiretime)
        return false;
#endif

    int nIndex = m_differentFeePara.value.indexOf(sStationId);
    if (-1 == nIndex)
        return false;
    else
        return true;
}
