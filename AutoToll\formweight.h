#ifndef FORMWEIGHT_H
#define FORMWEIGHT_H

#include <QWidget>
#include <QTableWidget>
#include "weighttype.h"

//车辆计重队列信息
typedef struct {
    //车辆总重
    qint32 totalWeight;
    //轴数
    qint32 axleCount;

}VehWeightItem;

class FormWeight : public QWidget
{
    Q_OBJECT
public:
    explicit FormWeight(QWidget *parent = 0);
    ~FormWeight();
signals:

public slots:
private:
    //显示当前车辆计重信息
    QTableWidget *m_pTblWtInfo;
    //显示当前车辆轴信息
    QTableWidget *m_pTblAxleInfo;
    //车辆队列
    QList<VehWeightItem> m_lstVeh;
    //设备正常
    bool m_bWeightDevNormal;
    //车辆外轮廓尺寸检测仪状态: 0 - 无设备，1 - 正常，2 - 异常
    int m_nVehicleOutlineDevStatus;

private:
    //初始化当前车辆计重信息
    void InitWtInfoTable();
    //初始化轴组信息表格
    void InitAxleInfoTable();
    //根据轴数获取车辆外轮廓最大允许尺寸(单位：毫米)
    void GetMaxVehDimensions(int axisCount, int &maxLength, int &maxWidth, int &maxHeight);
protected:
    void paintEvent(QPaintEvent *);
public:
    void InitUI();
    //清除车辆显示
    void ClearWeightInfoShow();
    //增加车辆显示
    void AddVehWeightInfo(const VehWeightItem item);
    //显示当前车辆的计重, bSpecial:是否是专项作业车
    void ShowVehWeight(const CVehAxisInfo* pVehAxisInfo, bool bSpecial = false);
    //显示当前计重状态为正常
    void SetDevStatusNormal(bool bNormal);
    //设置车辆外轮廓尺寸检测仪状态
    void SetVehicleOutlineDevStatus(int nStatus);
};

#endif // FORMWEIGHT_H
