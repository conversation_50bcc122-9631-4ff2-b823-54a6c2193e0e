#ifndef LANEINFO_H
#define LANEINFO_H

#include <QObject>

#include "common/paramfile.h"
#include "common/secureverify.h"
#include "jxlanetype.h"

const int MAX_INPUT_AXIS_COUNT = 10;
const int WND_WIDTH = 511;
const int SCROLL_SCREEN_HIGHT = 60;
const int ENTRY_WASTEFM_HIGHT = 215;
const int EXIT_WASTEFM_HIIGHT = 143;
// define a retcode: 773 = 'r'+'e'+'s'+'t'+'a'+'r'+'t' = restart
static const int RETCODE_RESTART = 773;
static const int RETCODE_QUIT = 777;

class CLaneInfo
{
public:
    CLaneInfo(void);
    ~CLaneInfo(void);

public:
    static CLaneInfo *GetLaneInfo()
    {
        static CLaneInfo LaneInfo;
        return &LaneInfo;
    }
    QString GetCfgFileName() { return m_sFileName; }

public:
    void LoadLogFileInfo();
    bool ReadCfgFile(QString &sError);

    bool SaveCfgItem(const QString &sKey, bool bValue);
    bool SaveCfgItem(const QString sKey, const QString &sValue);
    quint8 GetLaneType() { return m_nLaneType; }
    //quint8 ConverLaneTypeToTrans(int nType = 3);
    quint8 ConverLaneTypeToTrans(int nType = 7);
    quint8 GetLaneSign() { return m_nLaneSign; }
    quint8 GetLaneId() { return m_nLaneId; }
    int GetCardMgrType() { return m_nCardMgrType; }

    QString GetLaneName();
    QString GetPicPath() { return m_sPicPath; }
    QString GetAppVer();
    QString GetTransVer() { return m_sTransVer; }
    QString GetSubVer();
    void CreateVersion(bool bTest);
    void CreateVersionNew(bool bTest, QString sMajorVersion, int nMinorVersion);

    bool GetReverDownBar() { return m_bReverDownBar; }
    bool GetFareShow() { return m_bFareShow; }
    bool bFrontBar() { return m_bFrontBar; }
    bool IsExitLane() { return LaneType_EtcExit == m_nLaneType || LaneType_MtcExit == m_nLaneType; }
    bool IsEntryLane()
    {
        return LaneType_EtcEntry == m_nLaneType || LaneType_MtcEntry == m_nLaneType ||
                LaneType_AutoLane == m_nLaneType;
    }
    bool IsETCLane() { return LaneType_EtcEntry == m_nLaneType || LaneType_EtcExit == m_nLaneType; }
    bool IsMTCLane()
    {
        return LaneType_MtcEntry == m_nLaneType || LaneType_MtcExit == m_nLaneType ||
                LaneType_AutoLane == m_nLaneType;
    }
    bool bHaveBackDev();
    bool bHaveFrontDev();

    void SetStationID(int nStationID);

    qint32 GetStationID() { return m_nStationID; }
    QString GetStationName() { return m_sStationName; }

    bool GetCheckOBUExpired() { return m_bCheckOBUExpired; }

    QString GetHexStationID() { return m_sHexStationID; }

    //取车道hex（带站代码hex）,使用时一定注意
    QString GetHexLaneID() { return m_sHexLaneId; }

    QString GetGBLaneId() { return m_sGBLaneId; }

    QString GetGBStationId() { return m_sGBStationId; }

    void SetGBStationInfo(const QString &sGBStationId, const QString &sGBLaneId);
    void SetStationName(const QString &sStationName);

    QString GetFeeDllCurPath() { return m_sFeeDllCurPath; }
    void SetFeeDllCurPath(const QString &feeDllCurPath);

    void GetParaUrl(QString &sUrl, QString &sBinFileAuth);
    void GetDataUrl(QString &sDataUrl, QString &sBinFileAuth, QString &sBakUrl);

    QString GetDBPath() { return m_sDBPath; }
    QString GetBakDBPath() { return m_sBakDBPath; }
    qint32 GetLogLevel() { return m_nLogLevel; }
    QString GetLogPath() { return m_sLogPath; }

    bool bCheckVehInfo() { return m_bCheckVehInfo; }

    bool bCheckOutTime() { return m_bCheckOutTime; }
    bool bCheckLocal() { return m_bCheckLocal; }
    bool bCheckCardBList() { return true; }  // m_bCheckCardBList; }
    bool bCheckSameVeh() { return m_bCheckSameVeh; }
    bool bCheckExistLoop() { return m_bCheckExitLoop; }

    qint32 GetVehClassDef(int nPos);

    QString GetFeeVersion() { return m_sFeeVersion; }
    void SetFeeVersion(QString sVersion) { m_sFeeVersion = sVersion; }

    QString GetDefaultPlate() { return m_sDefaultPlate; }
    inline void SetDefaultPlate(QString sDefaultPlate) { m_sDefaultPlate = sDefaultPlate; }

    bool bAllowDisOBU() { return m_bAllowDisOBU; }

    bool bUseStdVPR() { return m_bUseStdVPR; }

    bool bCheckMinFee() { return m_bCheckMinFee; }

    bool bOpenLane() { return m_bOpenLane; }

    void SetOrgCode(const COrgCode &OrgCode, const QString &sRoadName);
    int GetBL_SubCenter();
    int GetBL_Center() { return 3600000; }
    QString GetRoadName() { return m_sRoadName; }

    inline bool IsLockWindowsOnTop() { return m_bLockWindowsOnTop; }
    inline bool IsShowMouse() { return m_bShowMouse; }

    void GetURLParams(URLParams &urlparam) { urlparam = m_sURLParams; }

    bool GetCapture() { return m_capture; }

    qint32 GetLoogNum() { return m_LoopNum; }
    quint8 GetUTimes() { return m_nUTimes; }

    bool GetIsCLear() { return m_isClear; }
    QString getNtpHost() { return m_sNTPHost; }
    quint32 getNtpPort() { return m_nNTPPort; }
    quint32 getNtpTime() { return m_nNTPTime; }
    QString getOpenLaneIPPort() { return m_sOpenLaneIPPort; }

    void GetVlprSmInfo(QString &sBatchNo, int &vehicleDataCount, int &vehiclePicCount);
    void SetVlprSmInfo(const QString &sBatchNo, const int &vehicleDataCount,
                       const int &vehiclePicCount);
    quint32 GetLaneLen() { return m_nLanelen; }
    bool GetCheckConVeh() { return m_bCheckConVeh; }

    QDate GetUploadLogDate();
    void SetUploadLogDate(QDate date);

    void AddVirStationInfo(const CVirGantryInfo &virStaInfo);
    void AddOpenStationInfo(const QList<CVirGantryInfo> gantryList);
    bool IsUVeh(const QString &sHex);
    quint32 GetDetectTimes() { return m_dwDetectTime; }
    bool IsCheckVehInQue() { return m_bCheckVehInQue; }

    QString GetWindowsVer() { return m_sSysTemVer; }

    bool IsDeleteReservVeh() { return m_bDeleteReservVeh; }
    bool GetManualCapture() { return m_bManualCapture; }

    QString GetRSUComm() { return m_sRSUComm; }
    QString GetRSUPower() { return m_sRSUPower; }
    QString GetRSUChannelID() { return m_sRSUChannelID; }

    //入口时间是否使用虚拟站的入口时间，缺省是
    bool bUseOpenTime() { return m_bUseOpenTime; }

    QString GetReplaceGantryHex() { return m_sReplaceGatryHex; }
    QString GetReplaceGantryID() { return m_sReplaceGatryID; }
    void SetReplaceGantryHex(const QString &sHex) { m_sReplaceGatryHex = sHex; }

    bool CheckReplaceWriteCard();  //是否需要代写入口信息

    int GetRsuNum() { return m_nRsuNum; }
    bool bHaveCardMgr() { return m_bhaveCardMgr; }
    void MakeCardMgrEnabled(bool enabled) { m_bCardMgrEnabled = enabled; }
    bool bCardMgrEnabled() { return m_bCardMgrEnabled; }

    QString GetFeeUrl() { return m_sFeeUrl; }
    QString GetEntryUrl() { return m_sEntryUrl; }
    QString GetFlagUrl() { return m_sFlagUrl; }

    bool bRemoveBakFile() { return m_bRemoveBakFile; }
    bool bEticket() { return m_eTick; }
    int GetMaxOutTime() { return m_nOutTime; }
    bool bStartScreenUI() { return m_bStartScreenUI; }
    QString GetScreenUIPath() { return m_sScreenUIPath; }

    bool bNoWeightDev();
    int GetMaxDriveDays();
    bool bCheckWeight();

    bool bRemoteControl() { return m_bRemoteControl; }
    QString GetLocalIP() { return m_sLocalIP; }
    QString GetRtmpIP() { return m_sRtmpIP; }
    qint32 GetLocalPort() { return m_nLocalPort; }

    QString GetRemoteServerIP() { return m_sRemoteServer; }
    qint32 GetRemotePort() { return m_nRemotePort; }
    QString GetRemoteToken() { return m_sRemoteControlToken; }
    bool bRemoteCloseLaneVideo() { return m_bRemoteCloseLaneVideo; }

    bool CheckVehPassPermit(int nVehClass, const QDateTime &dateTime);
    bool bGrantPaperCard() { return m_bPaperCard; }
    QString GetUpLoadLogUrl() { return m_sUpLoadLogUrl; }
    bool bUseVehLib() { return m_bUseVehLib; }
    QString GetVehLibUrl() { return m_sVehLibUrl; }
    void GetTokenUrl(QString &sQryUrl, QString &sRefreshUrl, QString &sSaveUrl);
    int GetVehLibScore(int type=0) {
        if (type==1)
        {//入口
            return m_nScoreEN;
        }
        else if (type == 2)
        {//出口
            return m_nScoreEX;
        }
        else
            return m_nScore;
    }
    int GetMaxInterval() { return m_nMaxTimeInterval; }
    bool bStopUJ() { return m_bStopUJ; }
    bool bNewUJ() { return m_bNewUJ; }
    int GetMinFlagCnt() { return m_nFlagCnt; }

    void GetMaxLoopTime(int &nMaxOn, int nMaxOff);
    bool RelocatedCard() { return m_bRelocatedCard; }
    int GetTmpFileTime() { return m_nTmpFileMaxTime; }
    bool bETCStopTruck();
    bool bFuzztMatch();
    bool bReplaceByVcr() { return m_bReplaceByVcr; }
    bool bTestPaper() { return m_bTestPaper; }
    QString GeteETCDelayUrl() { return m_sETCDelayUrl; }
    bool bAutoDelay();
    //使用车型识别的结果作为抓拍结果（针对前天线车辆）
    bool bUseVcrResult() { return m_bUseVcrResult; }

    int GetVehDetectTime() { return m_nVehDetectTime; }

    // 新增：是否禁用从车型设备队列获取车牌信息
    bool bDisableVcrQueuePlate() { return m_bDisableVcrQueuePlate; }

    quint8 GetLaneTypeHT() { return m_nLaneTypeHT; }
    quint32 GetMaxTimeOnBackLoop() { return m_nMaxTimeOnBackLoop; }
    QString GetProDebPaymentUrl() { return m_sProDebPaymentUrl; }

    quint32 GetCardTimerInterval() { return m_nCardTimerInterval;}

    // 获取卡头伸缩配置：1-依据车型判断，0-只要有线圈信号就伸出
    int GetCardHeadStretchConfig() { return m_nCardHeadStretchConfig; }

private:
    QString GetSysVersion();

private:
    QString m_sFileName;

    QString m_sHexStationID;  //本站16进制编码，带网络号 "3601"+4字节16进制字符串，大写字母
    QString m_sHexLaneId;  //车道16进制字符串,站代码+车道代码，大写字母

    quint8 m_nLaneType;  //车道类型 1-Me入，2-me出3-ETC入口 4-ETC出口
    quint8 m_nLaneSign;  //入出口类型1-入口 2-出口
    quint8 m_nLaneId;    //车道代码

    QString m_sPicPath;  //车牌识别图像保存路径，抓拍图片保存路径在videocard里设置
    qint32 m_nStationID;     //收费站代码
    QString m_sStationName;  //收费站名称
    QString m_sRoadName;     //路段名称
    QString m_sAppVer;       //程序版本号
    QString m_sTransVer;     //传输版本号
    QString m_sSubVer;       //子版本号，用于显示和日志

    QString m_sFeeDllCurPath;  //当前使用的费率计算动态库路径

    bool m_bLaneStats;     //当前车道状态，上班还是下班
    QString m_sDBPath;     //脱机数据库路径
    QString m_sBakDBPath;  //备份数据库路径
    qint32 m_nLogLevel;    //日志等级
    QString m_sLogPath;    //日志保存路径

    bool m_bCheckVehInfo;  //是否校验车卡绑定

    bool m_bCheckOutTime;    //是否判断过期卡
    bool m_bCheckLocal;      //判断非本区域卡
    bool m_bCheckCardBList;  //判断是否校验黑名单

    QString m_sDefaultPlate;  //默认车牌
    QString m_sFeeVersion;    //跨省费率计算版本号

    bool m_bAllowDisOBU;  //是否允许标签拆卸

    bool m_bOpenLane;   //是否处理开放式电子站
    bool m_bUseStdVPR;  //标准化抓拍

    COrgCode m_OrgCode;
    quint32 m_nGBCardBlackInterval;  //国标卡黑名单全量更新时间间隔,单位：小时
    quint32 m_nOtherParamInterval;   //国标卡黑名单增量更新时间间隔,单位：分钟

    bool m_bShowMouse;  //界面显示鼠标

    URLParams m_sURLParams;

    bool m_bLockWindowsOnTop;

    // http
    QString m_sParaUrl;
    QString m_sDataUrl;
    QString m_sBinFileAuth;
    QString m_sDataUrlBak;
    QString m_sUpLoadLogUrl;

    QString m_sGBLaneId;
    QString m_sGBStationId;

    bool m_bFrontBar;
    bool m_capture;  //视频卡是否抓拍，缺省false
    qint32 m_LoopNum;  //车道线圈数，etc车道检测跟车干扰，缺省是6，如果是4线圈则不判断。
    quint8 m_nUTimes;  //防止同一站重复交易时间,单位秒，缺省30

    bool m_isClear;  //费显显示是否去除'闽'字,缺省false

    QString m_sNTPHost;
    quint32 m_nNTPPort;
    quint32 m_nNTPTime;

    QString m_sOpenLaneIPPort;
    QString m_sReplaceGatryHex;  //代写门加hex
    QString m_sReplaceGatryID;   //代谢门架id
    QString m_sReplaceTime;      //代写卡启用时间

    bool m_bCheckMinFee;

    QString m_sVlprBatchNo;
    quint32 m_nDataCnt;
    quint32 m_nPicCnt;

    quint32 m_nLanelen;  //车道长度,在这为抓拍线圈和过车线圈之间的距离,用于判断车辆速度

    bool m_bCheckOBUExpired;
    bool m_bCheckConVeh;  //是否校验前车干扰,缺省true

    QMutex m_virStationMt;
    QList<CVirGantryInfo> m_VirStationList;  //虚拟站表

    quint32
    m_dwDetectTime;  //车辆在检测线圈允许停留时间单位毫秒配置文件内按秒配置缺省3秒，用于判单跟车干扰，只有压上检测线圈，且超过该时间才算异常车。

    bool
    m_bCheckVehInQue;  //前天线交易时，是否校验队列内的异常车。缺省为false，因为双天线处理所以缺省不校验车道内的异常车

    bool m_bDeleteReservVeh;  //是否删除交易正常的倒车车辆。缺省false.前提是校验队列中的异常车

    QString m_sSysTemVer;

    bool m_bReverDownBar;  //缺省false,倒车落杆,倒车是否落杆（主要是防止倒车检测错误导致砸车）
    bool
    m_bFareShow;  //缺省true,向费显发送文字时是否使用 \n
    //符号分行,有些费显不支持\n,必须去掉\n,将费显内容组织成固定长度字符串(华软车道金晓费显)
    bool m_bManualCapture;  //是否启用手动抓拍

    QDate m_UploadLogDate;
    bool m_bUseOpenTime;  //入口是否使用虚拟站时间作为入口时间,缺省是true

    bool m_bHaveFrontRsu;  //有前天线
    int m_nRsuNum;         //天线数 1或2
    bool m_bhaveCardMgr;   //是否安装卡机
    bool m_bCardMgrEnabled;  //是否允许使用卡机, 当为false时，禁用卡机扫码支付，使用壁挂式扫码支付
    QString m_sFeeUrl;    //在线计费url
    QString m_sEntryUrl;  //入口信息查询url
    QString m_sFlagUrl;   //标识点查询url
    bool m_bRemoveBakFile;  //是否删除备份的报文数据文件,true-删除 false-不删除缺省是true

    bool m_bCheckSameVeh;  //天线交易时，如果车辆已经交易，判断是否进行同车交易判断处理。缺省true
    bool m_bCheckExitLoop;  //卡机发卡或收费，以及后天线交易时是否判断存在线圈状态，缺省 true
    bool m_eTick;    //是否电子票 true-电子票 false -非电子票
    int m_nOutTime;  //车辆最大超时时间，单位分钟,目前设定24小时
    int m_nMaxDays;  //车辆允许形式的最大天数 ，缺省20天

    QString m_sRSUComm;
    QString m_sRSUChannelID;
    QString m_sRSUPower;

    bool m_bStartScreenUI;  //是否启动卡机屏幕软件
    QString m_sScreenUIPath;
    bool m_bNoWeightDev;  //没有称重（出口）
    bool m_bCheckWeight;
    //远程控制对应参数
    bool m_bRemoteControl;  //是否为远程控制
    QString m_sLocalIP;
    QString m_sRtmpIP;
    qint32 m_nLocalPort;
    QString m_sRemoteServer;
    qint32 m_nRemotePort;
    QString m_sRemoteControlToken;
    //进行远程视频时，关闭本地车道视频
    bool m_bRemoteCloseLaneVideo;

    //两客一危车型，允许通过车型信息 1100 表示客1客2通行
    QString m_sVehClass;  //
    QString m_sStopTime;  //禁止行驶时间 0205 两点到5点
    QString m_sBeginTime;
    QString m_sEndTime;
    //电子纸卡
    bool m_bPaperCard;

    //车型库
    bool m_bUseVehLib;
    QString m_sVehLibUrl;
    QString m_sQryTokenUrl;
    QString m_sRefreshTokenUrl;
    QString m_sSaveUrl;
    int m_nScore;  //车型可信度
    int m_nScoreEN;  //入口车型置信度
    int m_nScoreEX;  //出口车型置信度

    int m_nMaxTimeInterval;  //同车允许连续交易时间间隔，单位秒，缺省10分钟

    //是否拦截UJ型车
    bool m_bStopUJ;
    bool m_bNewUJ;

    int m_nMaxTimeOnLoop;   //线圈防抖动，允许的最大时间
    int m_nMaxTimeOffLoop;  //
    int m_nVehDetectTime;  //车辆压上存在线圈，伸出卡机头的延迟时间，单位毫秒
    bool m_bRelocatedCard;
    int m_nTmpFileMaxTime;  //重新上传临时文件的时间限制，单位天，缺省1天以内
    bool m_bETCStopTruck;   // ETC 拦截货车？
    bool m_bFuzzyMatch;     //是否模糊查找车型，缺省不（入口用）
    bool m_bReplaceByVcr;   //是否以小黄人代替

    bool m_bTestVer;  //是否测试版本
    int m_nFlagCnt;  //判断路径不全的最小标识点数，缺省为2，就是如果是1就路径不全
    bool m_bTestPaper;  //

    QString m_sETCDelayUrl;
    bool m_bAutoDelay;

    bool m_bUseVcrResult;

    // 新增：是否禁用从车型设备队列获取车牌信息
    bool m_bDisableVcrQueuePlate;

    quint8 m_nLaneTypeHT;  //车道类型HT
    quint32 m_nMaxTimeOnBackLoop;  //后线圈闯关检测时间,单位毫秒
    QString m_sProDebPaymentUrl;  // ProDebPayment URL

    int m_nCardMgrType;  //卡机厂家：1 - 顺一；11 - 金溢； 21 - 朗为； 31 - 福赛特-中瑞；
    int m_nCardTimerInterval; // 卡机定时器间隔时间 ms
    int m_nCardHeadStretchConfig; // 卡头伸缩配置：1-依据车型判断，0-只要有线圈信号就伸出
};

#define Ptr_Info CLaneInfo::GetLaneInfo()
#endif  // LANEINFO_H
