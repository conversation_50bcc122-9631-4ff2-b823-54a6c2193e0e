#include "rsudev.h"

#include <QHostAddress>

#include "ilogmsg.h"

CRsuDev::CRsuDev(int nIndex, QObject *parent) : QObject(parent)
{
    // 首先初始化所有成员变量，避免未初始化的指针访问
    m_pComPort = NULL;
    m_pSocket = NULL;
    m_pRsuStateTimer = NULL;
    m_pRsuThread = NULL;
    m_pLog = NULL;
    
    m_lastFrameTime = QDateTime::currentDateTime();
    m_RSCTL = 0x10;
    memset(&m_rsuBaseInfo, 0, sizeof m_rsuBaseInfo);
    m_bLoopNum = 5;
    m_bWaitB0 = false;
    m_nMaxAuthNums = 15;
    m_nPsamStatus = PSAM_UNAUTH;  //默认未授权状态
    m_nRsuIndex = nIndex;
    m_bPaused = false;
    m_bUsed = true;
    m_bLlcCtl = 0x00;
    m_bRsuInited = false;
    
    // 创建串口对象
    m_pComPort = new ComPort(this);
    
    // 创建定时器
    m_pRsuStateTimer = new QTimer(this);
    
    // 创建日志对象
    m_pLog = new CLog4Qt();
    QString sLogPath = QApplication::applicationDirPath() + "//" + QString("Rsu%1").arg(m_nRsuIndex);
    m_pLog->InitLog4Qt(QString(), sLogPath, QString("rsu"));
    
    // 创建线程（但不立即移动对象）
    m_pRsuThread = new QThread();
    
    // 连接信号槽（在移动到线程之前连接，使用Qt::QueuedConnection确保线程安全）
    connect(m_pComPort, SIGNAL(OnDataToRead(int)), this, SLOT(OnDataReady_Com(int)), Qt::QueuedConnection);
    connect(m_pRsuStateTimer, SIGNAL(timeout()), this, SLOT(OnRsuStateTimer()), Qt::QueuedConnection);
    connect(this, SIGNAL(NotifyCheckState()), this, SLOT(OnCheckStateEvent()), Qt::QueuedConnection);
    connect(this, SIGNAL(NotifySendDataToRsu(QByteArray)), this, SLOT(OnSendDataToRsu(QByteArray)), Qt::QueuedConnection);
    // 连接线程安全的主机连接信号槽
    connect(this, SIGNAL(NotifyConnectHost(const QString&, int)), this, SLOT(OnConnectHostRequest(const QString&, int)), Qt::QueuedConnection);
    // 连接线程安全的Socket初始化信号槽
    connect(this, SIGNAL(NotifyInitSocket()), this, SLOT(OnInitSocketRequest()), Qt::QueuedConnection);
    
    // 移动到工作线程
    this->moveToThread(m_pRsuThread);
}

// void CRsuDev::setLaneMode(quint8 laneMode) { m_bLaneMode = laneMode; }

CRsuDev::~CRsuDev()
{
    // 安全地停止和清理资源
    // 首先设置停止标志，防止新的操作
    m_bRsuInited = false;

    // 首先断开所有信号槽连接，防止在析构过程中收到信号
    if (m_pComPort) {
        disconnect(m_pComPort, SIGNAL(OnDataToRead(int)), this, SLOT(OnDataReady_Com(int)));
    }
    if (m_pRsuStateTimer) {
        disconnect(m_pRsuStateTimer, SIGNAL(timeout()), this, SLOT(OnRsuStateTimer()));
    }
    
    // 停止定时器
    if (m_pRsuStateTimer && m_pRsuStateTimer->isActive()) {
        m_pRsuStateTimer->stop();
    }
    
    // 等待线程安全退出
    if (m_pRsuThread && m_pRsuThread->isRunning()) {
        m_pRsuThread->quit();
        if (!m_pRsuThread->wait(5000)) {  // 等待5秒
            m_pRsuThread->terminate();
            m_pRsuThread->wait(2000);  // 再等2秒
        }
    }
    
    // 清理资源
    if (m_pRsuStateTimer) {
        delete m_pRsuStateTimer;
        m_pRsuStateTimer = NULL;
    }

    if (m_pSocket) {
        m_pSocket->deleteLater();
        m_pSocket = NULL;
    }

    if (m_pComPort) {
        delete m_pComPort;
        m_pComPort = NULL;
    }

    if (m_pLog) {
        delete m_pLog;
        m_pLog = NULL;
    }
    
    // 最后删除线程对象
    if (m_pRsuThread) {
        delete m_pRsuThread;
        m_pRsuThread = NULL;
    }
}

bool CRsuDev::IsPsamNeedAuth() { return m_cfgParam.bIsEnablePSAM; }

void CRsuDev::ReadRsuParamFromCfgFile(CRsuCtrlParam &rsuParam)
{
    QString sFileName = QString("%1%2").arg(GetCurrentPath()).arg(LaneCfg_FileName);
    RsuDebugLog(QString("rsu cfg file:%1").arg(sFileName));
    QSettings rsuCfgFile(sFileName, QSettings::IniFormat);

    QString sSection = "rsu";
    sSection = QString("rsu%1").arg(m_nRsuIndex);
    // rsuParam.bLaneMode = m_bLaneMode;
    rsuParam.bCommType = rsuCfgFile.value(sSection + "/commtype", 0).toInt();
    rsuParam.bChannelId = rsuCfgFile.value(sSection + "/channelid", 0).toInt();
    rsuParam.bFlagInfo = rsuCfgFile.value(sSection + "/worktype", 0x83).toInt();
    ;  //新协议，zz
    rsuParam.bTxPower = rsuCfgFile.value(sSection + "/power", 28).toInt();
    rsuParam.bWaitTime = rsuCfgFile.value(sSection + "/waittime", 1).toInt();

    rsuParam.bLaneMode = rsuCfgFile.value(sSection + "/lanemode", 0).toInt();
    rsuParam.bIsEnablePSAM = rsuCfgFile.value(sSection + "/isenablepsam", true).toBool();
    rsuParam.PSAMChannel = rsuCfgFile.value(sSection + "/PsamChannel", 0).toInt();
    rsuParam.nPsamSerial = rsuCfgFile.value(sSection + "/PsamSerial", 0).toUInt();
    rsuParam.nMaxY = rsuCfgFile.value(sSection + "/MaxY", 0).toInt();
    rsuParam.nLeftX = rsuCfgFile.value(sSection + "/LeftX", 0).toInt();
    if (rsuParam.nLeftX > 0) {
        rsuParam.nLeftX = 0 - rsuParam.nLeftX;
    }

    rsuParam.nRightX = rsuCfgFile.value(sSection + "/RightX", 0).toInt();

    m_bLoopNum = rsuCfgFile.value(sSection + "/loopnum", 5).toInt();
    RsuDebugLog(QString("当前天线，线圈数:%1").arg(m_bLoopNum));
    if (0 == rsuParam.bLaneMode) {
        rsuParam.bLaneMode = m_bLaneMode;
    }

    rsuParam.nBeatInterval = rsuCfgFile.value(sSection + "/beatinterval", 20).toInt();

    QString tmpStr;
    QString sConnstr1 = "/connstr1";
    QString sConnstr2 = "/connstr2";
    tmpStr = sSection + sConnstr1;
    m_sConnStr1 = rsuCfgFile.value(tmpStr).toString();
    tmpStr = sSection + sConnstr2;
    m_sConnStr2 = rsuCfgFile.value(tmpStr).toString();
    RsuDebugLog(QString("rsu connstr1[%1],connstr2[%2]").arg(m_sConnStr1).arg(m_sConnStr2));
    QString sLog = QString(
                       "rsudev channelid:%1,bFlagInfo:%2,Power:%3,waittime:%4,"
                       "lanemode:%5,commtype:%6,beatinterval:%7,PsamChannel:%8,OsamSerialNo:%9")
                       .arg(rsuParam.bChannelId)
                       .arg(rsuParam.bFlagInfo)
                       .arg(rsuParam.bTxPower)
                       .arg(rsuParam.bWaitTime)
                       .arg(rsuParam.bLaneMode)
                       .arg(rsuParam.bCommType)
                       .arg(rsuParam.nBeatInterval)
                       .arg(rsuParam.PSAMChannel)
                       .arg(rsuParam.nPsamSerial);

    QString sKey = sSection + "/Used";

    m_bUsed = rsuCfgFile.value(sKey, true).toBool();

    RsuDebugLog(sLog);
    if (rsuParam.bIsEnablePSAM) {
        RsuDebugLog("PSam卡需要授权");
    } else
        RsuDebugLog("PSam 卡不需要授权");

    return;
}
bool CRsuDev::StartRsuDev(int LaneMode)
{
    if (1 == LaneMode || 3 == LaneMode || 0x10 == LaneMode)
        this->m_bLaneMode = 3;
    else
        m_bLaneMode = 4;
    ReadRsuParamFromCfgFile(m_cfgParam);
    InitAndStartRsuThread();
    return true;
}

bool CRsuDev::ReInitRsu()
{
    // 使用互斥锁保护天线重新初始化过程，防止多线程同时调用造成冲突
    QMutexLocker locker(&m_MutexReInit);
    
    SetPsamStatus(PSAM_UNAUTH);
    if (ComType_Com == m_cfgParam.bCommType) {
        OpenComPort();
        m_bRsuInited = false;
        return ReInitRsuDev();
    } else {
        ConnectToRsu();
        m_bRsuInited = false;
        return ReInitRsuDev();
    }
}

void CRsuDev::GetTerminateId(quint8 bTerminateId[])
{
    memcpy(bTerminateId, m_rsuBaseInfo.PsamInfo[m_cfgParam.PSAMChannel].TerminateCode, 6);
    return;
    for (int i = 0; i < 6; ++i) {
        if (m_rsuBaseInfo.PsamInfo[i].bVersion != 0) {
            memcpy(bTerminateId, m_rsuBaseInfo.PsamInfo[i].TerminateCode, 6);
            break;
        }
    }
    return;
}

const CRsuBaseInfo_Jx *CRsuDev::GetRsuBaseInfo() { return &m_rsuBaseInfo; }

bool CRsuDev::CheckVehPos(quint32 dwOBUId, qint32 &nPosX, qint32 &nPosY)
{
    QMutexLocker locker(&m_MutexPos);
    if (0 == m_cfgParam.nMaxY) {
        return true;
    }
    if (dwOBUId == m_vehPosInfo.dwOBUID) {
        if (0 == m_vehPosInfo.bMark) {
            if (m_vehPosInfo.PosY <= m_cfgParam.nMaxY && m_vehPosInfo.PosX >= m_cfgParam.nLeftX &&
                m_vehPosInfo.PosX <= m_cfgParam.nRightX) {
                nPosX = m_vehPosInfo.PosX;
                nPosY = m_vehPosInfo.PosY;
                return true;
            } else {
            }
        }
        nPosY = m_vehPosInfo.PosY;
        nPosX = m_vehPosInfo.PosX;
        return false;
    }
    return true;
}

void CRsuDev::GetTransparentResult(CTransParentResult &result)
{
    QMutexLocker locker(&m_responseMt);
    result = m_transparentRlt;
}

bool CRsuDev::ReInitRsuDev() { return false; }

bool CRsuDev::PsamInit(quint8 bChannelId) { return true; }

bool CRsuDev::PsamAuth(quint8 bChannelId, quint8 *MacCode) { return true; }

bool CRsuDev::TransParentPass_F2(quint32 OBUID, quint8 devType, QList<QByteArray> &cmds)
{
    return false;
}

bool CRsuDev::PsamAuthInit() { return PsamInit(m_cfgParam.PSAMChannel); }

void CRsuDev::DoRsuRawData(QByteArray &data) {}

bool CRsuDev::TransD0Frame(const QByteArray &D0Frame, quint32 &dwOBUID, CVehPosInfo &vehPosInfo,
                           quint8 &nErrorCode)
{
    return true;
}

bool CRsuDev::TransBFFrame(const QByteArray &BFFrame, quint8 SW1SW2[], quint8 &nError)
{
    return true;
}

bool CRsuDev::TransB7Frame(const QByteArray &B7Frame, quint32 &dwOBUID, int nProvinceCnt,
                           CRsuProvinceFeeGroupInfo &ProvinceFeeGroups, quint8 &nErrorCode)
{
    return false;
}

bool CRsuDev::TransE2Frame(const QByteArray &E2Frame, quint32 &dwOBUID,
                           QList<QByteArray> &responseList, quint8 &llc, quint8 &devType,
                           quint8 &nErrorCode)
{
    return false;
}

bool CRsuDev::TransBDFrame(const QByteArray &BDFrame, CPsamAuthBasicInfo &PsamAuthBasicInfo,
                           quint8 &nError)
{
    return true;
}

bool CRsuDev::RegetTac(quint32 OBUID, QDateTime &TransTime, CPro0019Raw_NewGB *p19Raw,
                       EF04Raw &EF04Info, bool isOpEf04)
{
    return false;
}

bool CRsuDev::ReadEf04_C7(quint32 OBUID, quint8 bProvinceNum) { return true; }

/*
bool CRsuDev::SendLoopStatus(quint8 bStatus)
{
    return true;
}*/

//派生类继承
bool CRsuDev::ProcessFrameData(const QByteArray &frameData, quint8 bRsctl, bool bBcc)
{
    quint8 bFrameId = frameData.at(0);
    quint8 nErrorCode = 0;
    m_lastFrameTime = QDateTime::currentDateTime();

    if ((!m_bWaitB0) && IsPowerOnFrame(bFrameId, bRsctl)) {
        m_bWaitB0 = true;
        ReInitRsuDev();
        emit NotifyRsuEvent(m_nRsuIndex, 0, RsuEvent_OnPower, 0xB0, 0);
        return true;
    }
    QTime time = QTime::currentTime();
    QString str = time.toString("hhmmsszzz");
    PrintStdLog(bFrameId, str);
    quint32 OBUID = 0;
    switch (bFrameId) {
        case 0xB0: {
            m_bWaitB0 = false;
            CRsuBaseInfo_Jx rsuBaseInfo;
            if (!TransB0Frame(frameData, rsuBaseInfo, nErrorCode)) {
                RsuDebugLog(QString("B0帧解析失败,ErrorCode:%1").arg(nErrorCode));
                return false;
            }
            memcpy(&m_rsuBaseInfo, &rsuBaseInfo, sizeof rsuBaseInfo);
            m_bRsuInited = true;
            //回应b0
            //启用PSAM
            if (m_cfgParam.bIsEnablePSAM) {
                this->PsamInit(m_cfgParam.PSAMChannel);
            } else {
                ContinueDeal(0);
            }
            emit NotifyRsuState(m_nRsuIndex, true);

            return true;
            break;
        }

        case 0xB2: {
            //
            COBUBaseInfo obuBaseInfo;
            bool tmp = TransB2Frame(frameData, OBUID, obuBaseInfo, nErrorCode);

            QString sLog = QString("%1:%2H")
                               .arg(bFrameId, 2, 16, QLatin1Char('0'))
                               .arg(nErrorCode, 2, 16, QLatin1Char('0'))
                               .toUpper();
            StdInfoLog(LogKey::OneKey_FrameData, LogKey::FrameData_ErrCode, str, str, sLog);

            RsuDebugLog(QString("接收到B2帧处理结果为:%1").arg(tmp));
            if (!tmp) {
                if (nErrorCode == 0x80 || nErrorCode == 0xFF) {
                    // emit NotifyRsuEvent(m_nRsuIndex, 0, RsuEvent_HeartBeat, bFrameId,
                    // nErrorCode);
                    if (!m_bRsuInited) {
                        RsuDebugLog(QString("心跳帧重新初始化天线"));
                        // ReInitRsu();
                    }
                    return true;
                }

                emit NotifyRsuError(m_nRsuIndex, 0xB2, nErrorCode);
                return false;
            }
            if (quint8(frameData.at(1)) > 0xA0) {
                // cpc
                // return false;
            }
            m_OBUBaseInfo = obuBaseInfo;
            // memcpy(&m_OBUBaseInfo,&obuBaseInfo,sizeof obuBaseInfo);
            emit NotifyRsuEvent(m_nRsuIndex, OBUID, RsuEvent_OBUBaseInfo, bFrameId, nErrorCode);
            break;
        }

        case 0xB3: {
            if (!m_bRsuInited) {
                RsuDebugLog("天线未初始化收到B3帧不处理");
                return false;
            }
            bool tmp = TransB3Frame(frameData, OBUID, m_OBUVehInfo, nErrorCode);
            QString sLog = QString("%1:%2H")
                               .arg(bFrameId, 2, 16, QLatin1Char('0'))
                               .arg(nErrorCode, 2, 16, QLatin1Char('0'))
                               .toUpper();
            StdInfoLog(LogKey::OneKey_FrameData, LogKey::FrameData_ErrCode, str,
                       QString::fromAscii(m_OBUBaseInfo.szTime), sLog);
            if (!tmp) {
                emit NotifyRsuError(m_nRsuIndex, 0xB3, nErrorCode);
                return false;
            }
            emit NotifyRsuEvent(m_nRsuIndex, OBUID, RsuEvent_OBUVehInfo, bFrameId, nErrorCode);
            break;
        }
        case 0xB4: {
            if (!m_bRsuInited) {
                RsuDebugLog("天线未初始化收到B4帧，不处理");
                return false;
            }
            bool tmp = TransB4Frame(frameData, m_bLaneMode, OBUID, m_IccInfo, nErrorCode);
            QString sLog = QString("%1:%2H")
                               .arg(bFrameId, 2, 16, QLatin1Char('0'))
                               .arg(nErrorCode, 2, 16, QLatin1Char('0'))
                               .toUpper();
            StdInfoLog(LogKey::OneKey_FrameData, LogKey::FrameData_ErrCode, str,
                       QString::fromAscii(m_OBUBaseInfo.szTime), sLog);
            if (!tmp) {
                emit NotifyRsuError(m_nRsuIndex, 0xB4, nErrorCode);
                return false;
            }
            emit NotifyRsuEvent(m_nRsuIndex, OBUID, RsuEvent_IccInfo, bFrameId, nErrorCode);
            break;
        }
        case 0xB5: {
            if (!TransB5Frame(frameData, OBUID, m_rsuOpResult, nErrorCode)) {
                emit NotifyRsuError(m_nRsuIndex, 0xB5, nErrorCode);
                RsuDebugLog(QString("B5帧数据解析失败: %1").arg(nErrorCode));
                return false;
            }
            RsuDebugLog("B5帧数据解析成功");
            QString sLog = QString("%1:%2H")
                               .arg(bFrameId, 2, 16, QLatin1Char('0'))
                               .arg(nErrorCode, 2, 16, QLatin1Char('0'))
                               .toUpper();
            StdInfoLog(LogKey::OneKey_FrameData, LogKey::FrameData_ErrCode, str,
                       QString::fromAscii(m_OBUBaseInfo.szTime), sLog);
            emit NotifyRsuEvent(m_nRsuIndex, OBUID, RsuEvent_TransResult, bFrameId, nErrorCode);
            break;
        }
        case 0xB7: {
            m_ProvinceFeeGroup.Clear();
            bool tmp = TransB7Frame(frameData, OBUID, m_IccInfo.ef04Info.bProvinceCount,
                                    m_ProvinceFeeGroup, nErrorCode);
            QString sLog = QString("%1:%2H")
                               .arg(bFrameId, 2, 16, QLatin1Char('0'))
                               .arg(nErrorCode, 2, 16, QLatin1Char('0'))
                               .toUpper();
            StdInfoLog(LogKey::OneKey_FrameData, LogKey::FrameData_ErrCode, str,
                       QString::fromAscii(m_OBUBaseInfo.szTime), sLog);
            if (!tmp) {
                RsuDebugLog(QString("B7帧数据解析失败"));
            }
            RsuDebugLog(QString("B7帧数据解析完毕"));
            emit NotifyRsuEvent(m_nRsuIndex, OBUID, RsuEvent_B7, bFrameId, nErrorCode);
            break;
        }
        case 0xD0: {  // d0帧不需要回复
            CVehPosInfo vehPosInfo;
            bool tmp = TransD0Frame(frameData, OBUID, vehPosInfo, nErrorCode);
            if (tmp) {
                QMutexLocker locker(&m_MutexPos);
                m_vehPosInfo = vehPosInfo;
            }
            QString sLog = QString("%1:%2H")
                               .arg(bFrameId, 2, 16, QLatin1Char('0'))
                               .arg(nErrorCode, 2, 16, QLatin1Char('0'))
                               .toUpper();
            StdInfoLog(LogKey::OneKey_FrameData, LogKey::FrameData_ErrCode, str, QString(), sLog);
            break;
        }
        case 0xBD: {  // PSAM授权初始化
            // psam
            CPsamAuthBasicInfo basicInfo;
            basicInfo.Clear();
            bool tmp = TransBDFrame(frameData, basicInfo, nErrorCode);
            QString sLog = QString("%1:%2H")
                               .arg(bFrameId, 2, 16, QLatin1Char('0'))
                               .arg(nErrorCode, 2, 16, QLatin1Char('0'))
                               .toUpper();
            StdInfoLog(LogKey::OneKey_FrameData, LogKey::FrameData_ErrCode, str, QString(), sLog);
            if (!tmp) {
                emit NotifyRsuError(m_nRsuIndex, 0xBD, nErrorCode);
                RsuDebugLog("BD 帧数据解析失败");
                return false;
            } else {
                int nPsamChannel = m_cfgParam.PSAMChannel;
                if (nPsamChannel < 0 || nPsamChannel > 3) nPsamChannel = 0;
                memcpy(basicInfo.terminalCode, m_rsuBaseInfo.PsamInfo[nPsamChannel].TerminateCode,
                       6);
                memcpy(&m_basicInfo, &basicInfo, sizeof basicInfo);
                emit NotifyRsuEvent(m_nRsuIndex, 0, RsuEvent_PsamCmd, bFrameId, nErrorCode);
            }
            break;
        }
        case 0xBF: {  // PSAM 授权结果
            quint8 SW1SW2[2] = {0};
            bool tmp = TransBFFrame(frameData, SW1SW2, nErrorCode);
            QString sLog = QString("%1:%2H")
                               .arg(bFrameId, 2, 16, QLatin1Char('0'))
                               .arg(nErrorCode, 2, 16, QLatin1Char('0'))
                               .toUpper();
            StdInfoLog(LogKey::OneKey_FrameData, LogKey::FrameData_ErrCode, str, QString(), sLog);
            if (!tmp) {
                m_basicInfo.nErrorCode = nErrorCode;
                // emit NotifyRsuError(0xBF,nErrorCode);
                emit NotifyRsuEvent(m_nRsuIndex, 0, RsuEvent_PsamAuthInfo, bFrameId, nErrorCode);
                SetPsamStatus(PSAM_AUTHFAIL);
                RsuDebugLog("BF 帧解析失败");
                return false;
            } else {
                memcpy(m_basicInfo.SW1SW2, SW1SW2, 2);
                if (0x90 == SW1SW2[0] && 0x00 == SW1SW2[1]) {
                    m_nMaxAuthNums = 0x0F;
                    m_basicInfo.nErrorCode = 0;
                    SetPsamStatus(PSAM_NORMAL);
                } else {
                    if (0x63 == SW1SW2[0] && 0xC0 == (SW1SW2[1] & 0xF0))
                        m_nMaxAuthNums = (0x0F & SW1SW2[1]);
                    SetPsamStatus(PSAM_AUTHFAIL);
                }
                emit NotifyRsuEvent(m_nRsuIndex, 0, RsuEvent_PsamAuthInfo, bFrameId, nErrorCode);
            }
            break;
        }
        case 0xE2: {
            QList<QByteArray> responses;
            quint8 bDevType = 0, bllc = 0;
            bool bRlt = TransE2Frame(frameData, OBUID, responses, bllc, bDevType, nErrorCode);
            QString sLog = QString("%1:%2H")
                               .arg(bFrameId, 2, 16, QLatin1Char('0'))
                               .arg(nErrorCode, 2, 16, QLatin1Char('0'))
                               .toUpper();
            StdInfoLog(LogKey::OneKey_FrameData, LogKey::FrameData_ErrCode, str, QString(), sLog);
            if (!bRlt) {
                emit NotifyRsuEvent(m_nRsuIndex, OBUID, RsuEvent_TransParent, bFrameId, nErrorCode);
                DebugLog(QString("E2 帧解析失败"));
                return false;
            } else {
                {
                    QMutexLocker locker(&m_responseMt);
                    m_transparentRlt.OBUID = OBUID;
                    m_transparentRlt.devChannel = bDevType;
                    m_transparentRlt.responses.clear();
                    m_transparentRlt.responses = responses;
                }
                if (SetLlc(bllc)) {
                }
                NotifyRsuEvent(m_nRsuIndex, OBUID, RsuEvent_TransParent, bFrameId, nErrorCode);
            }

            break;
        }
    }
    return true;
}

void CRsuDev::PrintStdLog(quint8 bFrameId, QString str)
{
    static quint8 blastFrameId = 0;
    static qint64 b2 = 0, b3 = 0, b4 = 0, b5 = 0, ba = 0;
    switch (bFrameId) {
        case 0xB2: {
            b2 = QDateTime::currentMSecsSinceEpoch();
            break;
        }
        case 0xB3: {
            if (0xB2 == blastFrameId) {
                b3 = QDateTime::currentMSecsSinceEpoch();
                StdInfoLog(LogKey::OneKey_TranResult, LogKey::TranResult_B2B3, str,
                           QString::fromAscii(m_OBUBaseInfo.szTime), QString("%1ms").arg(b3 - b2));
            }
            break;
        }
        case 0xB4: {
            if (0xB3 == blastFrameId) {
                b4 = QDateTime::currentMSecsSinceEpoch();
                StdInfoLog(LogKey::OneKey_TranResult, LogKey::TranResult_B3B4, str,
                           QString::fromAscii(m_OBUBaseInfo.szTime), QString("%1ms").arg(b4 - b3));
            }
            break;
        }
        case 0xB5: {
            if (0xB4 == blastFrameId) {
                b5 = QDateTime::currentMSecsSinceEpoch();
                StdInfoLog(LogKey::OneKey_TranResult, LogKey::TranResult_B4B5, str,
                           QString::fromAscii(m_OBUBaseInfo.szTime), QString("%1ms").arg(b5 - b4));
            }
            break;
        }
        case 0xB7: {
            if (0xB5 == blastFrameId) {
                ba = QDateTime::currentMSecsSinceEpoch();
                StdInfoLog(LogKey::OneKey_TranResult, LogKey::TranResult_B5BA, str,
                           QString::fromAscii(m_OBUBaseInfo.szTime), QString("%1ms").arg(ba - b5));
            }
            break;
        }
    }
    blastFrameId = bFrameId;
}

void CRsuDev::RsuDebugLog(const QString &sLog)
{
    QString str = QString("天线[%1]%2").arg(m_nRsuIndex).arg(sLog);
    // DebugLog(str);
    if (!m_pLog) return;
    m_pLog->LogMsg(LEVEL_DEBUG, str);
}

bool CRsuDev::SetLlc(quint8 bllc)
{
    QMutexLocker locker(&m_llcMt);
    if (m_bLlcCtl == bllc) {
        return false;
    } else {
        m_bLlcCtl = bllc;
        return true;
    }
}

bool CRsuDev::OpenComPort()
{
    if (m_pComPort->isOpen()) m_pComPort->close();

    RsuDebugLog(QString("Rsudev com connstr1%1").arg(m_sConnStr1));

    quint32 nBaudRate = m_sConnStr2.toUInt();
    if (0 == nBaudRate) nBaudRate = 115200;
    QString sComId;
#ifdef Q_OS_WIN32
    int nComID = ExtractComId(m_sConnStr1);
    if (0 >= nComID) {
        RsuDebugLog(QString("天线串口号[%1]设置错误").arg(m_sConnStr1));
        return false;
    }
    if (nComID < 10)
        sComId = QString("COM%1").arg(nComID);
    else
        sComId = QString("\\\\.\\COM%1").arg(nComID);
#else
    sComId = m_sConnStr1;
#endif
    RsuDebugLog(QString("天线设置串口%1").arg(sComId));
    if (m_pComPort->OpenComPort(sComId)) {
        m_pComPort->SetBaudRate(nBaudRate);
        m_pComPort->SetComOption(8, 1, 'n');
        m_bRsuInited = false;
        return true;
    } else {
        RsuDebugLog(QString("天线串口[%1]打开失败").arg(sComId));
    }
    return false;
}

bool CRsuDev::ConnectToRsu()
{
    // 解析连接字符串
    QStringList connList = m_sConnStr1.split(":");
    if (connList.size() < 2) {
        RsuDebugLog(QString("Rsu ConnStr1 [%1]错误").arg(m_sConnStr1));
        return false;
    }
    m_ip = connList.at(0);
    m_port = connList.at(1).toUInt();
    
    RsuDebugLog(QString("准备连接到RSU [%1:%2]").arg(m_ip).arg(m_port));
    
    // 通过信号槽机制在工作线程中初始化socket和连接
    emit NotifyInitSocket();
    
    return true;
}

void CRsuDev::InitAndStartRsuThread()
{
    m_bRsuInited = false;
    m_lastFrameTime = QDateTime::fromTime_t(0);
    if (!m_pRsuThread->isRunning()) m_pRsuThread->start();
    emit NotifyCheckState();
    return;
    /*
    ReInitRsu();
    m_lastFrameTime = QDateTime::currentDateTime();
    m_pRsuStateTimer->start(10000);
    return ;
    */
}

bool CRsuDev::SendToRsu(const quint8 *pData, int nSize)
{
    QByteArray byteArrayIn((char *)pData, nSize);
    QByteArray byteArray;
    TransOutputCmd(byteArrayIn, m_RSCTL, byteArray);

    QString sData = Raw2HexStr(byteArray);
    RsuDebugLog(QString("天线%1,发往RSU的数据：%2").arg(m_nRsuIndex).arg(sData));
    QString str = QTime::currentTime().toString("hhmmsszzz");
    StdInfoLog(LogKey::OneKey_FrameData, LogKey::FrameData_PCSend, str,
               QString::fromAscii(m_OBUBaseInfo.szTime), sData);
    // int nRet = 0;
    emit NotifySendDataToRsu(byteArray);
    /*
    if(ComType_Com==m_cfgParam.bCommType)
        nRet=m_pComPort->Com_Write(byteArray);
    else
    {
        nRet = m_pSocket->write(byteArray);
        m_pSocket->flush();
    }

    if (nRet != byteArray.size())
        return false;
     */
    RsuDebugLog(QString("rsu数据发送完成"));
    return true;
}

void CRsuDev::SetPsamSerialNo(quint32 nPsamSerialNo)
{
    m_cfgParam.nPsamSerial = nPsamSerialNo;
    QSettings cfgFlie(LaneCfg_FileName, QSettings::IniFormat);
    //车道基本配置参数
    QString sSection = "rsu";
    QString sSerial = QString::number(nPsamSerialNo);
    cfgFlie.setValue(sSection + "/PsamSerial", sSerial);
    return;
}

void CRsuDev::OnDataReady_Net()
{
    // 添加socket空指针检查，确保线程安全
    if (!m_pSocket) {
        RsuDebugLog("OnDataReady_Net: m_pSocket为空指针");
        return;
    }
    
    QByteArray data = m_pSocket->readAll();
    if (data.size() > 0) {
        // QString sData = Raw2HexStr(data);
        // RsuDebugLog(QString("从RSU网口接收到数据：%1").arg(sData));
        DoRsuRawData(data);
    }
}

void CRsuDev::OnDataReady_Com(int nDataLen)
{
#ifdef Q_OS_UNIX
    QByteArray bArray;
    m_pComPort->Com_Read(bArray, 100);
#else
    char buffer[2048];
    memset(buffer, 0, sizeof buffer);
    int nRlt = m_pComPort->Com_Read(buffer, nDataLen, 100);
    QByteArray bArray(buffer, nRlt);
#endif

#ifndef Lane_Test
    // RsuDebugLog(QString("从RSU串口接收到数据：%1").arg(Raw2HexStr(bArray)));
#endif
    DoRsuRawData(bArray);
}

void CRsuDev::OnRsuStateTimer()
{
    m_pRsuStateTimer->stop();
    QDateTime curTime = QDateTime::currentDateTime();
    if (m_bPaused) {
        m_lastFrameTime = curTime;
    }
    //两分钟没检测到数据，重新初始化天线，如果15秒还没检测到数据，就每15秒初始化一次
    else if (m_lastFrameTime.secsTo(curTime) > 2 * 60 || !m_bRsuInited) {
        emit NotifyRsuState(m_nRsuIndex, false);
        //超过二分钟没有接收到心跳报文，应该重新初始化
        RsuDebugLog("天线状态超时");
        ReInitRsu();
        m_lastFrameTime = QDateTime::currentDateTime();
    }
    m_pRsuStateTimer->start(15000);
}

void CRsuDev::OnCheckStateEvent()
{
    if (m_pRsuStateTimer->isActive()) m_pRsuStateTimer->stop();
    m_pRsuStateTimer->start(100);
}

void CRsuDev::ConnectHost(const QString &sIP, int nPort)
{
    // 警告：这个函数只能在对象所属的工作线程中调用！
    // 如果从主线程调用会导致跨线程访问Qt对象，引发崩溃
    // 建议使用 ConnectHostSafe() 替代直接调用此函数
    
    if (!m_pSocket) {
        RsuDebugLog("ConnectHost: m_pSocket为空指针");
        return;
    }
    
    if (!m_pSocket->isValid() || m_pSocket->state() != QAbstractSocket::ConnectedState) {
        QHostAddress host(sIP);
        m_pSocket->abort();
        m_pSocket->connectToHost(host, nPort);
        if (!m_pSocket->waitForConnected()) {
            RsuDebugLog(QString("天线网络重新连接出现异常: %1").arg(m_pSocket->state()));
        }
    }
    return;
}

void CRsuDev::OnConnected() { RsuDebugLog("已经通过网络连接到天线"); }

void CRsuDev::OnDisconnected() { RsuDebugLog("天线网络断开"); }

void CRsuDev::OnError(QAbstractSocket::SocketError errCode)
{
    RsuDebugLog(QString("天线网络出现错误:%1,断开重连").arg(errCode));
    // SetPsamStatus(PSAM_UNAUTH);
    
    // 添加socket空指针检查，确保线程安全
    if (m_pSocket) {
        m_pSocket->disconnectFromHost();
    } else {
        RsuDebugLog("OnError: m_pSocket为空指针");
    }
}

void CRsuDev::OnSendDataToRsu(QByteArray frameData)
{
    int nRet = 0;
    if (ComType_Com == m_cfgParam.bCommType) {
        nRet = m_pComPort->Com_Write(frameData);
    } else {
        // 添加socket空指针检查，确保线程安全
        if (!m_pSocket) {
            RsuDebugLog("OnSendDataToRsu: m_pSocket为空指针，无法发送数据");
            return;
        }
        nRet = m_pSocket->write(frameData);
        m_pSocket->flush();
    }

    if (nRet != frameData.size()) {
        RsuDebugLog(QString("Rsu%1,OnSendDataToRsu SendFailed").arg(m_nRsuIndex));
    }
    return;
}

// 线程安全的连接主机槽函数
void CRsuDev::OnConnectHostRequest(const QString &sIP, int nPort)
{
    // 这个函数在工作线程中执行，安全地访问m_pSocket
    ConnectHost(sIP, nPort);
}

// 线程安全的初始化Socket槽函数
void CRsuDev::OnInitSocketRequest()
{
    // 这个函数在工作线程中执行，安全地创建和初始化socket
    RsuDebugLog(QString("在工作线程中初始化Socket"));
    
    // 如果socket已存在，先清理
    if (m_pSocket) {
        if (m_pSocket->isValid() && m_pSocket->state() == QAbstractSocket::ConnectedState) {
            m_pSocket->close();
        }
        m_pSocket->deleteLater();
        m_pSocket = NULL;
    }
    
    // 在工作线程中创建新的socket
    m_pSocket = new QTcpSocket(this);
    
    // 连接socket信号槽
    connect(m_pSocket, SIGNAL(connected()), this, SLOT(OnConnected()));
    connect(m_pSocket, SIGNAL(disconnected()), this, SLOT(OnDisconnected()));
    bool bRlt = connect(m_pSocket, SIGNAL(readyRead()), this, SLOT(OnDataReady_Net()));
    if (!bRlt) {
        RsuDebugLog("rsu socket readReady signal connection error");
    }
    connect(m_pSocket, SIGNAL(error(QAbstractSocket::SocketError)), this,
            SLOT(OnError(QAbstractSocket::SocketError)));
    
    RsuDebugLog(QString("Socket初始化完成，准备连接到 [%1:%2]").arg(m_ip).arg(m_port));
    
    // 执行连接
    ConnectHost(m_ip, m_port);
}

// 线程安全的连接主机接口，可以从任何线程调用
void CRsuDev::ConnectHostSafe(const QString &sIP, int nPort)
{
    // 通过信号槽机制将调用转发到工作线程执行
    emit NotifyConnectHost(sIP, nPort);
}

// 线程安全的重新连接接口，使用配置中的IP和端口重新连接
void CRsuDev::ReConnectSafe()
{
    // 通过信号槽机制将调用转发到工作线程执行
    if (!m_ip.isEmpty() && m_port > 0) {
        emit NotifyConnectHost(m_ip, m_port);
    } else {
        RsuDebugLog("ReConnectSafe: IP或端口未配置");
    }
}
