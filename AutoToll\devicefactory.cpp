﻿#include "devicefactory.h"

#include "lanedevadapter.h"
#include "log4qt/ilogmsg.h"
#include "rsudev_gb.h"
#include "rsudev_gbef.h"
#include "rsudev_old.h"
#include "mobilepaytw.h"
#include "mobilepayjx.h"
#include "mobilepaydual.h"

#define Dev_Cfg_Driver "driver"
#define Dev_Cfg_ConnStr1 "connstr1"
#define Dev_Cfg_ConnStr2 "connstr2"
#define Dev_Cfg_Std "bstd"
#define Dev_Cfg_nType "ntype"
const int MaxDevCfgStrLen = 128;

CFareDisplayer_GB *CDeviceFactory::m_pETCFareDisplayer[MAX_FAREDISPLAYER_NUM] = {0, 0};
CFs_little *CDeviceFactory::m_pLEDFare = NULL;
CVideoCard *CDeviceFactory::m_pVideoCard = NULL;
CVPRDev *CDeviceFactory::m_pVPRDevs[MAX_VPR_NUM] = {NULL, NULL};
CCardReader *CDeviceFactory::m_pcardRead[MAX_CARD_READER_NUM] = {0, 0, 0};
bool CDeviceFactory::m_bHaseReader[MAX_CARD_READER_NUM] = {false, false, false};
CIOCard *CDeviceFactory::m_pIOCard = NULL;

CVDMDev *CDeviceFactory::m_pVDMDev = NULL;
CRsuDev *CDeviceFactory::m_pRsuDevs[2] = {NULL, NULL};
CCardMgr *CDeviceFactory::m_pCardMachine = NULL;
CWtSysDev_ZC *CDeviceFactory::m_pWtDev = NULL;
VCRDev *CDeviceFactory::m_pVcrDev = NULL;
//自助收费外屏
AutoExTollScreen *CDeviceFactory::m_pAutoExTollScreen = NULL;
MobilePayBase *CDeviceFactory::m_pMobilePay = NULL;

int CDeviceFactory::m_nCardReaderNum = 1;
quint8 CDeviceFactory::m_bLaneType = 3;
bool CDeviceFactory::m_bHaveCardMgr = false;
CPaymentMachine *CDeviceFactory::m_pPaymentMgr = NULL;
CPrinterDevice *CDeviceFactory::m_pPrinter = NULL;
CInvoiceQrCode *CDeviceFactory::m_pQrCode = NULL;
CSpEventDev *CDeviceFactory::m_pSpClient = NULL;
CVehicleOutlineDev *CDeviceFactory::m_pVehicleOutlineDev = NULL;

QString g_sDevNameArray[MAX_DEV_COUNT] = {
    QString::fromUtf8(""),
    QString::fromUtf8("视频卡"),
    QString::fromUtf8("前车牌识别"),
    QString::fromUtf8("后车牌识别"),
    QString::fromUtf8("费显"),
    QString::fromUtf8("IO卡"),
    QString::fromUtf8("车型识别"),
    QString::fromUtf8("卡机"),
    QString::fromUtf8("POS机"),
    QString::fromUtf8("打印机"),
    QString::fromUtf8("称台"),
    QString::fromUtf8("天线"),
    QString::fromUtf8("后天线"),
    QString::fromUtf8("字符叠加器"),
    QString::fromUtf8("ETC费显"),
    QString::fromUtf8("MTC费显"),
    QString::fromUtf8("LED显示屏"),
    QString::fromUtf8("网络"),
    QString::fromUtf8("卡读写器"),
    QString::fromUtf8("上读写器"),
    QString::fromUtf8("下读写器"),
    QString::fromUtf8("自助收费外屏"),
    QString::fromUtf8("自助扫码"),
    QString::fromUtf8("壁挂扫码"),
    QString::fromUtf8("车辆外轮廓尺寸检测仪")
    //  QString::fromUtf8("车牌识别")
};

// bStd,1-国标，其他非国标，根据不同类型定义不同接口
void GetDevCfg(const QString &szDevName, QString &szDriver, QString &szConnStr1,
               QString &szConnStr2, quint8 &bStd, int &nType, int &bUsed)
{
    QString configfile = QApplication::applicationDirPath() + "/" + LaneCfg_FileName;
    // DebugLog(QString("读到配置文件:%1").arg(configfile));
    QSettings readLaneConfig(configfile, QSettings::IniFormat);

    QString tmpStr;
    tmpStr = szDevName + "/" + Dev_Cfg_Driver;
    szDriver = readLaneConfig.value(tmpStr).toString();
    tmpStr = szDevName + "/" + Dev_Cfg_ConnStr1;
    szConnStr1 = readLaneConfig.value(tmpStr).toString();
    tmpStr = szDevName + "/" + Dev_Cfg_ConnStr2;
    szConnStr2 = readLaneConfig.value(tmpStr).toString();
    tmpStr = szDevName + "/" + Dev_Cfg_Std;
    bStd = readLaneConfig.value(tmpStr, false).toInt();
    tmpStr = szDevName + "/" + Dev_Cfg_nType;
    nType = readLaneConfig.value(tmpStr, 0).toInt();
    tmpStr = szDevName + "/" + "Used";
    bUsed = readLaneConfig.value(tmpStr, 1).toInt();

    DebugLog(QString("%1 driver[%2],connstr1[%3],connstr2[%4],bstd[%5],ntype[%6],bUsed[%7]")
                 .arg(szDevName)
                 .arg(szDriver)
                 .arg(szConnStr1)
                 .arg(szConnStr2)
                 .arg(bStd)
                 .arg(nType)
                 .arg(bUsed));
}

void ReadDevCfg(const QString &szDevName, const QString &sKey, QString &sValue)
{
    QString configfile = QApplication::applicationDirPath() + "/" + LaneCfg_FileName;
    QSettings readLaneConfig(configfile, QSettings::IniFormat);
    QString tmpKey = szDevName + "/" + sKey;
    sValue = readLaneConfig.value(tmpKey).toString();
    return;
}

bool CDeviceFactory::InitCardReader(QString &sError, const QObject *receiver, const char *method)
{
    QString sDriver;
    QString sConnStr1;
    QString sConnStr2;
    quint8 bStd;
    int nType, bUsed;

    int nCardNum = Ptr_Info->bHaveCardMgr() ? 3 : 1;
    m_nCardReaderNum = nCardNum;
    for (int i = 0; i < nCardNum; ++i) {
        GetDevCfg(QString("cardreader%1").arg(i), sDriver, sConnStr1, sConnStr2, bStd, nType,
                  bUsed);
        if (bUsed) {
            QString sSlot;
            int nSlot = 1;
            ReadDevCfg(QString("cardreader%1").arg(i), QString("psamslot"), sSlot);
            if (!sSlot.isEmpty()) {
                nSlot = sSlot.toInt();
                if (nSlot > 2 || nSlot < 1) nSlot = 1;
            }

            CCardReader *pCardReader = GetCardReader(i);
            bool bEntry = Ptr_Info->IsEntryLane() ? true : false;  //入口不校验
            pCardReader->SetWorkMode(bEntry);
            pCardReader->SetDevIdAndName(DEV_CardReader + i, g_sDevNameArray[DEV_CardReader + i]);
            pCardReader->SetStdReader(true);
            pCardReader->SetPsamSlot(nSlot);
            if (0 == i) {
                pCardReader->SetAddToReaders(true);
            } else {
                if (!bEntry) {
                    pCardReader->SetAddToReaders(true);
                }
            }
            connect(pCardReader, SIGNAL(NotifyReaderPsamAuthEvent(int, int, int)), receiver,
                    method);
            if (!pCardReader->InitDev(sDriver, sConnStr1, sConnStr2, 1, nType)) {
                DebugLog(QString("卡读写器[%1]初始化失败").arg(i));
                sError = QString("卡读写器初始化失败");
            } else {
                DebugLog(QString("卡读写器[%1]初始化成功开始授权").arg(i));
#ifdef Lane_Test
                pCardReader->SetAuthResult(true);
#else
                // pCardReader->SetAuthResult(true);
                pCardReader->PsamAuthInit();
#endif
            }
        }
    }
    DebugLog(QString("卡读写器初始化完毕"));
    return true;
}

bool CDeviceFactory::InitCardMachine(QString &sError, const QObject *receiver, const char *method,
                                     const char *helpBtn)
{
    if (!m_bHaveCardMgr) return false;
    QString sDriver;
    QString sConnStr1;
    QString sConnStr2;
    quint8 bStd;
    int nType, bUsed;

    GetDevCfg(QString("CardMgr"), sDriver, sConnStr1, sConnStr2, bStd, nType, bUsed);

    CBaseCardMgr *pBaseCardMgr = NULL;

    if (Ptr_Info->IsEntryLane()) {
        pBaseCardMgr = GetCardMachine();
        if (sConnStr2.isEmpty()) sConnStr2 = QString("57600");
    } else
        pBaseCardMgr = GetPayMentMgr();

    if (!pBaseCardMgr) return false;

    pBaseCardMgr->SetDevIdAndName(DEV_AutoMachine, g_sDevNameArray[DEV_AutoMachine]);
    connect(pBaseCardMgr, SIGNAL(NotifyCardMachineEvent(int, int, int)), receiver, method);
    connect(pBaseCardMgr, SIGNAL(NotifyPressHelpButton(int)), receiver, helpBtn);
    if (!pBaseCardMgr->InitDev(sDriver, sConnStr1, sConnStr2, bStd, nType)) {
        sError = QString("卡机初始化失败");
        disconnect(pBaseCardMgr, SIGNAL(NotifyCardMachineEvent(int, int, int)), receiver, method);
        return false;
    }
    return true;
}

bool CDeviceFactory::InitInvoiceQrCode()
{
    if (Ptr_Info->IsExitLane() && Ptr_Info->IsMTCLane()) {
        if (!m_pQrCode) m_pQrCode = new CInvoiceQrCode();
        QString szDriver, szConnStr1, szConnStr2;
        quint8 bStd = 0;
        int nType = 0, bUsed = 0;
        GetDevCfg(QString("qrCode"), szDriver, szConnStr1, szConnStr2, bStd, nType, bUsed);
        m_pQrCode->SetDevIdAndName(0, QString("二维码生成器"));
        if (!m_pQrCode->InitDev(szDriver, szConnStr1, szConnStr2)) {
            DebugLog(QString("二维码生成库初始化失败"));
            return false;
        }
    }
    return true;
}

void CDeviceFactory::InitDevFactory(int nLaneType, bool bHaveCardMgr)
{
    m_bLaneType = nLaneType;
    m_bHaveCardMgr = bHaveCardMgr;
    m_nCardReaderNum = Ptr_Info->bHaveCardMgr() ? 3 : 1;

    if (Ptr_Info->bHaveFrontDev()) {
        m_pRsuDevs[0] = new CRsuDev_GB_EF(0);
    }

    if (Ptr_Info->bHaveBackDev()) {
        m_pRsuDevs[1] = new CRsuDev_GB_EF(1);
    }

    // if (Ptr_Info->bHaveFrontDev()) m_pVPRDevs[DevIndex_First] = new CVPRDev(DevIndex_First);

    m_pVPRDevs[DevIndex_First] = NULL;

    if (Ptr_Info->bHaveBackDev()) m_pVPRDevs[DevIndex_Second] = new CVPRDev(DevIndex_Second);

    if (Ptr_Info->bHaveFrontDev()) {
        m_pETCFareDisplayer[0] = new CFareDisplayer_GB(0);
    }

    if (Ptr_Info->bHaveBackDev()) {
        m_pETCFareDisplayer[1] = new CFareDisplayer_GB(1);
    }

    return;
}

bool CDeviceFactory::InitDev(QString &sError)
{
    QString szDriver;
    QString szConnStr1;
    QString szConnStr2;
    quint8 bStd;
    int nType, bUsed;

    CLaneDevAdapter *pDevAdapter = CLaneDevAdapter::GetSingleInstance();
    if (pDevAdapter) {
        bool bRlt = pDevAdapter->StartDev();
        if (pDevAdapter->bUseDevAdapter() && !bRlt) {
            sError = "智能设备节点初始化失败";
            return false;
        }
    }

    //费显
    CFareDisplayer_GB *pFs_st = NULL;
    for (int i = 0; i < 2; ++i) {
        pFs_st = GetETCFareDisPlayer(i);
        if (!pFs_st) continue;
        pFs_st->SetMode(pDevAdapter->bUseDevAdapter());
        QString sFsName = g_sDevNameArray[DEV_ETCFare + i];
        QString sDev = QString("etcfd%1").arg(i);
        GetDevCfg(sDev, szDriver, szConnStr1, szConnStr2, bStd, nType, bUsed);
        pFs_st->SetDevIdAndName(DEV_ETCFare + i, sFsName);
        if (!pFs_st->InitDev(szDriver, szConnStr1, szConnStr2, true, nType)) {
            sError = QString("费显%1初始化失败,请检查配置参数[%2,%3,%4]")
                         .arg(i)
                         .arg(szDriver)
                         .arg(szConnStr1)
                         .arg(szConnStr2);
            ErrorLog(sError);
        } else {
            DebugLog(QString("费显%1初始化成功").arg(i));
            pFs_st->ShowWelcomeInfo(false);
        }
    }

    // 视频捕捉卡
    CVideoCard *pVideoCard = GetVideoCard();
    GetDevCfg("videocard", szDriver, szConnStr1, szConnStr2, bStd, nType, bUsed);
    pVideoCard->SetDevIdAndName(DEV_VideoCard, g_sDevNameArray[DEV_VideoCard]);
    if (!pVideoCard->InitDev(szDriver, szConnStr1, szConnStr2, bStd, nType)) {
        sError = QString("标准化视频捕捉设备初始化失败,请检查参数配置[%1 %2 %3]")
                     .arg(szDriver, szConnStr1, szConnStr2);
        DebugLog(sError);
        // return false;

    } else {
        DebugLog(QString("加载视频捕捉设备库[%1]成功!").arg(szDriver));
    }

    // IO卡
    CIOCard *pIOCard = GetIOCard();
    pIOCard->SetMode(pDevAdapter->bUseDevAdapter());
    pIOCard->SetDevIdAndName(DEV_IOCard, g_sDevNameArray[DEV_IOCard]);
    GetDevCfg(QString("IOCard"), szDriver, szConnStr1, szConnStr2, bStd, nType, bUsed);
    pIOCard->SetbUseStdIO(bStd);
    if (!pIOCard->InitDev(szDriver, szConnStr1, szConnStr2, bStd, nType)) {
        sError = QString("IO卡初始化失败");
        // return false;
    }
    //车牌识别

    for (int i = 0; i < MAX_VPR_NUM; ++i) {
        QString sSection = QString("VPR%1").arg(i);
        GetDevCfg(sSection, szDriver, szConnStr1, szConnStr2, bStd, nType, bUsed);
        CVPRDev *pVPRDev = GetVPRDev(i);
        if (!pVPRDev) continue;
        QString sVprDevName = g_sDevNameArray[DEV_VPR + i];
        pVPRDev->SetDevIdAndName(DEV_VPR + i, sVprDevName);
        pVPRDev->SetbUseStdVPR(bStd);
        if (!pVPRDev->InitDev(szDriver, szConnStr1, szConnStr2, bStd, nType)) {
            sError = QString("车牌识别%1初始化失败").arg(i);
            DebugLog(sError);
        } else {
            DebugLog(QString("车牌识别%1初始化成功").arg(i));
        }
    }
    //字符叠加器
    GetDevCfg(QString("VDM"), szDriver, szConnStr1, szConnStr2, bStd, nType, bUsed);
    if (!GetVDMDev()->InitDev(szDriver, szConnStr1, szConnStr2)) {
        DebugLog(QString("字符叠加器初始化失败"));
        sError = QString("字符叠加器初始化失败");
    } else
        DebugLog(QString("字符叠加器初始化成功"));

    // TODO 根据车道类型选择是否加载
    //计重设备
    GetDevCfg(QString("Weight"), szDriver, szConnStr1, szConnStr2, bStd, nType, bUsed);
    GetWeightDev()->SetDevIdAndName(DEV_Weight, g_sDevNameArray[DEV_Weight]);
    if (!GetWeightDev()->InitDev(szDriver, szConnStr1, szConnStr2)) {
        DebugLog(QString("计重设备初始化失败"));
        sError = QString("计重设备初始化失败");
    } else
        DebugLog(QString("计重设备初始化成功"));

    //车型识别设备
    GetDevCfg(QString("Vcr"), szDriver, szConnStr1, szConnStr2, bStd, nType, bUsed);
    GetVCRDev()->SetDevIdAndName(DEV_VCR, g_sDevNameArray[DEV_VCR]);
    GetVCRDev()->SetSendVideo(true);

    if (!GetVCRDev()->InitDev(szDriver, szConnStr1, szConnStr2, 0, nType)) {
        DebugLog(QString("车型识别设备初始化失败"));
        sError = QString("车型识别设备初始化失败");
    } else
        DebugLog(QString("车型识别设备初始化成功"));

    //车辆外轮廓尺寸检测仪
    GetDevCfg(QString("VehicleOutline"), szDriver, szConnStr1, szConnStr2, bStd, nType, bUsed);
    GetVehicleOutlineDev()->SetDevIdAndName(DEV_VehicleOutline, g_sDevNameArray[DEV_VehicleOutline]);
    if (bUsed){
        if (!GetVehicleOutlineDev()->InitDev(szDriver, szConnStr1, szConnStr2)) {
            DebugLog(QString("车辆外轮廓尺寸检测仪初始化失败"));
            sError = QString("车辆外轮廓尺寸检测仪初始化失败");
        } else
            DebugLog(QString("车辆外轮廓尺寸检测仪初始化成功"));
    }else{
        DebugLog(QString("车辆外轮廓尺寸检测仪未启用"));
    }

    //自助收费外置屏
    GetDevCfg(QString("AutoExTollScreen"), szDriver, szConnStr1, szConnStr2, bStd, nType, bUsed);
    GetAutoExTollScreen()->SetDevIdAndName(DEV_AutoExTollScreen,
                                           g_sDevNameArray[DEV_AutoExTollScreen]);
    if (!GetAutoExTollScreen()->InitDev(szDriver, szConnStr1, szConnStr2)) {
        DebugLog(QString("自助收费外屏初始化失败"));
        sError = QString("自助收费外屏初始化失败");
    } else
        DebugLog(QString("自助收费外屏初始化成功"));

    if (Ptr_Info->IsExitLane() || Ptr_Info->bGrantPaperCard()) {
        GetDevCfg(QString("printer"), szDriver, szConnStr1, szConnStr2, bStd, nType, bUsed);
        CPrinterDevice *pPrinter = GetPrinterDev();
        pPrinter->SetDevIdAndName(DEV_Printf, g_sDevNameArray[DEV_Printf]);
        if (!pPrinter->InitDev(szDriver, szConnStr1, szConnStr2, bStd, nType)) {
            DebugLog(QString("打印机初始化失败"));
        } else {
            DebugLog(QString("打印机初始化成功"));
        }
    }
    if (Ptr_Info->IsExitLane()) {
        GetDevCfg(QString("MobilePayNet"), szDriver, szConnStr1, szConnStr2, bStd, nType, bUsed);
        if (!m_pMobilePay) {
            if (0 == nType) {
                m_pMobilePay = new MobilePayTW();
            } else
                m_pMobilePay = new MobilePayJX();
        }
        // MobilePayTW *pMobilePay = GetMobilePayTw();
        m_pMobilePay->SetDevIdAndName(DEV_MobilePay, g_sDevNameArray[DEV_MobilePay]);
        if (!m_pMobilePay->InitDev(szDriver, szConnStr1, szConnStr2, 0, nType)) {
            DebugLog(QString("移动支付初始化失败"));
        } else {
            DebugLog(QString("移动支付初始化成功"));
        }

        GetDevCfg(QString("TwsmPay"), szDriver, szConnStr1, szConnStr2, bStd, nType, bUsed);
        CSpEventDev *pSpEventDev = GetSpEventDev();
        pSpEventDev->SetDevIdAndName(DEV_TwsmPay, g_sDevNameArray[DEV_TwsmPay]);
        if (!pSpEventDev->InitDev(szDriver, szConnStr1, szConnStr2, 0, nType)) {
            DebugLog(QString("特微壁挂扫码初始化失败"));
        } else {
            DebugLog(QString("特微壁挂扫码初始化成功"));
        }
    }
    return true;
}

void CDeviceFactory::ReleaseAllDev()
{
//    DebugLog(QString("开始释放天线"));
//    for (int i = 0; i < 2; ++i) {
//        if (m_pRsuDevs[i]) {
//            m_pRsuDevs[i]->deleteLater();
//        }
//    }
    DebugLog(QString("开始释放天线"));
    for (int i = 0; i < 2; ++i) {
        if (m_pRsuDevs[i]) {
            // 直接删除，而不是使用deleteLater()
            delete m_pRsuDevs[i];
            m_pRsuDevs[i] = NULL;
        }
    }

    if (m_pSpClient) {
        delete m_pSpClient;
        m_pSpClient = NULL;
        DebugLog("移动支付终端释放完毕");
    }

    if (m_pMobilePay) {
        delete m_pMobilePay;
        m_pMobilePay = NULL;
        DebugLog("移动支付接口释放完毕");
    }

    if (m_pVideoCard) {
        DebugLog(QString("开始释放videocard"));
        m_pVideoCard->CloseDev();
        delete m_pVideoCard;
        m_pVideoCard = NULL;
    }

    DebugLog("videocard释放完毕");
    for (int i = 0; i < MAX_FAREDISPLAYER_NUM; ++i) {
        if (m_pETCFareDisplayer[i]) {
            DebugLog(QString("开始释放费显%1").arg(i));
            m_pETCFareDisplayer[i]->CloseDev();
            delete m_pETCFareDisplayer[i];
            m_pETCFareDisplayer[i] = NULL;
        }
    }
    DebugLog("fareDisplayer释放完毕");

    for (int i = 0; i < MAX_VPR_NUM; ++i) {
        if (m_pVPRDevs[i]) {
            DebugLog(QString("开始释放VPR%1").arg(i));
            m_pVPRDevs[i]->CloseDev();
            delete m_pVPRDevs[i];
            m_pVPRDevs[i] = NULL;
        }
    }
    DebugLog("VPR释放完毕");

    CCardReader::CancelCardDetection(true);
    CCardReader::CloseAllOpenedReaders();

    for (int i = 0; i < m_nCardReaderNum; i++) {
        if (m_pcardRead[i]) {
            m_pcardRead[i]->CloseDev();
            delete m_pcardRead[i];
            m_pcardRead[i] = NULL;
            TraceLog(QString("CardReader%1释放完毕").arg(i));
        }
    }

    DebugLog("开始释放字符叠加器");
    if (m_pVDMDev) {
        m_pVDMDev->CloseDev();
        delete m_pVDMDev;
    }
    TraceLog(QString("字符叠加器释放完毕"));

    if (m_pVcrDev) {
        m_pVcrDev->CloseDev();
        delete m_pVcrDev;
    }
    TraceLog(QString("车型设备释放完毕"));

    if (m_pVehicleOutlineDev) {
        m_pVehicleOutlineDev->CloseDev();
        delete m_pVehicleOutlineDev;
        m_pVehicleOutlineDev = NULL;
    }
    TraceLog(QString("车辆外轮廓尺寸检测仪释放完毕"));

    DebugLog("开始释放卡机模块");
    if (m_pCardMachine) {
        m_pCardMachine->CloseDev();
        delete m_pCardMachine;
    }

    if (m_pPaymentMgr) {
        m_pPaymentMgr->CloseDev();
        delete m_pPaymentMgr;
    }
    DebugLog(QString("卡机释放完毕"));

    DebugLog("开始释放自助收费外屏模块");
    if (m_pAutoExTollScreen) {
        m_pAutoExTollScreen->CloseDev();
        delete m_pAutoExTollScreen;
    }
    DebugLog(QString("自助收费外屏模块释放完毕"));

    if (m_pPrinter) {
        m_pPrinter->CloseDev();
        delete m_pPrinter;
        DebugLog(QString("打印机释放完毕"));
    }

    if (m_pQrCode) {
        m_pQrCode->CloseDev();
        delete m_pQrCode;
    }

    if (m_pWtDev) {
        m_pWtDev->CloseDev();
        delete m_pWtDev;
    }
    TraceLog(QString("计重设备释放完毕"));
    DebugLog(QString("设备释放完毕"));
    return;
}

//设置设备状态信号与槽连接(应在初始化设备之前调用)
bool CDeviceFactory::ConnDevStateSignal(const QObject *receiver, const char *method)
{
    for (int i = 0; i < MAX_FAREDISPLAYER_NUM; ++i) {
        CFareDisplayer_GB *pFD = GetETCFareDisPlayer(i);
        if (!pFD) continue;
        if (!QObject::connect(pFD, SIGNAL(NotifyDevStatusChange(qint32, qint32)), receiver,
                              method)) {
            ErrorLog(QString("费显设备状态信号连接失败"));
            return false;
        }
    }

    for (int i = 0; i < MAX_VPR_NUM; ++i) {
        CVPRDev *pVpr = CDeviceFactory::GetVPRDev(i);
        if (!pVpr) continue;
        if (!QObject::connect(pVpr, SIGNAL(NotifyDevStatusChange(qint32, qint32)), receiver,
                              method)) {
            ErrorLog(QString("抓拍设备状态信号连接失败"));
            return false;
        }
    }

    if (!QObject::connect(GetWeightDev(), SIGNAL(NotifyDevStatusChange(qint32, qint32)), receiver,
                          method)) {
        ErrorLog("计重设备状态信号连接失败");
        return false;
    }

    if (!QObject::connect(GetVCRDev(), SIGNAL(NotifyDevStatusChange(qint32, qint32)), receiver,
                          method)) {
        ErrorLog("车型设备状态信号连接失败");
        return false;
    }

    if(!QObject::connect(GetVehicleOutlineDev(), SIGNAL(NotifyDevStatusChange(qint32, qint32)), receiver, method)){
        ErrorLog("车辆外轮廓尺寸检测仪状态信号连接失败");
        return false;
    }

    for (int i = 1; i <= m_nCardReaderNum; ++i) {
        CCardReader *pCardReader = GetCardReader(i);
        if (pCardReader) {
            if (!QObject::connect(pCardReader, SIGNAL(NotifyDevStatusChange(qint32, qint32)),
                                  receiver, method)) {
                ErrorLog(QString("读卡器设备状态信号连接失败"));
                return false;
            }
        }
    }

    if (!QObject::connect(GetIOCard(), SIGNAL(NotifyDevStatusChange(qint32, qint32)), receiver,
                          method)) {
        ErrorLog("IO卡设备状态信号连接失败");
        return false;
    }

    if (!QObject::connect(GetCardMachine(), SIGNAL(NotifyDevStatusChange(qint32, qint32)), receiver,
                          method)) {
        ErrorLog("卡机设备状态信号连接失败");
    }

    if (Ptr_Info->IsExitLane()) {
        if (!QObject::connect(GetPrinterDev(), SIGNAL(NotifyDevStatusChange(qint32, qint32)),
                              receiver, method)) {
            ErrorLog("打印机状态信号连接失败");
        }

        if (!QObject::connect(GetMobilePayTw(), SIGNAL(NotifyDevStatusChange(qint32, qint32)),
                              receiver, method)) {
            ErrorLog("移动支付状态信号连接失败");
        }
        if (!QObject::connect(GetSpEventDev(), SIGNAL(NotifyDevStatusChange(qint32, qint32)),
                              receiver, method)) {
            ErrorLog("壁挂扫码状态信号连接失败");
        }
    } else {
        CPrinterDevice *pPrinter = GetPrinterDev();
        if (Ptr_Info->bGrantPaperCard()) {
            if (!QObject::connect(pPrinter, SIGNAL(NotifyDevStatusChange(qint32, qint32)), receiver,
                                  method)) {
                ErrorLog("支票打印机状体状态信号连接失败");
            }
        }
    }
    return true;
}

void CDeviceFactory::DisConnDevStateSignal(const QObject *receiver, const char *method)
{
    for (int i = 0; i < MAX_FAREDISPLAYER_NUM; ++i) {
        CFareDisplayer_GB *pFD = GetETCFareDisPlayer(i);
        if (!pFD) continue;
        QObject::disconnect(pFD, SIGNAL(NotifyDevStatusChange(qint32, qint32)), receiver, method);
    }

    for (int i = 0; i < MAX_VPR_NUM; ++i) {
        CVPRDev *pVpr = GetVPRDev(i);
        if (!pVpr) continue;
        QObject::disconnect(pVpr, SIGNAL(NotifyDevStatusChange(qint32, qint32)), receiver, method);
    }
    QObject::disconnect(GetIoCard(), SIGNAL(NotifyDevStatusChange(qint32, qint32)), receiver,
                        method);
    QObject::disconnect(GetVCRDev(), SIGNAL(NotifyDevStatusChange(qint32, qint32)), receiver,
                        method);
    QObject::disconnect(GetWeightDev(), SIGNAL(NotifyDevStatusChange(qint32, qint32)), receiver,
                        method);
    for (int index = 1; index <= m_nCardReaderNum; index++) {
        CCardReader *pCardReader = CDeviceFactory::GetCardReader(index);
        if (!pCardReader) continue;
        QObject::disconnect(pCardReader, SIGNAL(NotifyDevStatusChange(qint32, qint32)), receiver,
                            method);
    }
    QObject::disconnect(GetCardMachine(), SIGNAL(NotifyDevStatusChange(qint32, qint32)), receiver,
                        method);

    if (Ptr_Info->IsExitLane()) {
        QObject::disconnect(GetPrinterDev(), SIGNAL(NotifyDevStatusChange(qint32, qint32)),
                            receiver, method);

        QObject::disconnect(GetMobilePayTw(), SIGNAL(NotifyDevStatusChange(qint32, qint32)),
                            receiver, method);

        QObject::disconnect(GetSpEventDev(), SIGNAL(NotifyDevStatusChange(qint32, qint32)),
                            receiver, method);
    }
}

void CDeviceFactory::FareDisplay_ShowHelpMsg(int nIndex, const QString &sMsg)
{
    if (nIndex >= MAX_FAREDISPLAYER_NUM) {
        return;
    }
    if (m_pETCFareDisplayer[nIndex]) {
        m_pETCFareDisplayer[nIndex]->ShowHelpMsg(sMsg);
    }
    return;
}

//根据索引值取对应的读写器，nIdex =0，1，2，3，4，5
CCardReader *CDeviceFactory::GetCardReader(int nIndex)
{
    if (nIndex + 1 > MAX_CARD_READER_NUM) return NULL;
    int nReaderIndex = nIndex;
    if (!m_pcardRead[nReaderIndex]) m_pcardRead[nReaderIndex] = new CCardReader(nReaderIndex);
    return m_pcardRead[nReaderIndex];
}

void CDeviceFactory::StartAlarm(int nIndex, int nMSeconds)
{
    if (nIndex >= DevIndex_Second) {
        if (m_pIOCard) m_pIOCard->StartAlarm(nMSeconds);
    }

    int nDevIndex = nIndex;
    if (nDevIndex > DevIndex_Second) nDevIndex = DevIndex_Second;
    CFareDisplayer_GB *pFare = GetETCFareDisPlayer(nDevIndex);
    if (!pFare) return;
    pFare->StartAlarmTime(nMSeconds);
}

void CDeviceFactory::StopAlarm(int nIndex)
{
    DebugLog("停止报警");
    int nDevIndex = nIndex;
    if (nIndex >= MAX_FAREDISPLAYER_NUM) {
        nDevIndex = DevIndex_Second;
    }
    if (nDevIndex < MAX_FAREDISPLAYER_NUM) {
        if (m_pETCFareDisplayer[nIndex]) {
            m_pETCFareDisplayer[nIndex]->OnStopAlarm();
        }
    }
    if (nDevIndex >= DevIndex_Second) {
        if (m_pIOCard) m_pIOCard->OnStopLightWarn();
    }
    return;
}

/**
 * @brief
 * @param bAllow -true 放行 false -落杆
 * @return
 */
void CDeviceFactory::SetAllow(bool bAllow)
{
    if (bAllow) {
        if (m_pIOCard) {
            m_pIOCard->SetAllowPass();
        }
        if (m_pETCFareDisplayer[DevIndex_Second]) {
            m_pETCFareDisplayer[DevIndex_Second]->Light(1, 0);
        }
    } else {
        if (m_pIOCard) m_pIOCard->SetRefusePass();
        if (m_pETCFareDisplayer[DevIndex_Second]) {
            m_pETCFareDisplayer[DevIndex_Second]->Light(0, 0);
        }
    }
    return;
}

CRsuDev *CDeviceFactory::GetRsuDev(int nIndex)
{
    if (nIndex >= 0 && nIndex < MAX_RSU_NUM)
        return m_pRsuDevs[nIndex];
    else
        return NULL;
}

CCardMgr *CDeviceFactory::GetCardMachine()
{
    if (!m_pCardMachine) {
        m_pCardMachine = new CCardMgr();
    }
    return m_pCardMachine;
}

CPaymentMachine *CDeviceFactory::GetPayMentMgr()
{
    if (!m_pPaymentMgr) m_pPaymentMgr = new CPaymentMachine();
    return m_pPaymentMgr;
}

CPrinterDevice *CDeviceFactory::GetPrinterDev()
{
    if (!m_pPrinter) m_pPrinter = new CPrinterDevice();
    return m_pPrinter;
}

//MobilePayBase *CDeviceFactory::GetMobilePayTw()
//{
//    if (!m_pMobilePay) {
//        QString szDriver, szConnStr1, szConnStr2;
//        quint8 bStd = 0;
//        int nType = 0;
//        int bUsed = false;
//        GetDevCfg(QString("MobilePayNet"), szDriver, szConnStr1, szConnStr2, bStd, nType, bUsed);
//        if (0 == nType) {
//            m_pMobilePay = new MobilePayTW();
//        } else
//            m_pMobilePay = new MobilePayJX();
//    }
//    /*
//    if (!m_pMobilePayTw) {
//        m_pMobilePayTw = new MobilePayTW();
//    }*/
//    return m_pMobilePay;
//}

MobilePayBase *CDeviceFactory::GetMobilePayTw()
{
    if (!m_pMobilePay) {
        QString szDriver, szConnStr1, szConnStr2;
        quint8 bStd = 0;
        int nType = 0;
        int bUsed = false;
        GetDevCfg(QString("MobilePayNet"), szDriver, szConnStr1, szConnStr2, bStd, nType, bUsed);
        if (0 == nType) {
            m_pMobilePay = new MobilePayTW();
        } else if (3 == nType) {
            m_pMobilePay = new MobilePayDual();
        } else {
            m_pMobilePay = new MobilePayJX();
        }
    }
    /*
    if (!m_pMobilePayTw) {
        m_pMobilePayTw = new MobilePayTW();
    }*/
    return m_pMobilePay;
}

CSpEventDev *CDeviceFactory::GetSpEventDev()
{
    if (!m_pSpClient) m_pSpClient = new CSpEventDev();
    return m_pSpClient;
}

QString CDeviceFactory::GetReaderStatus(QString &sManuId, QString &sReaderVer)
{
    QString sReturn;
    for (int i = 0; i < m_nCardReaderNum; ++i) {
        if (m_pcardRead[i]) {
            if (0 == m_pcardRead[i]->GetStatus()) {
                sReturn += "|1";
                QString stmpReaderVer, stmpAPIVer;
                sManuId += "|0";

                m_pcardRead[i]->GetCardReaderVer(stmpReaderVer, stmpAPIVer);
                sReaderVer += QString("|%1").arg(stmpReaderVer);
            } else {
                sReturn += "|0";
                sManuId += "|0";
                sReaderVer += "|0";
            }
        }
    }

    if (sReturn.length() > 0) {
        sReturn.remove(0, 1);
        sManuId.remove(0, 1);
        sReaderVer.remove(0, 1);
    } else {
        sReturn = QString("2");
        sManuId = QString("0");
        sReaderVer = QString("0");
    }
    return sReturn;
}

QString CDeviceFactory::GetVPRStatus()
{
    QString sReturn;
    for (int i = 0; i < MAX_VPR_NUM; ++i) {
        if (m_pVPRDevs[i]) {
            if (0 == m_pVPRDevs[i]->GetStatus()) {
                sReturn += "|1";
            } else
                sReturn += "|0";
        }
    }
    if (sReturn.length() > 0)
        sReturn.remove(0, 1);
    else
        sReturn = QString("2");
    return sReturn;
}

QString CDeviceFactory::GetRsuStatus(QString &sManuId, QString &HardVer, QString &softVer)
{
    QString sReturn;
    for (int i = 0; i < 2; ++i) {
        if (m_pRsuDevs[i]) {
            if (m_pRsuDevs[i]->bIniteOk()) {
                sReturn += "|1";
                sManuId +=
                    QString("|%1")
                        .arg(m_pRsuDevs[i]->GetRsuBaseInfo()->rsuManulId, 2, 16, QLatin1Char('0'))
                        .toUpper();
                quint16 bVersion =
                    qFromLittleEndian<quint16>(m_pRsuDevs[i]->GetRsuBaseInfo()->rsuVersion);
                softVer += QString("|%1").arg(
                    bVersion);  // Raw2HexStr(m_pRsuDevs[i]->GetRsuBaseInfo()->rsuVersion, 2));
                HardVer += QString("|0");
            } else {
                if (!m_pRsuDevs[i]->bUsed()) {
                    sReturn += "|2";
                } else
                    sReturn += "|0";
                sManuId += "|0";
                HardVer += "|0";
                softVer += "|0";
            }
        }
    }
    if (sReturn.length() > 0) {
        sReturn.remove(0, 1);
    } else
        sReturn = QString("2");
    if (sManuId.length() > 0)
        sManuId.remove(0, 1);
    else
        sManuId = QString("0");

    if (HardVer.length() > 0)
        HardVer.remove(0, 1);
    else
        HardVer = QString("0");
    if (softVer.length() > 0) {
        softVer.remove(0, 1);
    } else
        softVer = QString("0");
    return sReturn;
}

CInvoiceQrCode *CDeviceFactory::GetQrCode() { return m_pQrCode; }
