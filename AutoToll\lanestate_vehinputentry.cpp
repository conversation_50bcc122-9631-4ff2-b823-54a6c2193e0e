#include "lanestate_vehinputentry.h"

#include "IOCard.h"
#include "cfarecalcunit.h"
#include "ctransinfoshared.h"
#include "dlgmain.h"
#include "etclanectrl.h"
#include "formreverse.h"
#include "lanesoundplay.h"
#include "speventmgr.h"
#include "stdlog.h"
#include "vehweightinfo.h"
#include "remotemsgmgr.h"
#include "remotecontrolmgr.h"
#include "funcmenu.h"
#include "printcontent.h"
#include "formlogin.h"
#include "papercardmgr.h"
#include "crcutil.h"
#include "etcprompt.h"

CLaneState_VehInputEntry::CLaneState_VehInputEntry(QObject *parent)
    : CLaneState_VehInput(CAbstractState::StateID_VehInputEn,parent), m_nCurCardIndex(0) {

    if (Ptr_Info->IsEntryLane()) {
        connect(&m_timerCardMgr, SIGNAL(timeout()), this, SLOT(OnCardMgrTimer()));
        // 连接定时器停止信号
        connect(Ptr_ETCCtrl, SIGNAL(NotifyStopCardMgrTimer()), this, SLOT(OnStopCardMgrTimer()));

    }
}

CLaneState_VehInputEntry::~CLaneState_VehInputEntry() {
    // 停止定时器
    StopCardMgrTimer();
}

void CLaneState_VehInputEntry::MoveMgrHead()
{
    if (Ptr_Info && Ptr_Info->GetCardMgrType() == 31){

        CCardMgr *pCardMgr = CDeviceFactory::GetCardMachine();
        if (pCardMgr) {
            DebugLog(QString("伸出机械臂"));
            pCardMgr->MoveCardHead(true, true);

        }
    }
}

// 停止卡机定时器函数
void CLaneState_VehInputEntry::StopCardMgrTimer(int type)
{
    DebugLog(QString("StopCardMgrTimer:%1").arg(type));
    if (m_timerCardMgr.isActive()) {
        if(type != 0)
        {
            if (Ptr_Info && Ptr_Info->GetCardMgrType() == 31){

                CCardMgr *pCardMgr = CDeviceFactory::GetCardMachine();
                if (pCardMgr) {
                    DebugLog(QString("伸出机械臂"));
                    pCardMgr->MoveCardHead(true, true);

                }
            }
        }
        m_timerCardMgr.stop();
        m_nTimerCardMgrInterval2Flag = false;
        DebugLog("停止入口车道卡机定时器");
    }
}

void CLaneState_VehInputEntry::StartCardMgrTimer()
{
    int tempInterval = 0;

    if (!m_timerCardMgr.isActive()) {
        if(Ptr_Info)
        {
            tempInterval = m_nTimerCardMgrInterval2Flag ? m_nTimerCardMgrInterval2+Ptr_Info->GetCardTimerInterval() : Ptr_Info->GetCardTimerInterval();
        }else
        {
            tempInterval = m_nTimerCardMgrInterval2Flag ? m_nTimerCardMgrInterval2+timerCardMgrTime : timerCardMgrTime;
        }
        //        CTransInfo temp;
        //        Ptr_ETCCtrl->bAbnormalVehOfTheFirstVehInQueue(temp);
        //        CTransInfo* temp = Ptr_ETCCtrl->GetLastTransInfo(DevIndex_Second);//取后天线的最新交易

        //        if(!temp || (temp && !temp->bTransOk()))
        //        {
        //            m_timerCardMgr.start(tempInterval);
        //            DebugLog(QString("开启入口车道卡机定时器，间隔：%1ms，延时标志：%2")
        //                     .arg(tempInterval).arg(m_nTimerCardMgrInterval2Flag ? "已设置" : "未设置"));
        //        }else
        //        {
        //            DebugLog(QString("入口车道首车无未交易车辆，不开启定时器"));
        //        }
        CTransInfo* temp = Ptr_ETCCtrl->RemoveTransInfoFromQue(true, false);
        if(temp)
            DebugLog(QString("transResult is %1, transState is %2").arg(temp->transResult).arg(temp->transState));
        else
            DebugLog(QString("未找到队首车辆"));

        if(!temp || (temp && !temp->bTransOk()))
        {
            m_timerCardMgr.start(tempInterval);
            DebugLog(QString("开启入口车道卡机定时器，间隔：%1ms，延时标志：%2")
                     .arg(tempInterval).arg(m_nTimerCardMgrInterval2Flag ? "已设置" : "未设置"));
        }else
        {
            DebugLog(QString("入口车道首车无未交易车辆，不开启定时器"));

        }
    }
}

int CLaneState_VehInputEntry::mtcKeyPressed(MtcKeyPressedEvent *mtcKeyEvent)
{
    DebugLog(QString("处理按键:%1").arg(mtcKeyEvent->key()));
    int nRlt = CLaneState_VehInput::mtcKeyPressed(mtcKeyEvent);
    if (mtcKeyEvent->processed()) {
        return nRlt;
    }

    if (mtcKeyEvent->isFuncKey()) {
        int nFuncKey = mtcKeyEvent->func();
        mtcKeyEvent->setKeyType(KC_Func);
        switch (nFuncKey) {
        case KeyUp: {
#ifdef QT_DEBUG
            // GetMainDlg()->LoadVCRResult();
            //RemoteMsgMgr::GetSingleInst()->SendLaneReportEventReq(
            //            1, CSpEventMgr::SpEvent_AbnormalCard, QString("黑名单卡"));
            // CDeviceFactory::GetCardMachine()->EmitPressHelpButton(1);
            //ProcessCardMachineEvent(CCardMgr::CardMgrEvent_PressButton, 1, 1);
            // 测试车辆外轮廓数据获取功能
            DebugLog("=== 开始测试车辆外轮廓数据获取功能 ===");
            
            // 获取称重设备实例
            CWtSysDev_ZC *pWeightDev = CDeviceFactory::GetWeightDev();
            if (!pWeightDev) {
                QString sMsg = QString("测试失败：获取称重设备实例失败");
                DebugLog(sMsg);
                ShowErrorMessage(sMsg);
                break;
            }
            
            // 创建测试用的车辆轴重信息对象
            CVehAxisInfo testVehAxisInfo;
            
            // 调用车辆外轮廓数据获取功能
            pWeightDev->GetVehicleOutlineInfo(testVehAxisInfo);
            
            // 添加模拟的轴重数据（用于测试完整功能）
            testVehAxisInfo.AddRawAxis(1, 6500, 25);   // 第1轴：6.5吨，25km/h
            testVehAxisInfo.AddRawAxis(1, 8200, 25);   // 第2轴：8.2吨，25km/h
            testVehAxisInfo.AddRawAxis(2, 15300, 25);  // 第3-4轴：15.3吨，25km/h
            DebugLog("车辆外轮廓测试：添加模拟轴重数据 - 轴1:6.5T, 轴2:8.2T, 轴3-4:15.3T, 总重:30T");
            
            // 显示测试结果
            QString sResultMsg;
            bool bHasOutlineData = (testVehAxisInfo.GetVehLength() > 0 || 
                                   testVehAxisInfo.GetVehWidth() > 0 || 
                                   testVehAxisInfo.GetVehHeight() > 0);
            bool bHasWeightData = (testVehAxisInfo.GetConfirmedTotalRawWeight() > 0);
            
            if (bHasOutlineData || bHasWeightData) {
                sResultMsg = QString("测试数据获取成功！\n外轮廓: %1×%2×%3mm\n总重: %4kg\n轴数: %5")
                            .arg(testVehAxisInfo.GetVehLength())
                            .arg(testVehAxisInfo.GetVehWidth())
                            .arg(testVehAxisInfo.GetVehHeight())
                            .arg(testVehAxisInfo.GetConfirmedTotalRawWeight())
                            .arg(testVehAxisInfo.GetConfirmedSingleAxisNum());
                
                DebugLog(QString("车辆综合测试成功 - 外轮廓[%1×%2×%3mm], 总重[%4kg], 轴数[%5]")
                        .arg(testVehAxisInfo.GetVehLength())
                        .arg(testVehAxisInfo.GetVehWidth())
                        .arg(testVehAxisInfo.GetVehHeight())
                        .arg(testVehAxisInfo.GetConfirmedTotalRawWeight())
                        .arg(testVehAxisInfo.GetConfirmedSingleAxisNum()));
            } else {
                sResultMsg = QString("测试数据获取失败\n请检查设备连接状态");
                DebugLog("车辆综合测试：未获取到有效数据");
            }
            
            // 显示测试结果到界面
            ShowErrorMessage(sResultMsg);
            
            // 如果获取到有效的测试数据，添加到称重系统中
            if (bHasOutlineData || bHasWeightData) {
                VehWeightInfo::GetVehWeightInfo()->AddVeh(testVehAxisInfo);
                DebugLog("车辆综合测试数据已添加到称重系统");
            } 
            // 即使没有获取到测试数据，也刷新界面显示
            GetMainDlg()->NotifyWeightDataChange();
            
            DebugLog("=== 车辆外轮廓及称重数据综合测试完成 ===");
            
            // 原有的ETC提示测试代码（保留备用）
            /*
            CJsonBuilder builder;
            builder.AddKeyValue(QString("vlp"), QString::fromUtf8("赣A12345"));
            builder.AddKeyValue_Int(QString("vlpc"), 1);
            builder.AddKeyValue_Int(QString("type"), 2);
            builder.AddKeyValue_Int(QString("remindLevel"), 2);
            builder.AddKeyValue_Int(QString("cashTollCnt"), 10);
            builder.AddKeyValue(QString("remark"), QString::fromUtf8("现金高频用户，重点推广，办理ETC每年通行费预计优惠56.12元"));

            // 生成JSON字符串并赋值给promptMsg
            QString promptMsg = QString("{%1}").arg(builder.CreateJsonStr().remove(0, 1));

            // 使用通知处理器处理ETC提示，解耦业务逻辑
            ETCNotificationHandler::GetInstance()->HandleETCPromptNotification(promptMsg);
            */
#endif
            break;
        }
        case KeyLeft: {
#ifdef QT_DEBUG
            //GetMainDlg()->LoadVPRResult();
            RemoteMsgMgr::GetSingleInst()->SendCallVideoReq(QString("1"));

            //                ProcessCardMachineEvent(CCardMgr::CardMgrEvent_UpCardOk, 1, 1);
#endif
            break;
        }
        case KeyRight: {
#ifdef QT_DEBUG
            RemoteMsgMgr::GetSingleInst()->SendCallVideoReq(QString("2"));

#endif
            break;
        }
        case KeyDown: {
#ifdef QT_DEBUG
            //            GetMainDlg()->InitOpenStationInfo_New();
            //            GetMainDlg()->LoadVCRResult();
            // ProcessCardMachineEvent(CCardMgr::CardMgrEvent_TakeCard, 1, 1);
            RemoteMsgMgr::GetSingleInst()->SendLaneReportEventReq(1, CSpEventMgr::SpEvent_Truck_OBU,
                                                                  QString("赣T00001\n货6\n等待确认"));
#endif
            break;
        }
        case KeyCash: {
            //取车辆信息
            CVehInfo vehInfo;
            CVehAxisInfo vehAxisInfo;
            QString sError, sFDMsg;
            if (!Ptr_ETCCtrl->bHasWaitingTransVeh(DevIndex_Manual)) {
                sError = QString("未检测到车辆");
                return 0;
            }
            CTransInfo *pcurTransInfo = Ptr_ETCCtrl->GetCurTransInfo(DevIndex_Manual);
            if (!pcurTransInfo) {
                sError = QString("无车辆到来");
                return 0;
            }
            bool bRlt = GetVehInfoForCPC_New(false, pcurTransInfo, vehInfo, vehAxisInfo, sError,
                                             sFDMsg);
            if (!bRlt) {
                ShowErrorMessage(sError);
                return 0;
            }
            //入口只执行一次刷卡，每次需要按现金键，因此参数传入null,读卡事件中的continue无效
            CCardReader::StartCardDetection(NULL);
            break;
        }
        case KeyPaperCard: {
            if (!Ptr_Info->bGrantPaperCard()) {
                break;
            }
            QString sError, sFDMsg;
            bool bAlarm = false;
            QString sOpName;
            if (!CheckAndSetOpState(opState_IsWritingCard, sOpName)) {
                sError = QString("%1,按纸卡键无效").arg(sOpName);
                ShowErrorMessage(sError);
                DebugLog(sError);
                return false;
            }
            bool bRlt = OnEntryPaperCardKeyEvent(sError, sFDMsg, bAlarm);
            if (!bRlt) {
                DisplayTransError_CPC(DevIndex_Second, sError, sFDMsg, bAlarm);
                SetOpState(opState_None);
                CTransInfo *pcurTransInfo = Ptr_ETCCtrl->GetCurTransInfo(DevIndex_Manual);
                if (pcurTransInfo) {
                    pcurTransInfo->ClearTransInfo();
                }
            } else {
                SetOpState(opState_None);
                // m_nCurCardIndex = nIndex;
            }
            break;
        }
        default: {
            mtcKeyEvent->setKeyType(KC_NONE);
        }
        }
    }
    return 0;
}

bool CLaneState_VehInputEntry::ProcessDIEvent(qint32 nDI, bool bStatus, QString &sError)
{
    bool bRlt = CLaneState_VehInput::ProcessDIEvent(nDI, bStatus, sError);

    if (bRlt) {
        return true;
    }

    if (DI_LoopExist == nDI) {
        if (bStatus) {
            DebugLog("入口车辆压上存在线圈");
            if (Ptr_Info->bHaveCardMgr()) {
                // 当m_nCardMgrType=31时启动定时器
                if (Ptr_Info && Ptr_Info->GetCardMgrType() == 31) {
                    // 设置定时器间隔
                    //m_timerCardMgr.start(timerCardMgrTime);
                    //StartCardMgrTimer();
                    //DebugLog("入口车道启动卡机定时器(m_nCardMgrType=31)");
                }else{
                    GetMainDlg()->ShowPromptMsg("请按键取卡");
                    CDeviceFactory::FareDisplay_ShowHelpMsg(DevIndex_Second,
                                                            QString("领取通行卡\n请按取卡按钮"));
                    //                CLaneSoundPlay::PlayHelpSnd(CLaneSoundPlay::PlayEvent_WelCome);
                    CCardMgr *pCardMgr = CDeviceFactory::GetCardMachine();
                    if (pCardMgr) {
                        bool bMoved = false;

                        // 根据卡头伸缩配置决定逻辑
                        if (Ptr_Info->GetCardHeadStretchConfig() == 0) {
                            // 配置为0时，只要存在线圈有信号就发送卡头伸出指令
                            DebugLog(QString("卡头伸缩配置为0，检测到线圈信号，直接伸出卡机头"));
                            bMoved = true;
                        } else {
                            // 配置非零时，依据现有逻辑判断（车型判断）
                            VcrResult vcrResult;
                            bool bRlt = CDeviceFactory::GetVCRDev()->GetFistVcrResult(vcrResult);
                            if (bRlt) {
                                if (vcrResult.vehclass > 0) {
                                    if (vcrResult.vehclass == VC_Truck1 || (vcrResult.vehclass < VC_Car3)) {
                                        DebugLog(QString("车型识别首辆车车型:%1,移动卡机头")
                                                 .arg(vcrResult.vehclass));
                                        bMoved = true;
                                    }
                                }
                            }
                        }

                        if (bMoved) pCardMgr->MoveCardHead(bMoved, true);
                        if (Ptr_Info->bRemoteControl()) {
                            DebugLog("卡机外屏：提示按键取卡， HT_En_WaitSendCard");
                            CDeviceFactory::GetAutoExTollScreen()->ShowHelp(
                                        AutoExTollScreen::HT_En_WaitSendCard, "");
                        } else {
                            //非远控版本，播放卡机提示音
                            SleeperThread::msleep(100);
                            pCardMgr->PlaySnd(CCardMgr::SndId_HintPressButton);
                        }
                    }
                }
            }
            NotifyFrontRsuHasTrans();

        } else {
            if (Ptr_Info->bHaveCardMgr()) {
                CCardMgr *pCardMgr = CDeviceFactory::GetCardMachine();
                if (pCardMgr) {
                    pCardMgr->MoveCardHead(false);
                    if (Ptr_Info->bRemoteControl()) {
                        DebugLog("卡机外屏：回到等待车辆界面， HT_En_WaitVehicleArrive");
                        CDeviceFactory::GetAutoExTollScreen()->ShowHelp(
                                    AutoExTollScreen::HT_En_WaitVehicleArrive, "");
                    }
                }
            }
        }
    }
    return true;
}

bool CLaneState_VehInputEntry::ProcessCardMachineEvent(int nEvent, int nPos, int nIndex)
{
    QString sError;
    QString sFDMsg;
    CCardMgr *pCardMgr = CDeviceFactory::GetCardMachine();
    DebugLog(QString("卡机事件:%1").arg(nEvent));

    switch (nEvent) {
    case CBaseCardMgr::CardMgrEvent_PressButton: {
        QString sOpName;

        DebugLog(QString("按键取卡,nPos:%1,nIndex:%2").arg(nPos).arg(nIndex));
        bool bAlarm = false;
        StopVehStayOutTimer();

        if (CAbstractState::m_bDataSaveFailed) {
            sError = QString("数据存储异常,停止交易,请联系维护人员!");
            DebugLog(sError);
            bAlarm = true;
            DisplayTransError_CPC(DevIndex_Second, sError, sFDMsg, bAlarm);
            return false;
        }

        if (m_IsInputingVLP) {
            sError = QString("正在修改车牌,请稍后");
            sFDMsg = QString("正在输入车辆信息\n请稍后");
            DisplayTransError_CPC(DevIndex_Second, sError, sFDMsg, bAlarm);
            return false;
        }

        m_OtherXMt.lock();
        int nOpType = m_OtherXVehInfo.bOpType;
        QString sOtherMsg = m_OtherXVehInfo.GetEventMsg();
        m_OtherXMt.unlock();

        if (nOpType == Operate_Pause) {
            sError = QString("%1 \n等待人工处理").arg(sOtherMsg);
            ShowErrorMessage(sError);
            DebugLog(sError);
            return false;
        }

        if (!CheckAndSetOpState(opState_IsWritingCard, sOpName)) {
            sError = QString("%1,按键无效").arg(sOpName);
            ShowErrorMessage(sError);
            DebugLog(sError);
            return false;
        }

        bool bRlt = ProcessPressButton(nPos, nIndex, sError, sFDMsg, bAlarm, true);
        if (!bRlt) {
            DisplayTransError_CPC(DevIndex_Second, sError, sFDMsg, bAlarm);
            SetOpState(opState_None);
            CTransInfo *pcurTransInfo = Ptr_ETCCtrl->GetCurTransInfo(DevIndex_Manual);
            if (pcurTransInfo) {
                pcurTransInfo->ClearTransInfo();
            }
        } else {
            // SetOpState(opState_WaitUpCard);
            // m_nCurCardIndex = nIndex;
        }
        break;
    }
    case CBaseCardMgr::CardMgrEvent_UpCardOk: {
        DebugLog(QString("%1,%2出卡成功,等待取卡").arg(nPos).arg(nIndex));

        if (nPos == 3) {
            sError = QString("%1号卡机出卡失败").arg(nIndex);
            DebugLog(sError);
            ShowErrorMessage(sError);
            break;
        }

        if (m_OpState != opState_WaitUpCard) {
            DebugLog(QString("当前状态%1,非等待发卡状态下,收到发卡成功指令").arg(m_OpState));
            break;
        }
        SetOpState(opState_WaitPickCard);
        pCardMgr->SetUpCardFailed(nIndex, false);
        //            CLaneSoundPlay::PlayHelpSnd(CLaneSoundPlay::PlayEvent_PickCard);
        if (Ptr_Info->bRemoteControl()) {
            //通知远控平板
            Ptr_RemoteCtrl->ChangeState_EntrySendCard();
            DebugLog("卡机外屏：等待取卡");
            CDeviceFactory::GetAutoExTollScreen()->ShowHelp(
                        AutoExTollScreen::HT_En_WaitTakeCard, "");
        } else {
            //非远控版本，播放卡机提示音
            SleeperThread::msleep(80);
            pCardMgr->PlaySnd(CCardMgr::SndId_HintPickCard);
        }
        break;
    }
    case CBaseCardMgr::CardMgrEvent_UpCardFailed: {
        sError = QString("%1号卡机出卡失败,重新出卡").arg(nIndex);
        DebugLog(sError);
        int nFailedCnt = pCardMgr->SetUpCardFailed(nIndex, true);
        if (nFailedCnt <= 2) {
            DebugLog(QString("出卡失败,重新出卡"));
            CDeviceFactory::GetCardMachine()->UpCard(nIndex);
        }
        ShowErrorMessage(sError);
        break;
    }
    case CBaseCardMgr::CardMgrEvent_TakeCard: {
        //抬杆放行
        DebugLog("卡片被取走");
        if (m_OpState == opState_WaitPickCard || m_OpState == opState_WaitUpCard) {
            Ptr_ETCCtrl->SetAllowPass(true, false);
            //                CLaneSoundPlay::PlayHelpSnd(CLaneSoundPlay::PlayEvent_SaveCard);
            if (Ptr_Info->bRemoteControl()) {
                //通知远控平板
                Ptr_RemoteCtrl->ChangeState_Input();
                //如果是远控版本，播放外屏提示音
                DebugLog("卡机外屏：取走卡片后， HT_En_WaitVehicleLeave");
                CDeviceFactory::GetAutoExTollScreen()->ShowHelp(
                            AutoExTollScreen::HT_En_WaitVehicleLeave, "");
            } else {
                //非远控版本，播放卡机提示
                CDeviceFactory::GetCardMachine()->PlaySnd(CCardMgr::SndId_Thank);
                SleeperThread::msleep(80);
            }
            pCardMgr->MoveCardHead(false);
            SetOpState(opState_None);
        }
        break;
    }
    case CBaseCardMgr::CardMgrEvent_CardInsert: {
        pCardMgr->SetCardMgrReady(nIndex, true);
        pCardMgr->SetBadCard(nIndex, false);
        //重新备卡,
        if (m_OpState == opState_WaitPickCard || m_OpState == opState_WaitUpCard) {
            DebugLog(QString("%1号卡机备卡时,当前为等待取卡状态,当前等待取卡卡机:%2")
                     .arg(nIndex)
                     .arg(m_nCurCardIndex));
            if (nIndex == m_nCurCardIndex) {
                DebugLog(
                            QString("等待取卡时，检测到%1号卡机备卡,认为卡片已经取走").arg(nIndex));
                //通知远控平板（这里可能没用，感觉是出口卡机收卡）
                Ptr_RemoteCtrl->ChangeState_Input();
                Ptr_ETCCtrl->SetAllowPass(true, false);
                CLaneSoundPlay::PlayHelpSnd(CLaneSoundPlay::PlayEvent_SaveCard);
                CDeviceFactory::GetCardMachine()->PlaySnd(CCardMgr::SndId_Thank);
                SleeperThread::msleep(80);
                pCardMgr->MoveCardHead(false);
                SetOpState(opState_None);
            }
        }

        /*
            if (nPos == CBaseCardMgr::CardPos_Low || nPos == CBaseCardMgr::CardPos_Up) {
                if (!pCardMgr->HaveBadCard(nIndex)) {
                    DebugLog(QString("%1号卡机备卡成功,当前无坏卡,不处理读卡事件").arg(nIndex));
                    return false;
                    break;
                }
                pCardMgr->SetBadCard(nIndex, false);
                QString sOpName;
                if (!CheckAndSetOpState(opState_IsWritingCard, sOpName)) {
                    DebugLog(QString("%1,暂不处理备卡后自动读卡").arg(sOpName));
                    return false;
                }
                bool bAlarm = false;
                bool bRlt = ProcessPressButton(nPos, nIndex, sError, sFDMsg, bAlarm, false);
                if (!bRlt) {
                    DisplayTransError_CPC(DevIndex_Second, sError, sFDMsg, bAlarm);
                    SetOpState(opState_None);
                    CTransInfo *pcurTransInfo = Ptr_ETCCtrl->GetCurTransInfo(DevIndex_Manual);
                    if (pcurTransInfo) {
                        pcurTransInfo->ClearTransInfo();
                    }
                } else {
                    SetOpState(opState_WaitUpCard);
                }
            }*/
        /*
            if (!Ptr_ETCCtrl->IsBarUp() && HasVehInLane()) {
                CLaneSoundPlay::PlayHelpSnd(CLaneSoundPlay::PlayEvent_WelCome);
                if (pCardMgr) {
                    pCardMgr->PlaySnd(CCardMgr::SndId_HintPressButton);
                }
            }*/
        break;
    }

    case CBaseCardMgr::CardMgrEvent_Relocal: {
        DebugLog(QString("%1,%2,卡片重定位完毕").arg(nPos).arg(nIndex));
        pCardMgr->SetCardMgrReady(nIndex, true);
        SleeperThread::msleep(60);
        if (nPos == CBaseCardMgr::CardPos_Low || nPos == CBaseCardMgr::CardPos_Up) {
            if (!pCardMgr->HaveBadCard(nIndex)) {
                DebugLog(QString("%1号卡机重定位卡片,之前无坏卡,不处理读卡事件").arg(nIndex));
                break;
            }

            QString sOpName;
            if (!CheckAndSetOpState(opState_IsWritingCard, sOpName)) {
                sError = QString("%1,重定位卡片暂不处理").arg(sOpName);
                ShowErrorMessage(sError);
                return false;
            }
            bool bAlarm = false;
            bool bRlt = ProcessPressButton(nPos, nIndex, sError, sFDMsg, bAlarm, false);
            if (!bRlt) {
                DisplayTransError_CPC(DevIndex_Second, sError, sFDMsg, bAlarm);
                SetOpState(opState_None);
                CTransInfo *pcurTransInfo = Ptr_ETCCtrl->GetCurTransInfo(DevIndex_Manual);
                if (pcurTransInfo) {
                    pcurTransInfo->ClearTransInfo();
                }
                return false;
            } else {
                SetOpState(opState_WaitUpCard);
                m_nCurCardIndex = nIndex;
            }
        }
        break;
    }

    case CBaseCardMgr::CardMgrEvent_VehReverse: {
        DebugLog(QString("收到入口卡机倒车事件，开始执行倒车处理"));
        
        // 调用倒车处理函数
        bool bReverseResult = ProcessVehReverse();
        if (bReverseResult) {
            DebugLog(QString("入口倒车处理完成"));
        } else {
            DebugLog(QString("入口倒车处理失败"));
        }
        break;
    }

    default:
        break;
    }
    return true;
}

bool CLaneState_VehInputEntry::OnOpenCardEvent(int nReadId, int nCardType, bool &bAlarm,
                                               QString &sError, bool &bContinueReadCard)
{
    StopVehStayOutTimer();
    if (!Ptr_ETCCtrl->bHasWaitingTransVeh(DevIndex_Manual)) {
        sError = QString("未检测到车辆");
        return false;
    }

    CTransInfo *pCurTransInfo = Ptr_ETCCtrl->GetCurTransInfo(DevIndex_Manual);
    if (!pCurTransInfo) {
        SetOpState(opState_None);
        return false;
    }
    pCurTransInfo->SetTransState(CTransInfo::Ts_IsReadingIcc);
    bool bRlt = ProcessOnOpenCardEvent(nReadId, nCardType, bAlarm, sError, bContinueReadCard);
    if (!bRlt) {
        pCurTransInfo->ClearTransInfo();
    }
    return bRlt;
}

bool CLaneState_VehInputEntry::OnInputVehInfoByRemote(qint32 vehClass, int nPlateColor,
                                                      QString sVehPlate, qint32 vehType,
                                                      QString axleType)
{
    //入口，要所有信息都一次性发下来
    if (vehClass != -1 && nPlateColor != -1 && vehType != -1) {
        SetInputVehClass((CVehClass)vehClass);
        SetInputVehPlate(sVehPlate, nPlateColor);
        SetInputVehType((CUnionVehType)vehType);
        if (!axleType.isEmpty()) {
            SetInputAxleType(axleType);
        } else {
            //由计重取出轴组，填写
            if (VehWeightInfo::GetVehWeightInfo()->GetVehCount() > 0) {
                CVehAxisInfo firstAxisInfo;
                VehWeightInfo::GetVehWeightInfo()->GetFirstVeh(&firstAxisInfo);
                SetInputAxleType(QString::number(firstAxisInfo.GetConfirmedAxisGroup()));
            }
        }
        //播放提示
        if (Ptr_Info->bHaveCardMgr()) {
            GetMainDlg()->ShowPromptMsg("请按键取卡");
            CDeviceFactory::FareDisplay_ShowHelpMsg(DevIndex_Second,
                                                    QString("领取通行卡\n请按取卡按钮"));
            CLaneSoundPlay::PlayHelpSnd(CLaneSoundPlay::PlayEvent_WelCome);
            CCardMgr *pCardMgr = CDeviceFactory::GetCardMachine();
            if (pCardMgr) {
                //                pCardMgr->MoveCardHead(true);
                if (Ptr_Info->bRemoteControl()) {
                    DebugLog(
                                "收到远控输入车辆信息，通知卡机外屏：提示按键取卡， HT_En_WaitSendCard");
                    CDeviceFactory::GetAutoExTollScreen()->ShowHelp(
                                AutoExTollScreen::HT_En_WaitSendCard, "");
                } else {
                    SleeperThread::msleep(100);
                    pCardMgr->PlaySnd(CCardMgr::SndId_HintPressButton);
                }
            }
        }
    }
    return true;
}

bool CLaneState_VehInputEntry::OnEntryPaperCardKeyEvent(QString &sError, QString &sFDMsg,
                                                        bool &bAlarm)
{
    sFDMsg.clear();
    sError.clear();

    bAlarm = false;

    if (!Ptr_ETCCtrl->bHasWaitingTransVeh(DevIndex_Manual)) {
        sError = QString("未检测到车辆");
        return false;
    }
    // 1判断是否允许发卡
    if (!CheckAllowWriteCPCCard(false, sError, sFDMsg, bAlarm, false)) {
        return false;
    }
    CTransInfo *pCurTransInfo = Ptr_ETCCtrl->GetCurTransInfo(DevIndex_Manual);
    if (!pCurTransInfo) {
        return false;
    }
    pCurTransInfo->SetTransState(CTransInfo::Ts_IsReadingIcc);

    //取车辆信息
    CVehInfo vehInfo;
    CVehAxisInfo vehAxisInfo;

    bool bRlt = GetVehInfoForCPC_New(false, pCurTransInfo, vehInfo, vehAxisInfo, sError, sFDMsg);
    if (!bRlt) {
        bAlarm = true;
        return false;
    }

    RemoteMsgMgr::GetSingleInst()->SendCPCTransMsg_Entry(pCurTransInfo, 2);

    PaperCardNo_Param paperInfo;

    bool bPaperCard = false;
    if (Ptr_Info->bGrantPaperCard()) {
        bPaperCard = CPaperCardMgr::GetSingleInst()->GetPaperCardNO(vehInfo.VehClass, paperInfo);
    }
    if (bPaperCard) {
        DebugLog(
                    QString("纸卡处理.时间范围:%1,%2").arg(paperInfo.beginTime).arg(paperInfo.endTime));
    } else {
        sError = QString("当前无电子纸票");
        return false;
    }

    QString sPlate = GB2312toUnicode(vehInfo.szVehPlate);

    int nErrorCode = 0;

    //同车不允许发卡
    bool bHaveTrans = QryTransShare(MediaType_CPC, sPlate, sError);
    if (bHaveTrans) {
        return false;
    }
    QDateTime curTime = QDateTime::currentDateTime();
    bool bPermit = Ptr_Info->CheckVehPassPermit(vehInfo.VehClass, curTime);
    if (!bPermit) {
        CVehPassPermitInfo permitInfo;
        if (!CheckVehInPermitList(sPlate, permitInfo)) {
            QString sVehName = GetVehClassName(vehInfo.VehClass);
            DebugLog(QString("%1,%2 限时通行").arg(sVehName).arg(sPlate));
            sError = QString("<%1,%2>夜间禁止通行").arg(sPlate).arg(sVehName);
            QString sHelp = QString("<确定>键继续发卡,<取消>键停止");
            bool bRlt =
                    ShowInformation_Help(QString("夜间禁止通行"), sError, sHelp, true, QString(""),
                                         CSpEventMgr::SPEvent_StopPassByTime, true);
            if (bRlt) {
                FormLogin loginUser;
                COperInfo operInfo;
                if (loginUser.auth(operInfo)) {
                    pCurTransInfo->m_bPassPermit = true;
                    pCurTransInfo->m_operInfo = operInfo;
                    pCurTransInfo->m_AuthTime = curTime;
                    AddToPermitList(sPlate, operInfo);
                } else {
                    return false;
                }

            } else {
                nErrorCode = CSpEventMgr::SPEvent_StopPassByTime;
                return false;
            }
        } else {
            pCurTransInfo->m_bPassPermit = true;
            pCurTransInfo->m_operInfo = permitInfo.operInfo;
            pCurTransInfo->m_AuthTime = permitInfo.occurTime;
        }
    }

    int bInList = CheckVehList(DevIndex_Manual, sPlate, vehInfo.nVehPlateColor, nErrorCode, sError);
    if (bInList > 0) {
        if (!ShowInformation_Help(QString("黑名单车辆"), sError,
                                  QString("<确定>键继续发卡,<取消>键返回"), true,
                                  QString("黑名单车辆"), CSpEventMgr::SpEvent_BlackCar, true)) {
            sFDMsg = QString("黑名单车辆");
            bAlarm = false;
            return false;
        }
        if (3 == bInList) {
            int nRlt = CFuncMenu::ShowMenu_PrePayBList();
            if (0 == nRlt) return false;
            pCurTransInfo->m_nPrepayList = nRlt;
        }
    }

    CTransInfo transInfo;
    bHaveTrans = Ptr_ETCCtrl->CheckVehhasTransByVLP(sPlate, transInfo);

    if (bHaveTrans) {
        if (transInfo.bTransOk()) {
            sError =
                    QString("车辆已领卡,时间:%1").arg(transInfo.TransTime.toString("MM-dd hh:mm:ss"));
            return false;
        }
    }

    if (vehInfo.VehClass > VC_Truck) {
        bool bCheckRlt =
                CheckVCAndAxis(vehInfo, pCurTransInfo->GetVehAxisNum(), false, sError, sFDMsg);
        if (!bCheckRlt) {
            bAlarm = true;
            return false;
        }
    }

    if (bPaperCard) {
        bool printResult = ProcessPaperCard(paperInfo, sError);
        if (printResult) {
            DebugLog("发纸卡成功!");
            return true;
        } else {
            sFDMsg = QString("纸卡打印失败");
            DebugLog("发纸卡失败!");
            bAlarm = true;
            return false;
        }
    }
    return false;
}

bool CLaneState_VehInputEntry::ProcessPaperCard(const PaperCardNo_Param &paperInfo, QString &sError)
{
    CTransInfo *pCurTransInfo = Ptr_ETCCtrl->GetCurTransInfo(DevIndex_Manual);
    // TODO 处理纸卡数据保存、填报文
    QDateTime TransTime = QDateTime::currentDateTime();

    QString sLastGantryHex;
    QString sPlate = GB2312toUnicode(pCurTransInfo->VehInfo.szVehPlate);
    COpenGantryInfo openGantryInfo;
    bool bHasOpenGantry =
            GetOpenGantyInfo_New(pCurTransInfo->VehInfo.VehClass, sPlate,
                                 pCurTransInfo->VehInfo.nVehPlateColor, NULL, openGantryInfo);
    if (bHasOpenGantry) {
        sLastGantryHex = openGantryInfo.sGantryHex;
        DebugLog(QString("车辆经过开放式门架:%1,时间:%2")
                 .arg(sLastGantryHex)
                 .arg(openGantryInfo.uEntryTime));
    }
    //取当前门架信息
    bool bOpenGantry = false;
    CVirGantryInfo curGantryInfo;
    bool bQryResult = CTollGantryMgr::GetTollGantryMgr()->GetCurGantryInfo(
                sLastGantryHex, curGantryInfo, bOpenGantry);
    if (!bQryResult) {
        sError = QString("纸卡门架%1信息查询失败").arg(sLastGantryHex);
        DebugLog(sError);
        if (sLastGantryHex.isEmpty()) return false;
        sLastGantryHex.clear();
        bQryResult = CTollGantryMgr::GetTollGantryMgr()->GetCurGantryInfo(
                    sLastGantryHex, curGantryInfo, bHasOpenGantry);
        if (!bQryResult) {
            DebugLog(QString("再次取当前门架信息失败"));
            return false;
        }
    } else {
        if (bOpenGantry) {
            time_t dwCurTime = QDateTime::currentDateTime().toTime_t();
            if (dwCurTime > openGantryInfo.uEntryTime) {
                if (dwCurTime - openGantryInfo.uEntryTime > curGantryInfo.nMaxTime) {
                    DebugLog(QString("开放式门架时间超过时间阈值:%1,取当前实体门架信息")
                             .arg(curGantryInfo.nMaxTime));
                    sLastGantryHex.clear();
                    if (!CTollGantryMgr::GetTollGantryMgr()->GetCurGantryInfo(
                                sLastGantryHex, curGantryInfo, bHasOpenGantry)) {
                        sError = QString("取当前门架失败");
                        return false;
                    }
                }
            }
        }
    }
    pCurTransInfo->SetVirGantryInfo(curGantryInfo, bOpenGantry, openGantryInfo.sVlpId);

    // 1、初始化入口过站信息
    if (bOpenGantry) {
        QDateTime openGantryTime;
        if (openGantryInfo.uEntryTime > 0) {
            if (UnixTime2QDateTime_GB(openGantryInfo.uEntryTime, openGantryTime)) {
                TransTime = openGantryTime;
                DebugLog(
                            QString("开放式门架时间:%1").arg(TransTime.toString("yyyy-MM-dd hh:mm:ss")));
            }
        }
    }

    //
    CCardTollInfo cardTollInfo;
    FillCardTollInfo(cardTollInfo, TransTime, pCurTransInfo);
    // CCardFileConverter::CardTollInfo2CPCTollInfoRaw(&cpcIccInfo.cpcTollInfoRaw,&cpcIccInfo.cardTollInfo);
    DebugLog(QString("sLastGantryHex:%1, stationName:%2 nStationId:%3,当前站信息:%4,交易时间:%5 ")
             .arg(sLastGantryHex)
             .arg(pCurTransInfo->m_curGantryInfo.sStationName)
             .arg(pCurTransInfo->m_curGantryInfo.nStationId)
             .arg(Ptr_Info->GetStationID())
             .arg(TransTime.toString("yyyy-MM-dd hh:mm:ss")));

    CVehEntryInfo entryInfo;
    GetEntryInforEntry(TransTime, cardTollInfo, pCurTransInfo, entryInfo);
    pCurTransInfo->SetVehEntryInfo(entryInfo, TransTime);

    pCurTransInfo->SetPaperInfo(paperInfo.lCurNo, paperInfo.encryption_key);
    pCurTransInfo->m_BeginTime = QDateTime::currentDateTime().toMSecsSinceEpoch();

    //门架计费
    QString sExStationHex = "";
    quint8 feeVehClass = pCurTransInfo->VehInfo.VehClass;
    if (CTollGantryMgr::GetTollGantryMgr()->CheckNeedGrantryCalcFee()) {
        if (!CFareCalcUnit::CalcGantryFee(*pCurTransInfo, feeVehClass, sExStationHex,
                                          pCurTransInfo->bHolidayFree, TransTime, sError)) {
            sError = QString("门架计费失败,Error:%1").arg(sError);
            pCurTransInfo->SetOncePaySpEvent(OncePay_Sp_FeeQryFeeFailed);
            return false;
        } else {
            ErrorLog(QString("门架计费成功,fee:%1,payfee:%2,discountFee:%3,feeProSumLocal:%4,"
                             "计费车型:%5")
                     .arg(pCurTransInfo->gantryFeeInfo.cardFee)
                     .arg(pCurTransInfo->gantryFeeInfo.payFee)
                     .arg(pCurTransInfo->gantryFeeInfo.discountFee)
                     .arg(pCurTransInfo->gantryFeeInfo.feeProvSumLocal)
                     .arg(feeVehClass));
        }
    }

    /*
    qint32 dwLastMoney, dwConsumeMoney;
    pCurTransInfo->cpcIccInfo.cardTollInfo.dwTotalFee =
        pCurTransInfo->GetLastMoney_Entry(dwLastMoney, dwConsumeMoney);
    FillCPCRoadAndTollCellInfo(pCurTransInfo, pCurTransInfo->cpcIccInfo);
    memcpy(&cpcRoadInfoRaw, pCurTransInfo->cpcIccInfo.cpcpRoadInfoRaw.data(),
           sizeof cpcRoadInfoRaw);
    memcpy(&cpcTollCellInfoRaw, pCurTransInfo->cpcIccInfo.cpcTollCellInfoRaw.data(),
           sizeof cpcTollCellInfoRaw);
    */

    CTransInfo *pLastTransInfo = Ptr_ETCCtrl->GetLastTransInfo(DevIndex_Manual);
    pCurTransInfo->nDetectNum = 1;
    pCurTransInfo->m_endTime = QDateTime::currentDateTime().toMSecsSinceEpoch();

    QString sPaperNo = QString("%1").arg(paperInfo.lCurNo);
    if (!PrintPaperInfo(sPaperNo, paperInfo.encryption_key, pCurTransInfo, sError)) {
        // sError = QString("纸票打印失败");
        return false;
    }
    CPaperCardMgr::GetSingleInst()->IncCardNo(paperInfo);
    CPaperCardMgr::GetSingleInst()->SavePaperTransInfo_Entry(pCurTransInfo);
    //通知外屏，播放语音
    if (Ptr_Info->bRemoteControl()) {
        //通知远控平板
        Ptr_RemoteCtrl->ChangeState_EntrySendCard();
        DebugLog("卡机外屏：提示取纸卡通行");
        CDeviceFactory::GetAutoExTollScreen()->ShowHelp(AutoExTollScreen::HT_En_TakePaperCard, "");
    } else {
        //非远控版本，播放卡机提示音
        SleeperThread::msleep(80);
        CCardMgr *pCardMgr = CDeviceFactory::GetCardMachine();
        pCardMgr->PlaySnd(CCardMgr::SndId_HintPickCard);
    }

    pCurTransInfo->SaveTo(*pLastTransInfo);
    pLastTransInfo->CompleteTrans(DevIndex_Manual, TransPT_None, NULL, Tr_Successed,
                                  CTransInfo::Ts_WaitToSave);
    //发卡成功不抬杆，只有等待取卡后再抬
    Ptr_ETCCtrl->CompleteTrans(Tr_Successed, DevIndex_Manual, true);
    
    OnTransFinished_Manual(true, 0, sError, NULL);

    SetOpState(opState_None);
    //填写计费信息
    TransInfoShared Value;
    Value.nStatus = 0;
    Value.Transtime = TransTime;
    Value.nLaneid = Ptr_Info->GetLaneId();
    Value.nMediatype = MediaType_Paper;
    Value.nVehtype = pCurTransInfo->VehInfo.VehClass;
    CTransInfoShared::GetTransInfoShared()->SendData(sPlate, Value);
    pCurTransInfo->ClearTransInfo();
    return true;
}

bool CLaneState_VehInputEntry::PrintPaperInfo(const QString &sPaperNo, const QString &sKey,
                                              CTransInfo *pTransInfo, QString &sError)
{
    //打印二维码
    CPaperTicketContent ptc;
    ptc.sEntryNetId = Ptr_Info->GetHexStationID().left(4);
    ptc.sEntryHex = Ptr_Info->GetHexStationID().right(4);
    ptc.sEntryStation = Ptr_Info->GetStationName();
    ptc.nVehClass = pTransInfo->VehInfo.VehClass;
    ptc.sVehPlate = GB2312toUnicode(pTransInfo->VehInfo.szVehPlate);
    ptc.bPlateColor = pTransInfo->VehInfo.nVehPlateColor;

    QByteArray bPlateHex = ptc.sVehPlate.toLocal8Bit().toHex();
    // TODO 取票号
    ptc.sTicketNo = QString("%1").arg(sPaperNo);
    ptc.t = pTransInfo->TransTime;
    QString sEnTime = pTransInfo->TransTime.toString("yyyyMMddhhmmss");
    //|%5|%6|%7|%8|")
    ptc.sQrCode = QString("%1|%2|%3|%4|%5|%6|%7|%8|")
            .arg(Ptr_Info->GetHexStationID())
            .arg(Ptr_Info->GetLaneId())
            .arg(ptc.sTicketNo)
            .arg(pTransInfo->VehInfo.VehClass)
            .arg(pTransInfo->VehInfo.nVehPlateColor)
            .arg(QString::fromLocal8Bit(bPlateHex.data()))
            .arg(sEnTime)
            .arg(1);

    QString stmp = QString("%1%2").arg(ptc.sQrCode).arg(sKey);
    QByteArray bData = stmp.toUtf8();
    QByteArray bMd5 = QCryptographicHash::hash(bData, QCryptographicHash::Md5).toHex().toUpper();
    ptc.sQrCode += QString("%1").arg(QString::fromLocal8Bit(bMd5.data()));

    /*
    quint16 CalcCRC = qToBigEndian(CRC16_X25((uchar*)bData.data(),bData.size()));
    QString sCrc16 = Raw2HexStr((quint8*)&CalcCRC,2);
    */
    DebugLog(QString("paper qrcode:%1").arg(ptc.sQrCode));
    CPrinterDevice *pPrinter = CDeviceFactory::GetPrinterDev();
    if (pPrinter) {
        /*
        bool bPaperStatus = pPrinter->GetPaperStatus();
        if (!bPaperStatus) {
            sError = QString("打印机缺纸");

            return false;
        }*/
        pPrinter->SetPaperTicketContent(ptc);
        if (!pPrinter->PrintPaperTicket()) {
            DebugLog(QString("纸票打印失败"));
#ifdef QT_DEBUG
            return true;
#else
            return false;
#endif
        } else
            return true;
    }
    return false;
}

bool CLaneState_VehInputEntry::ProcessPressButton(int nPos, int nIndex, QString &sError,
                                                  QString &sFDMsg, bool &bAlarm, bool bCheckAllow)
{
    sFDMsg.clear();
    sError.clear();
    if (!Ptr_ETCCtrl->bHasWaitingTransVeh(DevIndex_Manual)) {
        sError = QString("未检测到车辆");
        return false;
    }
    // 1判断是否允许发卡
    if (!CheckAllowWriteCPCCard(bCheckAllow, sError, sFDMsg, bAlarm, false)) {
        return false;
    }
    CTransInfo *pCurTransInfo = Ptr_ETCCtrl->GetCurTransInfo(DevIndex_Manual);
    if (!pCurTransInfo) {
        return false;
    }
    pCurTransInfo->SetTransState(CTransInfo::Ts_IsReadingIcc);

    int nReaderIndex = nPos;
    CCardReader *pCardReader = CDeviceFactory::GetCardReader(nReaderIndex);
    if (!pCardReader) {
        sError = QString("卡机对应读写器[%1]没配置").arg(nReaderIndex);
        return false;
    }

    CCardMgr *pCardMachine = CDeviceFactory::GetCardMachine();
    if (!pCardMachine) {
        sError = QString("未配置卡机");
        return false;
    }

    //取车辆信息
    CVehInfo vehInfo;
    CVehAxisInfo vehAxisInfo;

    bool bRlt = GetVehInfoForCPC_New(false, pCurTransInfo, vehInfo, vehAxisInfo, sError, sFDMsg);
    if (!bRlt) {
        bAlarm = true;
        return false;
    }

    RemoteMsgMgr::GetSingleInst()->SendCPCTransMsg_Entry(pCurTransInfo, 2);

    PaperCardNo_Param paperInfo;

    bool bPaperCard = false;
    if (Ptr_Info->bGrantPaperCard()) {
        bPaperCard = CPaperCardMgr::GetSingleInst()->GetPaperCardNO(vehInfo.VehClass, paperInfo);
    }
    if (bPaperCard) {
        DebugLog(
                    QString("纸卡处理.时间范围:%1,%2").arg(paperInfo.beginTime).arg(paperInfo.endTime));
    }

    bRlt = pCardReader->GetAuthResult();
    if ((!bPaperCard) && (!bRlt)) {
        sError = QString("读写器PSAM未授权,无法发卡");
        sFDMsg = QString("读写器未授权");
        bAlarm = true;
        return false;
    }

    if (!bPaperCard) {
        if (!pCardMachine->CardMgrReady(nIndex)) {
            sError = QString("正在回收坏卡,请稍后");
            DebugLog(QString("%1号卡机正在回收坏卡").arg(nIndex));
            return false;
        }
    }

    QString sPlate = GB2312toUnicode(vehInfo.szVehPlate);
    if (vehInfo.GBVehType == UVT_BigTruck) {
        /*
        CBigVehInfo bigVehInfo;
        if (HandleBigVehSelection(sPlate, vehInfo.nVehPlateColor, bigVehInfo)) {
            vehInfo.GBVehType = UVT_BigTruck;
            pCurTransInfo->m_sCertNo = bigVehInfo.cerNo;
        }
        */
        pCurTransInfo->m_sCertNo = GetCertNo();
    }

    int nErrorCode = 0;

    //同车不允许发卡
    bool bHaveTrans = QryTransShare(MediaType_CPC, sPlate, sError);
    if (bHaveTrans) {
        return false;
    }
    /*
        if (vehInfo.VehClass >= VC_Car2 && vehInfo.VehClass <= VC_Car4) {
            QDateTime curTime = QDateTime::currentDateTime();
            if (curTime.time().hour() >= 2 && curTime.time().hour() <= 4) {
                sError = QString("客2以上车辆限时通行");
                if (!ShowInformation_Help(
                        QString("客车限时通行"), sError, QString("<确定>键继续放行,<取消>键返回"),
       true, QString("客车限时通行"), CSpEventMgr::SPEvent_StopPassByTime, true)) { nErrorCode =
       CSpEventMgr::SPEvent_StopPassByTime; sFDMsg = QString("客车限时通行"); bAlarm = true; return
       false;
                }
                DebugLog(QString("客车%1限时通行，允许通行").arg(vehInfo.VehClass));
            }
        }
      */
    QDateTime curTime = QDateTime::currentDateTime();
    bool bPermit = Ptr_Info->CheckVehPassPermit(vehInfo.VehClass, curTime);
    if (!bPermit) {
        CVehPassPermitInfo permitInfo;
        if (!CheckVehInPermitList(sPlate, permitInfo)) {
            QString sVehName = GetVehClassName(vehInfo.VehClass);
            DebugLog(QString("%1,%2 限时通行").arg(sVehName).arg(sPlate));
            sError = QString("<%1,%2>夜间禁止通行").arg(sPlate).arg(sVehName);
            QString sHelp = QString("<确定>键继续发卡,<取消>键停止");
            bool bRlt =
                    ShowInformation_Help(QString("夜间禁止通行"), sError, sHelp, true, QString(""),
                                         CSpEventMgr::SPEvent_StopPassByTime, true);
            if (bRlt) {
                FormLogin loginUser;
                COperInfo operInfo;
                if (loginUser.auth(operInfo)) {
                    pCurTransInfo->m_bPassPermit = true;
                    pCurTransInfo->m_operInfo = operInfo;
                    pCurTransInfo->m_AuthTime = curTime;
                    AddToPermitList(sPlate, operInfo);
                } else {
                    return false;
                }

            } else {
                nErrorCode = CSpEventMgr::SPEvent_StopPassByTime;
                return false;
            }
        } else {
            pCurTransInfo->m_bPassPermit = true;
            pCurTransInfo->m_operInfo = permitInfo.operInfo;
            pCurTransInfo->m_AuthTime = permitInfo.occurTime;
        }
    }

    int bInList = CheckVehList(DevIndex_Manual, sPlate, vehInfo.nVehPlateColor, nErrorCode, sError);
    if (bInList > 0) {
        if (!ShowInformation_Help(QString("黑名单车辆"), sError,
                                  QString("<确定>键继续发卡,<取消>键返回"), true,
                                  QString("黑名单车辆"), CSpEventMgr::SpEvent_BlackCar, true)) {
            sFDMsg = QString("黑名单车辆");
            bAlarm = false;
            return false;
        }
        if (3 == bInList) {
            int nRlt = CFuncMenu::ShowMenu_PrePayBList();
            if (0 == nRlt) return false;
            pCurTransInfo->m_nPrepayList = nRlt;
        }
    }

    CTransInfo transInfo;
    bHaveTrans = Ptr_ETCCtrl->CheckVehhasTransByVLP(sPlate, transInfo);

    if (bHaveTrans) {
        if (transInfo.bTransOk()) {
            sError =
                    QString("车辆已领卡,时间:%1)").arg(transInfo.TransTime.toString("MM-dd hh:mm:ss"));
            return false;
        }
    }

    if (vehInfo.VehClass > VC_Truck) {
        bool bCheckRlt =
                CheckVCAndAxis(vehInfo, pCurTransInfo->GetVehAxisNum(), false, sError, sFDMsg);
        if (!bCheckRlt) {
            bAlarm = true;
            return false;
        }
    }

    if (bPaperCard) {
        /*
        CPrinterDevice *pPrinter = CDeviceFactory::GetPrinterDev();
        if (pPrinter) {
            bool bPaperStatus = pPrinter->GetPaperStatus();
            if (!bPaperStatus) {
                sError = QString("打印机缺纸");
                sFDMsg = QString("打印机缺纸");
                bAlarm = true;
                return false;
            }
        }*/

        bool printResult = ProcessPaperCard(paperInfo, sError);
        if (printResult) {
            DebugLog("发纸卡成功!");
            return true;
        } else {
            // sError=QString("纸卡处理")
            sFDMsg = QString("纸卡打印失败");
            DebugLog("发纸卡失败!");
            bAlarm = true;
        }
        return false;
    }

    int nOpenTimes = 0;
    bRlt = false;
    while (!bRlt) {
        bRlt = pCardReader->OpenCard();
        ++nOpenTimes;
        if (bRlt || 3 == nOpenTimes) break;
        if (!bRlt) SleeperThread::msleep(100);
    }

    if (!bRlt) {
        sError = QString("%1号卡机打开卡片失败").arg(nIndex);
        sFDMsg = QString("读卡失败\n请重按取卡按钮");
        int nBadCard = pCardMachine->SetBadCard(nIndex, true);
        DebugLog(QString("设置坏卡,当前次数%1").arg(nBadCard));
        if (nBadCard > 2) {
            sFDMsg = QString("发卡失败\n请重按取卡按钮");
        } else
            sFDMsg = QString("重新备卡\n请稍后");
        return false;
    }

    int nCardType = pCardReader->GetCardType();
    if (TYPE_CPC != nCardType) {
        DebugLog(QString("卡类型%1,非CPC卡").arg(nCardType));
        sError = QString("非CPC卡");
        int nBadCard = pCardMachine->SetBadCard(nIndex, true);
        if (nBadCard > 2) {
            sFDMsg = QString("发卡失败\n请重按取卡按钮");
        } else
            sFDMsg = QString("重新备卡\n请稍后");
        return false;
    }

    quint32 cpcCardMac = pCardReader->GetCardId();

    if (!ProcessCPCCardEvent(nPos, cpcCardMac, pCardReader, sError)) {
        int nBadCnt = pCardMachine->SetBadCard(nIndex, true);
        DebugLog(QString("设置坏卡,当前次数%1").arg(nBadCnt));
        if (nBadCnt > 2)
            sFDMsg = QString("发卡失败\n请重按取卡按钮");
        else
            sFDMsg = QString("重新备卡\n请稍后");
        return false;
    } else {
        pCardMachine->UpCard(nIndex);
        DebugLog("写卡成功,发送出卡指令");
        CTransInfo *pLastTransInfo = Ptr_ETCCtrl->GetLastTransInfo(DevIndex_Manual);
        if (pLastTransInfo) {
            QString sCardId = QString::fromAscii(pLastTransInfo->cpcIccInfo.cpcBasicInfo.sCardID);
            pCardMachine->SetLastCardId(nPos, sCardId);
        }
    }
    sError = QString("卡机正在发卡...");
    SetOpState(opState_WaitUpCard);
    m_nCurCardIndex = nIndex;
    return true;
}

bool CLaneState_VehInputEntry::ProcessCPCCardEvent(int nPos, quint32 dwCardMac,
                                                   CCardReader *pCardReader, QString &sError)
{
    // CVehInfo vehInfo;
    // GetCurVehInfo(vehInfo);

    CCPCBasicInfoRaw cpcBasicInfoRaw;
    memset(&cpcBasicInfoRaw, 0, sizeof cpcBasicInfoRaw);

    CCPCBatteryRaw cpcBatteryRaw;
    memset(&cpcBatteryRaw, 0, sizeof cpcBatteryRaw);

    CCPCTollInfoRaw cpcTollInfoRaw;
    memset(&cpcTollInfoRaw, 0, sizeof cpcTollInfoRaw);

    CCPCRoadInfoRaw_New cpcRoadInfoRaw;
    memset(&cpcRoadInfoRaw, 0, sizeof cpcRoadInfoRaw);
    CCPCTollCellInfoRaw_New cpcTollCellInfoRaw;
    memset(&cpcTollCellInfoRaw, 0, sizeof cpcTollCellInfoRaw);

    CCPCIccInfo cpcIccInfo;
    cpcIccInfo.cpcCardMac = dwCardMac;
    bool bRlt = pCardReader->ReadCPCCard_New(&cpcIccInfo.cpcBasicInfo, &cpcIccInfo.cardTollInfo,
                                             NULL, NULL, cpcBasicInfoRaw, cpcBatteryRaw,
                                             cpcTollInfoRaw, cpcRoadInfoRaw, cpcTollCellInfoRaw);

    if (!bRlt) {
        sError = QString("读卡失败");
        return false;
    }

    cpcIccInfo.MFef01.append((char *)&cpcBasicInfoRaw, sizeof cpcBasicInfoRaw);
    cpcIccInfo.MFef02.append((char *)&cpcBatteryRaw, sizeof cpcBatteryRaw);
    cpcIccInfo.cpcTollnfoRaw.append((char *)&cpcTollInfoRaw, sizeof cpcTollInfoRaw);

    if (cpcIccInfo.cpcBasicInfo.nBattery < 8) {
        sError = QString("卡片电量[%1]不足").arg(cpcIccInfo.cpcBasicInfo.nBattery);
        return false;
    }

    QString sCardId = QString::fromAscii(cpcIccInfo.cpcBasicInfo.sCardID);
    if (CCardFileConverter::IsValidEnFlag(cpcIccInfo.cardTollInfo.bPassStatus)) {
        sError = QString("卡片%1已发出").arg(sCardId);
        DebugLog(sError);
        if (nPos > 0) {
            CCardMgr *pCardMgr = CDeviceFactory::GetCardMachine();
            if (pCardMgr->CheckLastCardId(nPos, sCardId)) {
                QString sPos = 1 == nPos ? QString("上") : QString("下");
                DebugLog(
                            QString("卡片%1,上次已发出,请检查该卡片是否位于%2卡机").arg(sCardId).arg(sPos));
                sError = QString("卡片上次已发出,请检查读写器配置");
                return false;
            }
        }
        // return false;
    }

#ifndef QT_DEBUG
    if (!pCardReader->CPCClear(false)) {
        sError = QString("卡片%1清卡失败").arg(sCardId);
        return false;
    }
#endif

    CTransInfo *pCurTransInfo = Ptr_ETCCtrl->GetCurTransInfo(DevIndex_Manual);
    if (!pCurTransInfo) {
        return false;
    }
    QString sLastGantryHex;

    QString sPlate = GB2312toUnicode(pCurTransInfo->VehInfo.szVehPlate);
    COpenGantryInfo openGantryInfo;
    bool bHasOpenGantry =
            GetOpenGantyInfo_New(pCurTransInfo->VehInfo.VehClass, sPlate,
                                 pCurTransInfo->VehInfo.nVehPlateColor, NULL, openGantryInfo);
    if (bHasOpenGantry) {
        sLastGantryHex = openGantryInfo.sGantryHex;
        DebugLog(QString("车辆经过开放式门架:%1,时间:%2")
                 .arg(sLastGantryHex)
                 .arg(openGantryInfo.uEntryTime));
    }
    //取当前门架信息
    bool bOpenGantry = false;
    CVirGantryInfo curGantryInfo;
    bool bQryResult = CTollGantryMgr::GetTollGantryMgr()->GetCurGantryInfo(
                sLastGantryHex, curGantryInfo, bOpenGantry);
    if (!bQryResult) {
        sError = QString("门架%1信息查询失败").arg(sLastGantryHex);
        DebugLog(sError);
        if (sLastGantryHex.isEmpty()) return false;
        sLastGantryHex.clear();
        bQryResult = CTollGantryMgr::GetTollGantryMgr()->GetCurGantryInfo(
                    sLastGantryHex, curGantryInfo, bHasOpenGantry);
        if (!bQryResult) {
            DebugLog(QString("再次取当前门架信息失败"));
            return false;
        }
    } else {
        if (bOpenGantry) {
            time_t dwCurTime = QDateTime::currentDateTime().toTime_t();
            if (dwCurTime > openGantryInfo.uEntryTime) {
                if (dwCurTime - openGantryInfo.uEntryTime > curGantryInfo.nMaxTime) {
                    DebugLog(QString("开放式门架时间超过时间阈值:%1,取当前实体门架信息")
                             .arg(curGantryInfo.nMaxTime));
                    sLastGantryHex.clear();
                    if (!CTollGantryMgr::GetTollGantryMgr()->GetCurGantryInfo(
                                sLastGantryHex, curGantryInfo, bHasOpenGantry)) {
                        sError = QString("取当前门架失败");
                        return false;
                    }
                }
            }
        }
    }
    pCurTransInfo->SetVirGantryInfo(curGantryInfo, bOpenGantry, openGantryInfo.sVlpId);

    // 1、初始化入口过站信息
    QDateTime TransTime = QDateTime::currentDateTime();
    if (bOpenGantry) {
        QDateTime openGantryTime;
        if (openGantryInfo.uEntryTime > 0) {
            if (UnixTime2QDateTime_GB(openGantryInfo.uEntryTime, openGantryTime)) {
                TransTime = openGantryTime;
                DebugLog(
                            QString("开放式门架时间:%1").arg(TransTime.toString("yyyy-MM-dd hh:mm:ss")));
            }
        }
    }

    FillCardTollInfo(cpcIccInfo.cardTollInfo, TransTime, pCurTransInfo);
    // CCardFileConverter::CardTollInfo2CPCTollInfoRaw(&cpcIccInfo.cpcTollInfoRaw,&cpcIccInfo.cardTollInfo);
    DebugLog(QString("sLastGantryHex:%1, stationName:%2 nStationId:%3,当前站信息:%4,交易时间:%5 ")
             .arg(sLastGantryHex)
             .arg(pCurTransInfo->m_curGantryInfo.sStationName)
             .arg(pCurTransInfo->m_curGantryInfo.nStationId)
             .arg(Ptr_Info->GetStationID())
             .arg(TransTime.toString("yyyy-MM-dd hh:mm:ss")));

    CVehEntryInfo entryInfo;
    GetEntryInforEntry(TransTime, cpcIccInfo.cardTollInfo, pCurTransInfo, entryInfo);
    pCurTransInfo->SetVehEntryInfo(entryInfo, TransTime);

    pCurTransInfo->SetCPCIccInfo(&cpcIccInfo);
    pCurTransInfo->m_BeginTime = QDateTime::currentDateTime().toMSecsSinceEpoch();

    //门架计费
    QString sExStationHex = "";
    quint8 feeVehClass = pCurTransInfo->VehInfo.VehClass;
    if (CTollGantryMgr::GetTollGantryMgr()->CheckNeedGrantryCalcFee()) {
        if (!CFareCalcUnit::CalcGantryFee(*pCurTransInfo, feeVehClass, sExStationHex,
                                          pCurTransInfo->bHolidayFree, TransTime, sError)) {
            sError = QString("门架计费失败,Error:%1").arg(sError);
            pCurTransInfo->SetOncePaySpEvent(OncePay_Sp_FeeQryFeeFailed);
            return false;
        } else {
            ErrorLog(QString("门架计费成功,fee:%1,payfee:%2,discountFee:%3,feeProSumLocal:%4,"
                             "计费车型:%5")
                     .arg(pCurTransInfo->gantryFeeInfo.cardFee)
                     .arg(pCurTransInfo->gantryFeeInfo.payFee)
                     .arg(pCurTransInfo->gantryFeeInfo.discountFee)
                     .arg(pCurTransInfo->gantryFeeInfo.feeProvSumLocal)
                     .arg(feeVehClass));
        }
    }

    qint32 dwLastMoney, dwConsumeMoney;
    pCurTransInfo->cpcIccInfo.cardTollInfo.dwTotalFee =
            pCurTransInfo->GetLastMoney_Entry(dwLastMoney, dwConsumeMoney);
    FillCPCRoadAndTollCellInfo(pCurTransInfo, pCurTransInfo->cpcIccInfo);
    //    CCPCRoadInfoRaw_New cpcRoadInfoRaw;
    //    CCPCTollCellInfoRaw_New cpcTollCellInfoRaw;
    memcpy(&cpcRoadInfoRaw, pCurTransInfo->cpcIccInfo.cpcpRoadInfoRaw.data(),
           sizeof cpcRoadInfoRaw);
    memcpy(&cpcTollCellInfoRaw, pCurTransInfo->cpcIccInfo.cpcTollCellInfoRaw.data(),
           sizeof cpcTollCellInfoRaw);

    bRlt = pCardReader->WriteCPCRoadAndCellInfo_New(cpcRoadInfoRaw, cpcTollCellInfoRaw, false);
    if (!bRlt) {
        sError = QString("写入路径信息失败");
        return false;
    }

    bRlt = pCardReader->WriteCPCCard(pCardReader->GetCardId(), 0,
                                     &pCurTransInfo->cpcIccInfo.cardTollInfo, false, false);

    CTransInfo *pLastTransInfo = Ptr_ETCCtrl->GetLastTransInfo(DevIndex_Manual);
    // pCurTransInfo->
    pCardReader->GetPsamTermNo(pCurTransInfo->m_TermCode);
    pCurTransInfo->nDetectNum = 1;
    pCurTransInfo->m_endTime = QDateTime::currentDateTime().toMSecsSinceEpoch();

    pCurTransInfo->SaveTo(*pLastTransInfo);
    pLastTransInfo->CompleteTrans(DevIndex_Manual, TransPT_None, NULL, Tr_Successed,
                                  CTransInfo::Ts_WaitToSave);
    //发卡成功不抬杆，只有等待取卡后再抬
    Ptr_ETCCtrl->CompleteTrans(Tr_Successed, DevIndex_Manual, false);
    OnTransFinished_Manual(true, 0, sError, NULL);

    QString sSubKey = TransTime.toString("hhmmss");

    CStdLog::StdLog_CPCBaseInfo(sSubKey, pCurTransInfo->cpcIccInfo.cpcBasicInfo);
    CStdLog::StdLog_CPCStationInfo(sSubKey, pCurTransInfo->cpcIccInfo.cpcRoadInfo);
    CStdLog::StdLog_CPCFeeInfo(sSubKey, pCurTransInfo->cpcIccInfo.cpcTollCellInfo_After);
    //填写计费信息

    TransInfoShared Value;
    Value.nStatus = 0;
    Value.Transtime = TransTime;
    Value.nLaneid = Ptr_Info->GetLaneId();
    Value.nMediatype = MediaType_CPC;
    Value.nVehtype = pCurTransInfo->VehInfo.VehClass;
    CTransInfoShared::GetTransInfoShared()->SendData(sPlate, Value);
    pCurTransInfo->ClearTransInfo();
    return true;
}

bool CLaneState_VehInputEntry::ProcessOnOpenCardEvent(int nReadId, int nCardType, bool &bAlarm,
                                                      QString &sError, bool &bContinueReadCard)
{
    bAlarm = false;
    bContinueReadCard = true;
    QString sName;

    if (CAbstractState::m_bDataSaveFailed) {
        sError = QString("数据存储异常,停止交易,请联系维护人员!");
        DebugLog(sError);
        bAlarm = true;
        return false;
    }

    if (!CheckAndSetOpState(opState_IsWritingCard, sName)) {
        sError = QString("%1,刷卡无效").arg(sName);
        // ShowErrorMessage(sError);
        return false;
    }

    QString sFDMsg;

    sFDMsg.clear();
    // 1判断是否允许发卡
    if (!CheckAllowWriteCPCCard(true, sError, sFDMsg, bAlarm, nReadId == 0)) {
        SetOpState(opState_None);
        return false;
    }
    CTransInfo *pCurTransInfo = Ptr_ETCCtrl->GetCurTransInfo(DevIndex_Manual);
    if (!pCurTransInfo) {
        SetOpState(opState_None);
        return false;
    }

    CCardReader *pCardReader = CCardReader::GetCardReader(nReadId);
    if (!pCardReader) {
        sError = QString("卡机对应读写器[%1]没配置").arg(nReadId);
        SetOpState(opState_None);
        return false;
    }

    bool bRlt = pCardReader->GetAuthResult();
    if (!bRlt) {
        sError = QString("读写器PSAM未授权,无法发卡");
        sFDMsg = QString("读写器异常");
        bAlarm = true;
        SetOpState(opState_None);
        return false;
    }

    //取车辆信息
    CVehInfo vehInfo;
    CVehAxisInfo vehAxisInfo;

    bRlt = GetVehInfoForCPC_New(false, pCurTransInfo, vehInfo, vehAxisInfo, sError, sFDMsg);
    if (!bRlt) {
        bAlarm = true;
        SetOpState(opState_None);
        return false;
    }
    if (vehInfo.GBVehType == UVT_BigTruck) {
        pCurTransInfo->m_sCertNo = GetCertNo();
    }

    RemoteMsgMgr::GetSingleInst()->SendCPCTransMsg_Entry(pCurTransInfo, 2);

    //同车不允许发卡
    QString sPlate = GB2312toUnicode(vehInfo.szVehPlate);
    bool bHaveTrans = QryTransShare(MediaType_CPC, sPlate, sError);
    if (bHaveTrans) {
        DebugLog(QString("组播查询已交易:%1").arg(sError));
        SetOpState(opState_None);
        return false;
    }

    int nErrorCode = 0;

    /*
    if (vehInfo.VehClass >= VC_Car2 && vehInfo.VehClass <= VC_Car4) {
        QDateTime curTime = QDateTime::currentDateTime();
        if (curTime.time().hour() >= 2 && curTime.time().hour() <= 4) {
            sError = QString("客2以上车辆限时通行");
            if (!ShowInformation_Help(
                    QString("客车限时通行"), sError, QString("<确定>键继续放行,<取消>键返回"), true,
                    QString("客车限时通行"), CSpEventMgr::SPEvent_StopPassByTime, true)) {
                nErrorCode = CSpEventMgr::SPEvent_StopPassByTime;
                sFDMsg = QString("客车限时通行");
                bAlarm = true;
                return false;
            }
            DebugLog(QString("客车%1限时通行，允许通行").arg(vehInfo.VehClass));
        }
    }*/

    QDateTime curTime = QDateTime::currentDateTime();
    bool bPermit = Ptr_Info->CheckVehPassPermit(vehInfo.VehClass, curTime);
    if (!bPermit) {
        CVehPassPermitInfo permitInfo;
        if (!CheckVehInPermitList(sPlate, permitInfo)) {
            QString sVehName = GetVehClassName(vehInfo.VehClass);
            DebugLog(QString("%1,%2 限时通行").arg(sVehName).arg(sPlate));
            sError = QString("<%1,%2>夜间禁止通行").arg(sPlate).arg(sVehName);
            QString sHelp = QString("<确定>键继续发卡,<取消>键停止");
            bool bRlt =
                    ShowInformation_Help(QString("夜间禁止通行"), sError, sHelp, true, QString(""),
                                         CSpEventMgr::SPEvent_StopPassByTime, true);
            if (bRlt) {
                FormLogin loginUser;
                COperInfo operInfo;
                if (loginUser.auth(operInfo)) {
                    pCurTransInfo->m_bPassPermit = true;
                    pCurTransInfo->m_operInfo = operInfo;
                    pCurTransInfo->m_AuthTime = curTime;
                    AddToPermitList(sPlate, operInfo);
                } else {
                    return false;
                }

            } else {
                nErrorCode = CSpEventMgr::SPEvent_StopPassByTime;
                return false;
            }
        } else {
            pCurTransInfo->m_bPassPermit = true;
            pCurTransInfo->m_operInfo = permitInfo.operInfo;
            pCurTransInfo->m_AuthTime = permitInfo.occurTime;
        }
    }

    int bInList = CheckVehList(DevIndex_Manual, sPlate, vehInfo.nVehPlateColor, nErrorCode, sError);
    if (bInList > 0) {
        if (!ShowInformation_Help(QString("黑名单车辆"), sError,
                                  QString("<确定>键继续发卡,<取消>键返回"), true,
                                  QString("黑名单车辆"), CSpEventMgr::SpEvent_BlackCar, true)) {
            sFDMsg = QString("黑名单车辆");
            bAlarm = false;
            return false;
        }
        if (3 == bInList) {
            int nRlt = CFuncMenu::ShowMenu_PrePayBList();
            if (0 == nRlt) return false;
            pCurTransInfo->m_nPrepayList = nRlt;
        }
    }

    CTransInfo transInfo;
    bHaveTrans = Ptr_ETCCtrl->CheckVehhasTransByVLP(sPlate, transInfo);

    if (bHaveTrans) {
        if (transInfo.bTransOk()) {
            sError = QString("已发卡车辆,不允许发卡");
            SetOpState(opState_None);
            return false;
        }
    }

    if (vehInfo.VehClass > VC_Truck) {
        bool bCheckRlt =
                CheckVCAndAxis(vehInfo, pCurTransInfo->GetVehAxisNum(), false, sError, sFDMsg);
        if (!bCheckRlt) {
            bAlarm = true;
            SetOpState(opState_None);
            return false;
        }
    }

    // int nCardType = pCardReader->GetCardType();
    if (TYPE_CPC != nCardType) {
        DebugLog(QString("卡类型%1,非CPC卡").arg(nCardType));
        sError = QString("非CPC卡");
        bContinueReadCard = true;
        SetOpState(opState_None);
        return false;
    }

    quint32 cpcCardMac = pCardReader->GetCardId();
    if (!ProcessCPCCardEvent(0, cpcCardMac, pCardReader, sError)) {
        bContinueReadCard = true;
        SetOpState(opState_None);
        return false;
    }
    CCardReader::CancelCardDetection();
    //通知远控平板
    Ptr_RemoteCtrl->ChangeState_EntrySendCard();
    CMessageBox::Information(QString("发卡完成,请按确定键放行"), CMessageBox::Style_Ok);
    Ptr_RemoteCtrl->ChangeState_Input();
    Ptr_ETCCtrl->SetAllowPass(true, false);

    CLaneSoundPlay::PlayHelpSnd(CLaneSoundPlay::PlayEvent_SaveCard);
    SetOpState(opState_None);
    bContinueReadCard = false;
    return true;
}

bool CLaneState_VehInputEntry::CheckAllowWriteCPCCard(bool bCheckETC, QString &sError,
                                                      QString &sFDMsg, bool &bAlarm, bool isReader0)
{
    bAlarm = false;
    CTransInfo *pCurTransInfo = Ptr_ETCCtrl->GetCurTransInfo(DevIndex_Manual);
    if (!pCurTransInfo) {
        if (Ptr_Info && Ptr_Info->GetCardMgrType() == 31 && Ptr_Info->bHaveFrontDev()){
            DebugLog("车道无车，主动增加一辆异常车");
            CTransInfo *pTransInfo = new CTransInfo();
            Ptr_ETCCtrl->AddTransVehToList(1, pTransInfo, true);
        }
        else{
            sError = QString("没检测到未交易车辆,无法发卡");
            return false;
        }
    }

    if (bCheckETC) {
        CTransInfo *pEtcTransInfo = Ptr_ETCCtrl->GetCurTransInfo(DevIndex_Second);
        if (pEtcTransInfo) {
            if (pEtcTransInfo->etcTransType == TransType_EnNormal && pEtcTransInfo->transState >= CTransInfo::Ts_WaitOpResult) {
                //
                sError = QString("天线正在交易,无法发卡");
                return false;
            }
        }
    }

    // isReader0=true 桌面读写器，不检查地感线圈是否有车。 2024.7.25
    // 优化程序时要求加的，人工操作时，不检查是否有车。
    if (Ptr_Info->bCheckExistLoop() && !isReader0) {
        CIODevStatus iostatus;
        CDeviceFactory::GetIOCard()->GetDIPortStatus(DI_LoopExist, iostatus);
        if (!iostatus.bStatus) {
            sError = QString("地感线圈无车,无法发卡");
            sFDMsg = QString("地感无车\n无法发卡");
            bAlarm = true;
            return false;
        }
    }
    if (Ptr_ETCCtrl->bAllowllContinuePass(false)) {
        sError = QString("等待车辆离开, 无法发卡");
        return false;
    }

    /*
    QString sVehPlate = GB2312toUnicode(pCurTransInfo->VehInfo.szVehPlate);
    int nErrorCode = 0;
    int bInList = CheckVehList(DevIndex_Manual, sVehPlate, pCurTransInfo->VehInfo.nVehPlateColor,
    nErrorCode, sError); if (bInList > 0) { return false;
    }*/

    return true;
}

void CLaneState_VehInputEntry::FillCPCRoadAndTollCellInfo(CTransInfo *pTransInfo,
                                                          CCPCIccInfo &cpcIccInfo)
{
    cpcIccInfo.cpcRoadInfo.clear();
    cpcIccInfo.cpcRoadInfo.nProvCnt_After = 1;
    cpcIccInfo.cpcRoadInfo.nProvFlagCnt_After = 1;
    cpcIccInfo.cpcRoadInfo.nProvTollMoney_After = pTransInfo->gantryFeeInfo.payFee;
    cpcIccInfo.cpcRoadInfo.nProvTradeMeter_After = pTransInfo->gantryFeeInfo.feeMileage;
    cpcIccInfo.cpcRoadInfo.sProvEnFlag = pTransInfo->m_curGantryInfo.sGantryHex;
    cpcIccInfo.cpcRoadInfo.nProvEnFlagPassTime = pTransInfo->TransTime.toTime_t();
    cpcIccInfo.cpcRoadInfo.sNewFlag_After = pTransInfo->m_curGantryInfo.sGantryHex;
    cpcIccInfo.cpcRoadInfo.nNewFlagRealMoney_After = pTransInfo->gantryFeeInfo.realFee;
    cpcIccInfo.cpcRoadInfo.nNewFlagTradeMeter_After = pTransInfo->gantryFeeInfo.feeMileage;
    cpcIccInfo.cpcRoadInfo.nNewFlagPassTime_After = cpcIccInfo.cpcRoadInfo.nProvEnFlagPassTime;
    cpcIccInfo.cpcRoadInfo.bPassFlagCnt_After = 1;
    cpcIccInfo.cpcRoadInfo.FlagInfoList_After =
            cpcIccInfo.cpcRoadInfo.FlagInfoList + pTransInfo->m_curGantryInfo.sGantryHex;
    CCPCRoadInfoRaw_New cpcRoadInfoRaw;
    CCardFileConverter::CPCRoadInfo2CPCRoadInfoRaw_New(&cpcIccInfo.cpcRoadInfo, &cpcRoadInfoRaw);
    cpcIccInfo.cpcpRoadInfoRaw.clear();
    cpcIccInfo.cpcpRoadInfoRaw.append((char *)&cpcRoadInfoRaw, sizeof cpcRoadInfoRaw);

    cpcIccInfo.cpcTollCellInfo.clear();
    cpcIccInfo.cpcTollCellInfo.nProvCnt = 1;
    CProvCellInfo cellInfo;
    cellInfo.bProv = 36;
    cellInfo.bProvFlagCnt = 1;
    cellInfo.nProvTollMoney = pTransInfo->gantryFeeInfo.payFee;
    cellInfo.nProvTradeMeter = pTransInfo->gantryFeeInfo.feeMileage;
    cellInfo.sEnFlag = pTransInfo->m_curGantryInfo.sGantryHex;
    cellInfo.nPassTime = pTransInfo->TransTime.toTime_t();
    cellInfo.sNewFlag = pTransInfo->m_curGantryInfo.sGantryHex;
    cellInfo.nNewPassTime = cellInfo.nPassTime;
    cellInfo.bSign = 0;

    cpcIccInfo.cpcTollCellInfo.ProCellInfoList.push_back(cellInfo);
    CCPCTollCellInfoRaw_New cpcTollCellInfoRaw;
    CCardFileConverter::CPCTollCellInfo2CPCTollCellInfoRaw_New(&cpcIccInfo.cpcTollCellInfo,
                                                               &cpcTollCellInfoRaw);
    cpcIccInfo.cpcTollCellInfoRaw.clear();
    cpcIccInfo.cpcTollCellInfoRaw.append((char *)&cpcTollCellInfoRaw, sizeof cpcTollCellInfoRaw);
    return;
}

bool CLaneState_VehInputEntry::ProcessVehReverse()
{
    CTransInfo *pLastTransInfo = Ptr_ETCCtrl->RemoveTransInfoFromQue(true, false);
    if (!pLastTransInfo) {
        ShowErrorMessage("车道无车，无需倒车处理");
        return false;
    }
    if (!pLastTransInfo->bTransOk()) {
        ShowErrorMessage("无刷卡车辆,无需倒车处理");
        return false;
    }
    if (pLastTransInfo->mediaType == MediaType_Paper) {
        ShowErrorMessage("纸券车辆,无需倒车处理");
        return false;
    }

    DebugLog("入口倒车处理");
    FormReverse frmReverse;
    QString sError;

    {
        CTransInfo *pLastTransInfo = Ptr_ETCCtrl->RemoveTransInfoFromQue(true, true);
        pLastTransInfo->m_bReprint = true;
        Ptr_ETCCtrl->EmitNotifyTransCompleteSign(pLastTransInfo, false);
        //更新已交易队列内车辆信息

        TransInfoShared value;
        value.nLaneid = Ptr_Info->GetLaneId();
        value.nMediatype = pLastTransInfo->mediaType;
        value.nStatus = 3;
        value.nVehtype = pLastTransInfo->VehInfo.VehClass;
        QString sPlate = GB2312toUnicode(pLastTransInfo->VehInfo.szVehPlate);
        CTransInfoShared::GetTransInfoShared()->SendData(sPlate, value);

        //Ptr_ETCCtrl->bAllowllContinuePass(true);
        //Ptr_ETCCtrl->SetRefusePass();//确保落杆

        if (pLastTransInfo) {
            Ptr_ETCCtrl->RemoveHasTransInfo(pLastTransInfo);
            delete pLastTransInfo;
        }
    }

    SetOpState(opState_None);
    return true;
}
bool CLaneState_VehInputEntry::ProcessKeyReprint()
{
    CTransInfo *pLastTransInfo = Ptr_ETCCtrl->RemoveTransInfoFromQue(true, false);
    if (!pLastTransInfo) {
        ShowErrorMessage("无刷卡车辆,废票键无效");
        return false;
    }
    if (!pLastTransInfo->bTransOk()) {
        ShowErrorMessage("无刷卡车辆,废票键无效");
        return false;
    }

    if (pLastTransInfo->mediaType == MediaType_Paper) {
        bool bRlt = ShowInformation_Help(QString("重打"), QString("重新打印纸券"),
                                         QString("<确定>重新打印纸券,<取消>键返回"), false,
                                         QString(""), CSpEventMgr::SpEvent_Other, true);
        if (bRlt) {
            QString sError;
            if (!PrintPaperInfo(pLastTransInfo->m_sPaperId, pLastTransInfo->m_sPaperKey,
                                pLastTransInfo, sError)) {
                ShowErrorMessage(QString("%1,打印失败").arg(sError));
            }
        }
        return true;
    }

    QString sOpStateName;
    if (!CheckAndSetOpState(opState_Reverse, sOpStateName)) {
        ShowErrorMessage(QString("%1,按废票键无效").arg(sOpStateName));
        return false;
    }
    DebugLog("入口废票处理");
    FormReverse frmReverse;
    QString sError;
    bool bRlt = frmReverse.DoReverse(pLastTransInfo, sError);
    if (!bRlt) {
        if (sError.length() > 0) ShowErrorMessage(sError);
    } else {
        CTransInfo *pLastTransInfo = Ptr_ETCCtrl->RemoveTransInfoFromQue(true, true);
        pLastTransInfo->m_bReprint = true;
        Ptr_ETCCtrl->EmitNotifyTransCompleteSign(pLastTransInfo, false);

        //更新已交易队列内车辆信息

        TransInfoShared value;
        value.nLaneid = Ptr_Info->GetLaneId();
        value.nMediatype = pLastTransInfo->mediaType;
        value.nStatus = 3;
        value.nVehtype = pLastTransInfo->VehInfo.VehClass;
        QString sPlate = GB2312toUnicode(pLastTransInfo->VehInfo.szVehPlate);
        CTransInfoShared::GetTransInfoShared()->SendData(sPlate, value);

        Ptr_ETCCtrl->bAllowllContinuePass(true);

        if (pLastTransInfo) {
            Ptr_ETCCtrl->RemoveHasTransInfo(pLastTransInfo);
            delete pLastTransInfo;
        }
    }
    SetOpState(opState_None);
    return bRlt;
}

bool CLaneState_VehInputEntry::HasVehInLane()
{
    CIODevStatus ioStatus;
    bool bRlt = CDeviceFactory::GetIOCard()->GetDIPortStatus(DI_LoopExist, ioStatus);
    if (bRlt) {
        if (ioStatus.bStatus) return true;
    }
    return false;
}

bool CLaneState_VehInputEntry::DoHolidyFreeEntry()
{
    QString sFDMsg, sError;
    sFDMsg.clear();
    sError.clear();
    bool bAlarm = false;
    // 1判断是否允许发卡
    if (!CheckAllowWriteCPCCard(false, sError, sFDMsg, bAlarm, false)) {
        ShowErrorMessage(sError);
        return false;
    }
    CTransInfo *pCurTransInfo = Ptr_ETCCtrl->GetCurTransInfo(DevIndex_Manual);
    if (!pCurTransInfo) {
        DebugLog(QString("取交易车辆失败"));
        return false;
    }

    //取车辆信息
    CVehInfo vehInfo;
    CVehAxisInfo vehAxisInfo;

    bool bRlt = GetVehInfoForCPC_New(false, pCurTransInfo, vehInfo, vehAxisInfo, sError, sFDMsg);
    if (!bRlt) {
        ShowErrorMessage(sError);
        return false;
    }

    /*改功能用于节假日前放行车辆，因此不对节假日参数判断
    CHolidayFreeTable *pTable = (CHolidayFreeTable *)CParamFileMgr::GetParamFile(cfHolidayFree);
    bool bHolidayFree = pTable->IsHolidayFree(vehInfo.VehClass, sError);
    if (!bHolidayFree) {
        sError = QString("非节假日免费车辆");
        ShowErrorMessage(sError);
        return false;
    }*/

    QString sLastGantryHex;
    bool bOpenGantry = false;
    CVirGantryInfo curGantryInfo;

    bool bQryResult = CTollGantryMgr::GetTollGantryMgr()->GetCurGantryInfo(
                sLastGantryHex, curGantryInfo, bOpenGantry);
    if (!bQryResult) {
        sError = QString("入口节假日免费,门架%1信息查询失败").arg(sLastGantryHex);
        return false;
    }

    pCurTransInfo->SetVirGantryInfo(curGantryInfo, bOpenGantry, QString(""));
    QDateTime TransTime = QDateTime::currentDateTime();
    CCardTollInfo cardTollInfo;
    FillCardTollInfo(cardTollInfo, TransTime, pCurTransInfo);
    DebugLog(
                QString(
                    "入口节假日,sLastGantryHex:%1, stationName:%2 nStationId:%3,当前站信息:%4,交易时间:%5 ")
                .arg(sLastGantryHex)
                .arg(pCurTransInfo->m_curGantryInfo.sStationName)
                .arg(pCurTransInfo->m_curGantryInfo.nStationId)
                .arg(Ptr_Info->GetStationID())
                .arg(TransTime.toString("yyyy-MM-dd hh:mm:ss")));

    CVehEntryInfo entryInfo;
    GetEntryInforEntry(TransTime, cardTollInfo, pCurTransInfo, entryInfo);
    pCurTransInfo->SetVehEntryInfo(entryInfo, TransTime);

    pCurTransInfo->m_BeginTime = QDateTime::currentDateTime().toMSecsSinceEpoch();
    //  pCurTransInfo->bHolidayFree = 1;
    // pCurTransInfo->SetOncePaySpEvent(OncePay_Sp_HolidayFree);

    CTransInfo *pLastTransInfo = Ptr_ETCCtrl->GetLastTransInfo(DevIndex_Manual);
    pCurTransInfo->nDetectNum = 1;
    pCurTransInfo->m_endTime = QDateTime::currentDateTime().toMSecsSinceEpoch();

    pCurTransInfo->SaveTo(*pLastTransInfo);
    pLastTransInfo->CompleteTrans(DevIndex_Manual, TransPT_None, NULL, Tr_Successed,
                                  CTransInfo::Ts_WaitToSave);
    //发卡成功不抬杆，只有等待取卡后再抬
    Ptr_ETCCtrl->CompleteTrans(Tr_Successed, DevIndex_Manual, false);
    OnTransFinished_Manual(true, 0, sError, NULL);

    Ptr_RemoteCtrl->ChangeState_Input();
    Ptr_ETCCtrl->SetAllowPass(true, false);

    //填写计费信息
    pCurTransInfo->ClearTransInfo();

    return true;
}

void CLaneState_VehInputEntry::OnCardMgrTimer()
{
    //StopCardMgrTimer();
    // 定时器触发的处理逻辑
    if (Ptr_Info && Ptr_Info->GetCardMgrType() == 31) {
        if (m_nTimerCardMgrInterval2Flag) {
            m_nTimerCardMgrInterval2Flag = false;
            m_timerCardMgr.stop();
            DebugLog("天线进入B4帧交易，入口车道卡机定时器延时触发...");
            m_timerCardMgr.start(m_nTimerCardMgrInterval2);
            return;
        }
        // 这里添加对福赛特-中瑞卡机(m_nCardMgrType=31)的定时处理逻辑
        DebugLog("入口车道卡机定时器触发...");
        // 执行定时器事件处理

        GetMainDlg()->ShowPromptMsg("准备发卡");
        CDeviceFactory::FareDisplay_ShowHelpMsg(DevIndex_Second,
                                                QString("领取通行卡\n"));
        //                CLaneSoundPlay::PlayHelpSnd(CLaneSoundPlay::PlayEvent_WelCome);
        CCardMgr *pCardMgr = CDeviceFactory::GetCardMachine();
        if (pCardMgr) {
            //            VcrResult vcrResult;
            bool bMoved = false;
            //            bool bRlt = CDeviceFactory::GetVCRDev()->GetFistVcrResult(vcrResult);
            //            if (bRlt) {
            //                if (vcrResult.vehclass > 0) {

            bMoved = true;

            //                }
            //            }
            //#ifdef QT_DEBUG
            //            bMoved = true;
            //#endif
            if (bMoved) pCardMgr->MoveCardHead(bMoved, true);
            if (Ptr_Info->bRemoteControl()) {
                DebugLog("卡机外屏：提示按键取卡， HT_En_WaitSendCard");
                CDeviceFactory::GetAutoExTollScreen()->ShowHelp(
                            AutoExTollScreen::HT_En_WaitSendCard, "");
            } else {
                //非远控版本，播放卡机提示音
                SleeperThread::msleep(100);
                pCardMgr->PlaySnd(CCardMgr::SndId_HintPressButton);
            }
        }
    }
}
// 响应停止卡机定时器的槽函数
void CLaneState_VehInputEntry::OnStopCardMgrTimer()
{
    // 调用公共函数停止定时器
    StopCardMgrTimer();
}


bool CLaneState_VehInputEntry::DoQuanFanCheEntry()
{
    if (!CMessageBox::Information_Help(QString("劝返车"), QString("劝返车处理,请确认?"),
                                       QString("确定键继续,取消键返回"))) {
        return false;
    }

    //取车辆信息
    CVehInfo vehInfo;
    CVehAxisInfo vehAxisInfo;

    CTransInfo curTransInfo;
    CTransInfo *pCurTransInfo = &curTransInfo;

    GetCurVehInfo(vehInfo, false);
    QString sError;

    CVehClass auVehClass = VC_None;
    if (vehInfo.IsVLPEmpty()) {
        sError = QString("无车牌信息");
        ShowErrorMessage(sError);
        return false;
    } else {
        QString sVehPlate = GB2312toUnicode(vehInfo.szVehPlate);
        auVehClass = CDeviceFactory::GetVCRDev()->GetVehClass(vehInfo.nVehPlateColor, sVehPlate);
        DebugLog(QString("劝返车,取出车型识别结果:%1,%2").arg(sVehPlate).arg((int)auVehClass));
    }

    vehInfo.AutoVehClass = auVehClass;
    if (vehInfo.VehClass == VC_None) {
        vehInfo.VehClass = auVehClass;
        vehInfo.PVehClass = auVehClass;
    }

    if (vehInfo.VehClass == VC_None) {
        sError = QString("无车型信息");
        ShowErrorMessage(sError);
        return false;
    }

    quint32 dwTotalWeight = 0, dwLimitWeight = 0;
    qint32 nOverRate = 0;
    bool bRlt =
            GetCurVehWeightInfo(vehInfo.VehClass, vehAxisInfo, dwTotalWeight, dwLimitWeight, nOverRate);

    CVehAxisInfo *pVehAxisInfo = NULL;
    if (bRlt) pVehAxisInfo = &vehAxisInfo;
    pCurTransInfo->SetVehInfo(&vehInfo, pVehAxisInfo);
    if (m_sAutoVehPlate.length() > 0) {
        if (Ptr_ETCCtrl->GetETCAutoRegInfoByPlate(m_sAutoVehPlate, pCurTransInfo->AutoRegInfo)) {
            DebugLog(QString("劝返车,获取到当前车辆自动识别信息:Plate:%1,id:%2")
                     .arg(pCurTransInfo->AutoRegInfo.sAutoVehPlate)
                     .arg(pCurTransInfo->AutoRegInfo.id));
        } else {
            DebugLog(QString("劝返车,没找到%1,对应识别信息").arg(m_sAutoVehPlate));
        }
    }
    pCurTransInfo->m_nOverRate = nOverRate;

    QString sLastGantryHex;
    bool bOpenGantry = false;
    CVirGantryInfo curGantryInfo;

    bool bQryResult = CTollGantryMgr::GetTollGantryMgr()->GetCurGantryInfo(
                sLastGantryHex, curGantryInfo, bOpenGantry);
    if (!bQryResult) {
        sError = QString("劝返车,门架%1信息查询失败").arg(sLastGantryHex);
        return false;
    }

    pCurTransInfo->SetVirGantryInfo(curGantryInfo, bOpenGantry, QString(""));
    QDateTime TransTime = QDateTime::currentDateTime();
    CCardTollInfo cardTollInfo;
    FillCardTollInfo(cardTollInfo, TransTime, pCurTransInfo);
    DebugLog(
                QString(
                    "入口劝返车,sLastGantryHex:%1, stationName:%2 nStationId:%3,当前站信息:%4,交易时间:%5 ")
                .arg(sLastGantryHex)
                .arg(pCurTransInfo->m_curGantryInfo.sStationName)
                .arg(pCurTransInfo->m_curGantryInfo.nStationId)
                .arg(Ptr_Info->GetStationID())
                .arg(TransTime.toString("yyyy-MM-dd hh:mm:ss")));

    CVehEntryInfo entryInfo;
    GetEntryInforEntry(TransTime, cardTollInfo, pCurTransInfo, entryInfo);
    pCurTransInfo->SetVehEntryInfo(entryInfo, TransTime);

    pCurTransInfo->m_BeginTime = QDateTime::currentDateTime().toMSecsSinceEpoch();
    //  pCurTransInfo->bHolidayFree = 1;
    // pCurTransInfo->SetOncePaySpEvent(OncePay_Sp_HolidayFree);

    // CTransInfo *pLastTransInfo = Ptr_ETCCtrl->GetLastTransInfo(DevIndex_Manual);
    pCurTransInfo->nDetectNum = 1;
    pCurTransInfo->m_endTime = QDateTime::currentDateTime().toMSecsSinceEpoch();

    // pCurTransInfo->SaveTo(*pLastTransInfo);
    pCurTransInfo->CompleteTrans(DevIndex_Manual, TransPT_None, NULL, Tr_Successed,
                                 CTransInfo::Ts_WaitToSave);
    //发卡成功不抬杆，只有等待取卡后再抬
    // Ptr_ETCCtrl->CompleteTrans(Tr_Successed, DevIndex_Manual, false);
    OnTransFinished_Manual(true, 0, sError, NULL);

    Ptr_ETCCtrl->saveQuanFanChe_En(pCurTransInfo);
    //填写计费信息
    pCurTransInfo->ClearTransInfo();

    QString sLog;
    QString sPlate = GB2312toUnicode(vehInfo.szVehPlate);
    sLog = QString("劝返车:%1").arg(QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss"));
    GetMainDlg()->ShowLog(sLog);
    sLog = QString("劝返车:%1,%2").arg(GetVehClassName((CVehClass)vehInfo.VehClass)).arg(sPlate);

    GetMainDlg()->ShowLog(sLog);
    sLog = QString("总重:%1,限重:%2")
            .arg(pCurTransInfo->m_dwToTalWeight)
            .arg(pCurTransInfo->m_dwWeightLimit);
    GetMainDlg()->ShowLog(sLog);
    VehWeightInfo::GetVehWeightInfo()->RemoveAll();  // RemoveFirst(&vehAxisInfo);
    DebugLog(QString("劝返车,删除计重信息,总重: %1，限载:%2,超限：%3")
             .arg(pCurTransInfo->m_dwToTalWeight)
             .arg(pCurTransInfo->m_dwWeightLimit)
             .arg(pCurTransInfo->m_nOverRate));
    return true;
}
