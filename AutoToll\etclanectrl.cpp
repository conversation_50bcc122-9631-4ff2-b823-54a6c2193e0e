#include "etclanectrl.h"

#include "IOCard.h"
#include "WebService/qxtjson.h"
#include "ccrediteblist.h"
#include "cgbcardblistinc.h"
#include "cgcardblacktable.h"
#include "cgshortpath.h"
#include "coldsysproc.h"
#include "cryptographiccommon.h"
#include "devicefactory.h"
#include "dlgmain.h"
#include "gbmsgtype.h"
#include "httpmsgmanager.h"
#include "jxlanetype.h"
#include "laneinfo.h"
#include "lanestate_vehinputentry.h"
#include "lanestate_vehinputexit.h"
#include "laneutilse.h"
#include "paramfilemgr.h"
#include "remotemsgmgr.h"
#include "stdlog.h"
#include "tollgantrymgr.h"
#include "vehplatefunc.h"
#include "vehweightinfo.h"
#include "remotemsgmgr.h"
#include "remotecontrolmgr.h"
#include "vehtypelibmgr.h"
#include "vipumgr.h"
#include "vdmlibSingleton.h"
#include "lanetype.h"
#include "vehweightinfo.h"

static quint8 g_RruBatchNo = 0;
CETCLaneCtrl *CETCLaneCtrl::m_pETCLaneCtr = NULL;
CETCLaneCtrl::CETCLaneCtrl()
{
    m_pDataMgr = new CLocalDataMgr(QString("lanedb"), QString("savedb"), QString("senddb"), NULL);
    m_pSecureVerify = NULL;
    memset(m_DevInfo, '0', sizeof m_DevInfo);
    m_sLastLoopStatus = QString("1020");
    m_pStayOutTimer = new QTimer(NULL);
    m_pDownBarTimer = new QTimer(NULL);
    QObject::connect(m_pStayOutTimer, SIGNAL(timeout()), this, SLOT(OnStayOutTimer()));
    QObject::connect(m_pDownBarTimer, SIGNAL(timeout()), this, SLOT(OnDownBarTimer()));
    connect(this, SIGNAL(NotifySetAllowPass(bool, bool)), this, SLOT(OnSetAllowPass(bool, bool)));
    connect(this, SIGNAL(NotifySetRefusePass()), this, SLOT(OnSetRefusePass()));
    m_OnDetectTime = 0;
    m_OnFrontTime = 0;
    m_bBarState = false;

    m_sAllLoopStatus = QString("0|0|0|0|0|0");
    m_bVehLeadOut = false;

    for (int i = 0; i < 2; ++i) {
        m_ETCAutoRegInfos[i].ClearResult();
    }
    m_bFrontRsuWork = true;
    m_bLongVeh = false;
    m_lastVehInfo.ClearResult();

    QObject::connect(&m_vehLeaveTimer, SIGNAL(timeout()), this, SLOT(OnVehLeaveTimer()));
    QObject::connect(&m_vehDetectTimer, SIGNAL(timeout()), this, SLOT(OnVehDetectTimer()));
}

CETCLaneCtrl::~CETCLaneCtrl()
{
    if (m_pDataMgr) {
        // m_pDataMgr->ReleaseAll();
        delete m_pDataMgr;
        m_pDataMgr = NULL;
    }

    QObject::disconnect(m_pStayOutTimer, SIGNAL(timeout()), this, SLOT(OnStayOutTimer()));
    QObject::disconnect(m_pDownBarTimer, SIGNAL(timeout()), this, SLOT(OnDownBarTimer()));
    QObject::disconnect(&m_vehLeaveTimer, SIGNAL(timeout()), this, SLOT(OnVehLeaveTimer()));
    QObject::disconnect(&m_vehDetectTimer, SIGNAL(timeout()), this, SLOT(OnVehDetectTimer()));
    if (m_pDownBarTimer) {
        if (m_pDownBarTimer->isActive()) {
            m_pDownBarTimer->stop();
        }
        delete m_pDownBarTimer;
    }
    if (m_pStayOutTimer) {
        if (m_pDownBarTimer->isActive()) m_pDownBarTimer->stop();
        delete m_pStayOutTimer;
    }
}

CLocalDataMgr *CETCLaneCtrl::GetDataMgr() { return m_pDataMgr; }

bool CETCLaneCtrl::InitETCLaneCtrl(int nStationId, int nLaneId, qint32 nLaneType)
{
    m_nStationId = nStationId;
    m_nLaneId = nLaneId;
    m_bLaneType = nLaneType;
    m_ShiftMgr.InitShiftMgr(nStationId, nLaneId, NULL);
    return true;
}

bool CETCLaneCtrl::InitLocalDB(const QString &sDBPath, const QString &sBakDBPath,
                               bool bRemoveBakFile)
{
    bool bRet = m_pDataMgr->InitLocalDB(m_nStationId, m_nLaneId, sDBPath, sBakDBPath);
    m_pDataMgr->SetbRemoveBakFile(bRemoveBakFile);
    if (bRet) m_pDataMgr->StartSendDataThread();
    return bRet;
}

bool CETCLaneCtrl::GetAndResendTmpFile()
{
    QFileInfoList tmpList;
    bool bRlt = m_pDataMgr->GetTmpFileList(Ptr_Info->GetGBLaneId(), tmpList);

    QDateTime now = QDateTime::currentDateTime();
    foreach (QFileInfo fileInfo, tmpList) {
        QDateTime createTime = fileInfo.created();
        if (createTime.daysTo(now) <= Ptr_Info->GetTmpFileTime()) {
            QString sError;
            bool bLoadRlt =
                    m_pDataMgr->ResaveTmpData(Ptr_Info->GetGBLaneId(), fileInfo.filePath(), sError);
            if (!bLoadRlt) {
                DebugLog(QString("重新保存临时文件%1,失败").arg(fileInfo.filePath()));
            } else {
                DebugLog(QString("重新保存临时文件%1,成功").arg(fileInfo.filePath()));
            }
        } else {
            DebugLog(QString("存在过期临时文件%1").arg(fileInfo.filePath()));
        }
    }
    return bRlt;
}

void CETCLaneCtrl::Login(const COperInfo &OperInfo, const QDateTime &loginTime)
{
    // 班次初始化

    m_ShiftMgr.Login(OperInfo.dwOper, OperInfo.sOperName, OperInfo.wGroupID, loginTime,
                     m_InvoiceInfo.GetCurrentStartNo());

    SetLaneStatus(lsNormalWorking);
    for (int i = 0; i < MAX_FAREDISPLAYER_NUM; ++i) {
        CFareDisplayer_GB *pFD = CDeviceFactory::GetETCFareDisPlayer(i);
        if (!pFD) continue;
        pFD->ShowWelcomeInfo(true);
    }

    if (Ptr_Info->IsEntryLane()) {
        saveLoginWaste_En(loginTime, 1);
    } else {
        saveLoginWaste_Ex(loginTime, 1);
    }

    CShiftSumInfo ShiftSumInfo;
    m_ShiftMgr.GetShiftSumInfo(ShiftSumInfo);
    QString sLDate = QString::fromLocal8Bit(ShiftSumInfo.szLDate).mid(4, 4);
    QString tmpshift = GB2312toUnicode(ShiftSumInfo.szShiftName);
    QString sShiftName = QString("%1(%2)").arg(tmpshift).arg(sLDate);

    SaveWorkSumData(ShiftSumInfo);
    GetMainDlg()->ShowShiftInfo(loginTime.date(), sShiftName);
    CStdLog::StdLogLoginInfo(true, OperInfo.sOperName, sShiftName);
    
    // 上班成功后，设置工班状态控制端口为上班状态（发送1信号）
    CIOCard *pIOCard = CDeviceFactory::GetIOCard();
    if (pIOCard != NULL) {
        pIOCard->SetWorkShiftStatus(true);
    }
    
    emit showOnDutyTime(loginTime);
}

void CETCLaneCtrl::UnLogin(bool bIsReSend)
{
    if ((m_LaneStatus == lsUnlogin) && !bIsReSend) {
        return;
    }
    CShiftSumInfo oldShiftSumInfo;
    m_ShiftMgr.GetShiftSumInfo(oldShiftSumInfo);

    QDateTime logoutTime;

    m_ShiftMgr.Logout(logoutTime, m_InvoiceInfo.GetCurrentEndNo());

    CStdLog::StdLogLoginInfo(false, m_ShiftMgr.m_sOperName, QString());
    //发送下班报文
    if (Ptr_Info->IsEntryLane())
        saveLoginWaste_En(logoutTime, 2);
    else
        saveLoginWaste_Ex(logoutTime, 2);
    //   m_ShiftMgr.m_dwOperId = 0;
    SetLaneStatus(lsUnlogin);

    CShiftSumInfo ShiftSumInfo;
    m_ShiftMgr.GetShiftSumInfo(ShiftSumInfo);
    //保存工班合计数信息
    if (!SaveWorkSumData(ShiftSumInfo)) {
        ErrorLog("下班，小班汇总更新失败");
    }
    CShiftSumInfo bigShiftSumInfo;
    m_ShiftMgr.GetBigShiftSumInfo(bigShiftSumInfo);
    if (!SaveBigShiftSumData(bigShiftSumInfo, 0)) {
        ErrorLog("下班班次汇总更新失败");
    }

    //发送工班合计数报文
    if (Ptr_Info->IsEntryLane()) {
        SendWorkSum_En(ShiftSumInfo);
    } else {
        SendWorkSum_Ex(ShiftSumInfo);
    }
    
    // 下班成功后，设置工班状态控制端口为下班状态（发送0信号）
    CIOCard *pIOCard = CDeviceFactory::GetIOCard();
    if (pIOCard != NULL) {
        pIOCard->SetWorkShiftStatus(false);
    }

    for (int i = 0; i < MAX_FAREDISPLAYER_NUM; ++i) {
        CFareDisplayer_GB *pFD = CDeviceFactory::GetETCFareDisPlayer(i);
        if (!pFD) continue;
        pFD->ShowWelcomeInfo(false);
    }

    //下班雨棚灯改成红叉
    CDeviceFactory::GetIOCard()->SetCanopyLight(false);
}

void CETCLaneCtrl::AutoLogin()
{
    QDateTime curTime = QDateTime::currentDateTime();
    COperInfo OperInfo;

    if (Ptr_Info->IsEntryLane()) {
        OperInfo.dwOper = 1000003;
    } else
        OperInfo.dwOper = 1000004;  // Ptr_Info->GetStationID()*1000+Ptr_Info->GetLaneId()*10+1;
    OperInfo.sOperName = QString("ETC操作员");
    OperInfo.wGroupID = 0;
    Login(OperInfo, curTime);
    CDeviceFactory::GetIoCard()->SetCanopyLight(true);
}

bool CETCLaneCtrl::SaveShiftSumMsg(const CShiftSumInfo &OldShiftSumInfo,
                                   const CShiftSumInfo &NewShiftSumInfo)
{
    bool bBigShift = false;
    do {
        char szOldLDate[15] = {0};
        char szNewLDate[15] = {0};
        memcpy(szOldLDate, OldShiftSumInfo.szLDate, sizeof(OldShiftSumInfo.szLDate) - 1);
        memcpy(szNewLDate, NewShiftSumInfo.szLDate, sizeof(NewShiftSumInfo.szLDate) - 1);
        QByteArray btOldLDate = QByteArray::fromRawData(szOldLDate, sizeof(szOldLDate));
        QByteArray btNewLDate = QByteArray::fromRawData(szNewLDate, sizeof(szNewLDate));

        //不是同一工班日
        if (btOldLDate != btNewLDate) {
            bBigShift = true;
            break;
        }

        //工班号不一样
        if (OldShiftSumInfo.wShiftID != NewShiftSumInfo.wShiftID) {
            bBigShift = true;
            break;
        }

    } while (0);

    GetMainDlg()->ShowLog(QString("发送工班汇总报文"));

    // DebugLog(QString("工班记录信息记录数：%1，当前小工班记录信息记录数：%2")
    //         .arg(OldShiftSumInfo.Record.nDealCnt).arg(OldShiftSumInfo.SmallRecord.nDealCnt));
    return true;
}

bool CETCLaneCtrl::SaveLastTransInfo(int nInductCnt, CTransInfo *pTransInfo, bool bGetCapInfo,
                                     bool bSaveFailed, bool bClearCap, bool bSendGantry)
{
    //如果已经保存，就不再保存
    if (!pTransInfo) {
        DebugLog(QString("保存车辆流水，交易对象为NULL"));
        return false;
    }
    if (!pTransInfo->bWaitToSave()) return true;

    pTransInfo->SetDetectCnt(nInductCnt);
    if (bGetCapInfo) {
        CAutoRegInfo autoRegInfo;
        int nDevIndex = pTransInfo->m_nRsuIndex;  // DevIndex_First;
        /*
        if (Ptr_Info->bHaveBackDev()) {
            nDevIndex = DevIndex_Second;
        }*/
        bool bCheckTime = false;  // nDevIndex == DevIndex_First;
        if (pTransInfo->AutoRegInfo.id.isEmpty()) {
            bool bHaveAutoRegInfo =
                    GetETCAutoRegInfo(DevIndex_Second, autoRegInfo, bClearCap, bCheckTime);
            if (!bHaveAutoRegInfo) {
                DebugLog(QString("保存流水,取[%1]号车牌识别结果为空").arg(nDevIndex));
            } else {
                pTransInfo->AutoRegInfo = autoRegInfo;
                DebugLog(QString("保存流水,车牌识别Id:%1,车牌:%2,图片文件:%3")
                         .arg(pTransInfo->AutoRegInfo.id)
                         .arg(pTransInfo->AutoRegInfo.sAutoVehPlate)
                         .arg(pTransInfo->AutoRegInfo.sBigFileName));
            }
        } else {
            DebugLog(QString("保存流水,已记录车牌识别结果:%1,Id:%2")
                     .arg(pTransInfo->AutoRegInfo.sAutoVehPlate)
                     .arg(pTransInfo->AutoRegInfo.id));
        }
    }
    //保存报文
    bool bSaved = false;
    bSaved = pTransInfo->bTransOk() && pTransInfo->bWaitToSave();
    if (bSaveFailed) {
        if (pTransInfo->bTransFailed() && pTransInfo->bWaitToSave()) {
            bSaved = true;
        }
    }

    if (bSaved) {
        bool bRlt = false;
        if (Ptr_Info->IsEntryLane()) {
            bRlt = this->saveTransWaste_EN(pTransInfo);
            if (pTransInfo->bTransOk()) {
                if (pTransInfo->m_bOpenGantry && pTransInfo->m_sVLPId.isEmpty()) {
                    if (pTransInfo->m_sVehicleSignId.length() > 0) {
                        QString sPlate = GB2312toUnicode(pTransInfo->VehInfo.szVehPlate);
                        GantryByPlate::GetSTGantryByPlate()->SetVehPlateId(
                                    pTransInfo->VehInfo.nVehPlateColor, sPlate,
                                    pTransInfo->m_sVehicleSignId);
                    }
                }
            }
        } else {
            /*fortac*/
            if (pTransInfo->m_bForTac)
                this->saveTransWaste_Tac(pTransInfo, 1);
            else {
                bRlt = this->saveTransWaste_EX(pTransInfo);
                if (pTransInfo->bTransOk() && Ptr_Info->CheckReplaceWriteCard()) {
                    COpenGantryInfo gantryInfo;
                    gantryInfo.iColor = pTransInfo->VehInfo.nVehPlateColor;
                    gantryInfo.sVehPlate = GB2312toUnicode(pTransInfo->VehInfo.szVehPlate);
                    gantryInfo.sGantryHex = Ptr_Info->GetReplaceGantryHex();
                    gantryInfo.sGantryID = Ptr_Info->GetReplaceGantryID();
                    gantryInfo.uEntryTime = pTransInfo->TransTime.toTime_t();
                    gantryInfo.sVlpId = pTransInfo->AutoRegInfo.id;
                    GantryByPlate::GetSTGantryByPlate()->SetPassPlateInfo(gantryInfo);
                }
            }
        }
        if (bSendGantry) {
            QString sGantryId = QString::fromLocal8Bit(pTransInfo->tradInfo.curTollGantry);
            if (sGantryId.length() > 0) {
                SaveDoorFrameWaste_New(pTransInfo);
            }
        }
        pTransInfo->SetSaveResult(true);
        {
            CAutoRegInfo lastVehInfo;
            lastVehInfo.sAutoVehPlate = GB2312toUnicode(pTransInfo->VehInfo.szVehPlate);
            lastVehInfo.nAutoVLPColor = pTransInfo->VehInfo.nVehPlateColor;
            lastVehInfo.AutoVehClass = pTransInfo->VehInfo.VehClass;
            lastVehInfo.AuRegTime = QDateTime::currentDateTime();
            QMutexLocker locker(&m_lastVehMt);
            m_lastVehInfo = lastVehInfo;
        }
        return bRlt;
    }
    return true;
}

//通过OBUid 判断当前车辆是否已经交易过。bTransOk - true 判断是否已经交易成功。判断是否交易过。
bool CETCLaneCtrl::CheckVehhasTransByOBUId(const quint32 dwOBUId, int nSeconds,
                                           CTransInfo &transInfo)
{
    if (!Ptr_Info->bCheckSameVeh()) return false;
    QMutexLocker locker(&m_lockHaveTransVehMt);
    if (m_HaveTransVeh.isEmpty()) return false;

    time_t curTime = QDateTime::currentDateTime().toTime_t();
    QList<CTransInfo>::iterator it = m_HaveTransVeh.begin();
    for (; it != m_HaveTransVeh.end(); ++it) {
        if (it->dwOBUID == dwOBUId) {
            if (1) {
                int nSubSeconds = qAbs(curTime - it->m_InQueTime);
                if (nSubSeconds < nSeconds) {
                    DebugLog(QString("车辆重复交易,时间间隔%1秒").arg(nSubSeconds));
                    transInfo = *it;
                    return true;
                }
            }
        }
    }
    return false;
}

bool CETCLaneCtrl::CheckVehhasTransByVLP(const QString &sVLP, CTransInfo &transInfo)
{
    QMutexLocker locker(&m_lockHaveTransVehMt);
    if (m_HaveTransVeh.isEmpty()) return false;

    time_t curTime = QDateTime::currentDateTime().toTime_t();
    QList<CTransInfo>::iterator it = m_HaveTransVeh.begin();
    for (; it != m_HaveTransVeh.end(); ++it) {
        if (!it->bTransOk()) continue;
        QString sTmpVLP = GB2312toUnicode(it->VehInfo.szVehPlate);
        if (sTmpVLP == sVLP) {
            int nSubSeconds = qAbs(curTime - it->m_InQueTime);
            if (nSubSeconds < Ptr_Info->GetMaxInterval()) {
                DebugLog(QString("车辆已交易,上次交易时间:%1,时间间隔%2秒")
                         .arg(it->TransTime.toString("yyyy-MM-dd hh:mm:ss"))
                         .arg(nSubSeconds));
                transInfo = *it;
                return true;
            }
        }
    }
    return false;
}

bool CETCLaneCtrl::RemoveHasTransInfo(const CTransInfo *pTransInfo)
{
    QMutexLocker locker(&m_lockHaveTransVehMt);
    if (m_HaveTransVeh.isEmpty()) return false;
    if (!pTransInfo) {
        m_HaveTransVeh.pop_front();
        return true;
    } else {
        QList<CTransInfo>::iterator it = m_HaveTransVeh.begin();
        for (; it != m_HaveTransVeh.end(); ++it) {
            QString sTmpVLP = GB2312toUnicode(it->VehInfo.szVehPlate);
            QString sVLP = GB2312toUnicode(pTransInfo->VehInfo.szVehPlate);
            if (sTmpVLP == sVLP) {
                m_HaveTransVeh.erase(it);
                return true;
            }
        }
    }
    return false;
}

bool CETCLaneCtrl::CheckVehhasTransByCardId(const QString &sCardId, CTransInfo &transInfo)
{
    QMutexLocker locker(&m_lockHaveTransVehMt);
    if (m_HaveTransVeh.isEmpty()) return false;
    QList<CTransInfo>::iterator it = m_HaveTransVeh.begin();
    time_t curTime = QDateTime::currentDateTime().toTime_t();
    for (; it != m_HaveTransVeh.end(); ++it) {
        QString sTmpCardId;
        sTmpCardId = QString::fromAscii(it->IccInfo.ProCardBasicInfo.szCardNo);
        if (sTmpCardId == sCardId) {
            int nSubSeconds = qAbs(curTime - it->m_InQueTime);
            if (nSubSeconds < 300) {
                DebugLog(QString("车辆重复交易,时间间隔%1秒").arg(nSubSeconds));
                transInfo = *it;
                return true;
            }
        }
    }
    return false;
}

bool CETCLaneCtrl::CheckVehhastransForPaperCard(const QString &sPaperNo, const QString &sPlate,
                                                CTransInfo &transInfo, QString &sError)
{
    QMutexLocker locker(&m_lockHaveTransVehMt);
    if (m_HaveTransVeh.isEmpty()) return false;
    QList<CTransInfo>::iterator it = m_HaveTransVeh.begin();
    time_t curTime = QDateTime::currentDateTime().toTime_t();
    for (; it != m_HaveTransVeh.end(); ++it) {
        if (!it->bTransOk()) continue;

        QString sTmpVLP = GB2312toUnicode(it->VehInfo.szVehPlate);
        QString sMediaNo;
        if (it->mediaType == MediaType_Paper) {
            sMediaNo = it->m_sPaperId;
        }

        if (sPlate.length() > 0) {
            if (sTmpVLP == sPlate) {
                int nSubSeconds = qAbs(curTime - it->TransTime.toTime_t());
                if (nSubSeconds < Ptr_Info->GetMaxInterval()) {
                    DebugLog(QString("车辆已交易,上次交易时间:%1,时间间隔%2秒")
                             .arg(it->TransTime.toString("yyyy-MM-dd hh:mm:ss"))
                             .arg(nSubSeconds));
                    transInfo = *it;
                    sError = QString("%1 在本车道已交易,%2分钟内不允许交易")
                            .arg(sPlate)
                            .arg(Ptr_Info->GetMaxInterval() / 60);
                    return true;
                }
            }
        }
        if (sPaperNo.length() > 0) {
            if (sPaperNo == sMediaNo) {
                DebugLog(QString("纸卡%1,已交易,上次交易时间%2")
                         .arg(sPaperNo)
                         .arg(it->TransTime.toString("yyyy-MM-dd hh:mm:ss")));
                transInfo = *it;
                sError = QString("纸券%1在本车道已交易,不允许使用").arg(sPaperNo);
                return true;
            }
        }
    }
    return false;
}

bool CETCLaneCtrl::CheckReservedVeh(quint32 dwOBUId)
{
    QMutexLocker locker(&m_ReserveMt);
    if (m_ReservedQue.isEmpty()) {
        return NULL;
    }
    QList<CTransInfo>::iterator it = m_ReservedQue.begin();
    qint64 curTime = QDateTime::currentDateTime().toMSecsSinceEpoch();
    for (; it != m_ReservedQue.end(); ++it) {
        if (it->dwOBUID == dwOBUId) {
            //超过3分钟不认为是倒车车辆。
            if (it->m_BeginTime < curTime) {
                if (curTime - it->m_BeginTime > 180000) {
                    return false;
                }
            }
            return true;
        }
    }
    return false;
}

bool CETCLaneCtrl::CheckVehInVehQue(const CTransInfo *pTransInfo)
{
    QString sCardId = QString::fromAscii(pTransInfo->IccInfo.ProCardBasicInfo.szCardNo);
    {
        QMutexLocker locker(&m_transInfoMt);
        foreach (CTransInfo *pt, m_VehQueue) {
            QString sCardIdInque = QString::fromAscii(pt->IccInfo.ProCardBasicInfo.szCardNo);
            if (pt->dwOBUID == pTransInfo->dwOBUID || sCardId == sCardIdInque) return true;
        }
    }
    {
        QMutexLocker locker(&m_WaitTransMt);
        foreach (CTransInfo *pt, m_VehWaitQueue) {
            QString sCardIdInque = QString::fromAscii(pt->IccInfo.ProCardBasicInfo.szCardNo);
            if (pt->dwOBUID == pTransInfo->dwOBUID || sCardIdInque == sCardId) return true;
        }
    }
    return false;
}

void CETCLaneCtrl::ShowQrCode(const CTransInfo *pTransInfo)
{
    //    GetMainDlg()->ShowQRCode(NULL);
    if (Ptr_Info->IsEntryLane()) return;
    // if (Ptr_Info->IsETCLane()) return;
    if (!isCar(pTransInfo->VehInfo.VehClass)) {
        //货车二维码信息
        QrCodeInfo_Lane QrCodeInfo;
        QrCodeInfo.vehicleId = QString("%1_%2")
                .arg(GB2312toUnicode(pTransInfo->VehInfo.szVehPlate))
                .arg(pTransInfo->VehInfo.nVehPlateColor);

        QrCodeInfo.enStationId = pTransInfo->vehEntryInfo.sEnGBStationId;
        QrCodeInfo.exStationId = Ptr_Info->GetGBStationId();
        QrCodeInfo.enWeight = pTransInfo->vehEntryInfo.dwTotalWeight;
        QrCodeInfo.exWeight = pTransInfo->m_dwToTalWeight;
        QrCodeInfo.mediaType = pTransInfo->mediaType;

        if (pTransInfo->m_sId.isEmpty()) {
            /*
            QrCodeInfo.enStationId = pTransInfo->vehEntryInfo.sEnGBStationId;
            QrCodeInfo.exStationId = Ptr_Info->GetGBStationId();
            QrCodeInfo.enWeight = pTransInfo->vehEntryInfo.dwTotalWeight;
            QrCodeInfo.exWeight = pTransInfo->m_dwToTalWeight;
            QrCodeInfo.mediaType = pTransInfo->mediaType;
            */

            QString sBatch, sNo;

            QString sLaneHex = Ptr_Info->GetHexLaneID();
            QString sId =
                    Ptr_Info->GetGBLaneId() + CBatchMgr::GetBatchMgr()->GetBatchInfo(
                        sBatch, sNo, sLaneHex, CBatchMgr::SnType_Lane, true);
            QrCodeInfo.transactionId = sId;
        } else {
            QrCodeInfo.transactionId = pTransInfo->m_sId;
        }
        DebugLog(QString("货车%1二维码流水号:%2")
                 .arg(QrCodeInfo.vehicleId)
                 .arg(QrCodeInfo.transactionId));

        QrCodeInfo.passId = pTransInfo->GetPassId();
        QrCodeInfo.exTime = QDateTime2GBTimeStr(pTransInfo->TransTime);
        QrCodeInfo.transPayType = pTransInfo->m_transPayType;
        QrCodeInfo.fee = pTransInfo->m_nTransFee;
        QrCodeInfo.payFee = pTransInfo->m_nTotalFee;
        QrCodeInfo.vehicleSign =
                QString("0x") +
                QString("%1").arg(pTransInfo->m_bVehState, 2, 16, QLatin1Char('0')).toUpper();
        QrCodeInfo.nProvinceCount = pTransInfo->m_nProvinceCount;
        // GetMainDlg()->ShowQRCode(&QrCodeInfo);
        GetMainDlg()->SetQrCode(&QrCodeInfo);
    }
}

void CETCLaneCtrl::TestQrCode()
{
    if (Ptr_Info->IsEntryLane()) return;
    // if (Ptr_Info->IsETCLane()) return;
    //货车二维码信息
    QrCodeInfo_Lane QrCodeInfo;
    QrCodeInfo.vehicleId = QString("%1_%2").arg(QString("鲁LLC668")).arg(1);

    QrCodeInfo.enStationId = Ptr_Info->GetGBStationId();
    QrCodeInfo.exStationId = Ptr_Info->GetGBStationId();
    QrCodeInfo.enWeight = 49000;
    QrCodeInfo.exWeight = 49000;
    QrCodeInfo.mediaType = 1;

    QString sBatch, sNo;

    QString sLaneHex = Ptr_Info->GetHexLaneID();
    QString sId =
            Ptr_Info->GetGBLaneId() +
            CBatchMgr::GetBatchMgr()->GetBatchInfo(sBatch, sNo, sLaneHex, CBatchMgr::SnType_Lane, true);
    QrCodeInfo.transactionId = sId;

    DebugLog(
                QString("货车%1二维码流水号:%2").arg(QrCodeInfo.vehicleId).arg(QrCodeInfo.transactionId));

    QrCodeInfo.passId = "030";
    QrCodeInfo.exTime = QDateTime2GBTimeStr(QDateTime::currentDateTime());
    QrCodeInfo.transPayType = 1;
    QrCodeInfo.fee = 10000;
    QrCodeInfo.payFee = 1000;
    QrCodeInfo.vehicleSign =
            QString("0x") + QString("%1").arg(0xff, 2, 16, QLatin1Char('0')).toUpper();
    QrCodeInfo.nProvinceCount = 1;  // pTransInfo->m_nProvinceCount;
    // GetMainDlg()->ShowQRCode(&QrCodeInfo);
    GetMainDlg()->SetQrCode(&QrCodeInfo);
}

void CETCLaneCtrl::SetFrontRsuState(bool bWork) { m_bFrontRsuWork = bWork; }

bool CETCLaneCtrl::IsFrontRsuWork() { return Ptr_Info->bHaveFrontDev() && m_bFrontRsuWork; }

bool CETCLaneCtrl::OnDIChangeEvent(qint32 nDI, bool bStatus, QString sStatus)
{
    //过车线圈
    SetLastLoopStatus(nDI, bStatus, sStatus);

    if (DI_LoopDetect == nDI) return ProcessDetectLoopEvent(bStatus);
    if (DI_LoopFront == nDI) {
        return ProcessFrontLoopEvent(bStatus);
    }
    if (DI_LoopBack == nDI) return ProcessBackLoopEvent(bStatus);
    return true;
}

bool CETCLaneCtrl::ProcessBackLoopEvent(bool bStatus)
{
    if (bStatus) {
        StopVehLeaveTimer();
    }
    if (m_bLongVeh) {
        if (bStatus) {
            GetMainDlg()->ShowLog(QString("超长车状态后线圈检测到车辆不予处理"));
            DebugLog(QString("超长车状态后线圈检测到车辆不予处理"));
        } else {
            GetMainDlg()->ShowLog(QString("超长车状态后线圈检测到车辆不予处理"));
            DebugLog(QString("超长车状态后线圈检测到车辆离开不予处理"));
        }
        return true;
    }
    if (bStatus) {
        VehPass();
    } else {
        StopVehDetectTimer();
        CTransInfo *pTransInfo = RemoveTransInfoFromQue(true, true);
        if (Ptr_Info->IsMTCLane()) {
            CVehAxisInfo vehAxisInfo;
            VehWeightInfo::GetVehWeightInfo()->RemoveFirst(&vehAxisInfo);
            DebugLog(QString("车辆过车,删除计重车辆,总重: %1")
                     .arg(vehAxisInfo.GetConfirmedTotalRawWeight()));
        }

        if (!pTransInfo) {  //如果后队列没车，判断前队列是否有车，如果有，则认为该车过光栅时，没检测到
            pTransInfo = PopTransInfoFromFrontList();
        }
        if (pTransInfo) {
            if (pTransInfo->bTransOk() && pTransInfo->bWaitToSave()) {
                QString sVehPlate = GB2312toUnicode(pTransInfo->VehInfo.szVehPlate);
                DebugLog(QString("车辆%1离开后线圈,流水未保存,重新保存").arg(sVehPlate));
                pTransInfo->lSpeedStopTime = QDateTime::currentDateTime().toMSecsSinceEpoch();
                //为防止清除掉后车车牌识别结果，此处不取车牌识别信息

                SaveLastTransInfo(1, pTransInfo, false);
            }
        }
        if (!bAllowllContinuePass(true)) {
            DebugLog("检测到车辆离开,落杆");
        } else {
            if (pTransInfo && (!pTransInfo->bTransOk())) {
                DebugLog("异常车离开,重新抬杆放行");
                SetAllowPass(true, false);
            }
            DebugLog("车辆离开后线圈,后续车辆继续通行");
        }
        if (pTransInfo) delete pTransInfo;
    }
    return true;
}

bool CETCLaneCtrl::ProcessDetectLoopEvent(bool bStatus)
{
    CIOCard *pIO = CDeviceFactory::GetIOCard();
    if (!pIO) return false;

    QString sLastLoopStatus = GetLastLoopStatus();
    if (bStatus) {
        DebugLog(QString("光栅1有车,上次状态:%1").arg(sLastLoopStatus));
        m_OnDetectTime = QDateTime::currentMSecsSinceEpoch();
    } else {
        //非前置栏杆，检查并保存流水
        m_OnDetectTime = 0;
        DebugLog(QString("光栅1无车,上次状态:%1").arg(sLastLoopStatus));
    }
    return true;
}

bool CETCLaneCtrl::ProcessFrontLoopEvent(bool bStatus)
{
    CIOCard *pIO = CDeviceFactory::GetIOCard();
    if (!pIO) return false;

    QString sLastLoopStatus = GetLastLoopStatus();
    CIOCard *pIOCard = CDeviceFactory::GetIoCard();
    CIODevStatus detectLoopStatus;
    pIOCard->GetDIPortStatus(DI_LoopDetect, detectLoopStatus);

    QString sDetectLoopStatus = detectLoopStatus.bStatus ? "1" : "0";
    CTransInfo *pTransInfo = GetLastWasteOfNotSaved(false);
    if (bStatus) {
        DebugLog(QString("光栅2有车,上次线圈状态:%1，检测线圈状态:%2")
                 .arg(sLastLoopStatus)
                 .arg(sDetectLoopStatus));

        //统一按后置栏杆处理
        if (detectLoopStatus.bStatus ||
                sLastLoopStatus == QString("11")) {  // sLastLoopStatus==QString("11")){
            ProcessFrontLoopOnVehEvent();
        }
        return true;
    } else {
        //在这里保存抓拍图片
        DebugLog(QString("光栅2无车,上次状态:%1").arg(sLastLoopStatus));

        if (!detectLoopStatus.bStatus) {  // sLastLoopStatus==QString("10")){
            if (!pTransInfo) {
                DebugLog("前线圈无车,车辆驶入车道,但车辆队列无车!!!!!!!!!!!!!!!!!");
                return true;
            }
            //双天线没有前车牌识别，车牌识别统一用后车牌识别结果
            /*
            CAutoRegInfo autoRegInfo;
            if (GetETCAutoRegInfo(DevIndex_First, autoRegInfo, true)) {
                SaveAutoVLPResultToVehQue(DevIndex_First, autoRegInfo);
                DebugLog(QString("车辆离开前线圈，保存识别结果,车牌识别Id:%1,图片文件:%2,车牌:%3")
                             .arg(autoRegInfo.id)
                             .arg(autoRegInfo.sBigFileName)
                             .arg(autoRegInfo.sAutoVehPlate));
            }*/
        } else if (detectLoopStatus.bStatus) {
            //车辆倒车
            DebugLog(QString("车辆离开前线圈,检测到车辆倒车"));
            if (pTransInfo) {
                if (pTransInfo->bTransOk()) {  // && pTransInfo->bWaitToSave()){
                    DebugLog("交易成功车辆倒车,删除队列内车辆");
                    // SaveLastTransInfo(0,pTransInfo,true,false);
                    pTransInfo = RemoveTransInfoFromQue(false, true);
                    if (!pTransInfo) return true;
                    pTransInfo->m_bReserved = true;
                    bool bSaveTrans = true;
                    /*
                    if (Ptr_Info->IsCheckVehInQue()) {
                        if (!Ptr_Info->IsDeleteReservVeh()) {
                            AddNewTransInfoToFrontList(*pTransInfo, true);
                            bSaveTrans = false;
                        }
                    }*/
                    //一定先保存流水，车辆倒车，不处理车牌识别信息
                    if (bSaveTrans) {
                        SaveLastTransInfo(1, pTransInfo, false);
                    }
                    AddToReserveQue(pTransInfo);
                    delete pTransInfo;
                    if (0 == GetAllVehCountInQue()) {
                        DebugLog(QString("倒车车队剩余车辆数为0，为避免信号误干扰暂不落杆"));
                        // if (Ptr_Info->GetReverDownBar()) SetRefusePass();
                    }
                    //
                } else {
                    DebugLog("车辆倒车，删除当前队列异常车辆");
                    /*
                    {
                        QMutexLocker locker(&m_WaitTransMt);
                        m_VehWaitQueue.clear();
                    }
                    emit NotifyFrontVehQueChanged();
                    */
                    pTransInfo = RemoveTransInfoFromQue(false);
                    AddToReserveQue(pTransInfo);
                    if (pTransInfo) delete pTransInfo;
                }
                if (!Ptr_Info->bNoWeightDev()) {
                    DebugLog(QString("车辆倒车,删除称重信息"));
                    VehWeightInfo::GetVehWeightInfo()->RemoveLast();
                }
            }
        }
    }
    return true;
}

void CETCLaneCtrl::ProcessFrontLoopOnVehEvent()
{
    {
        QMutexLocker locker(&m_WaitTransMt);
        if (m_VehWaitQueue.isEmpty()) {
            DebugLog("队列增加异常车辆");
            if (m_curTransInfo.bWaitOpResult()) {
                CTransInfo *pTransInfo = new CTransInfo(m_curTransInfo);
                AddTransVehToList(1, pTransInfo);
                DebugLog(
                            QString("队列增加异常车辆,等待交易结果,OBUId:%1").arg(pTransInfo->dwOBUID));
            } else if (m_curTransInfo.transState == CTransInfo::Ts_IsReadingIcc) {
                CTransInfo *pTransInfo = new CTransInfo(m_curTransInfo);
                AddTransVehToList(1, pTransInfo);
                DebugLog(QString("队列增加车辆,正在交易,OBUId:%1").arg(pTransInfo->dwOBUID));
            } else {
                CTransInfo *pTransInfo = new CTransInfo();
                AddTransVehToList(1, pTransInfo);
                DebugLog(QString("队列增加空车"));
            }
            return;
        } else {
            CTransInfo *pTransInfo = m_VehWaitQueue.first();  // PopTransInfoFromFrontList();
            m_VehWaitQueue.pop_front();
            m_OnFrontTime = QDateTime::currentDateTime().toMSecsSinceEpoch();
            pTransInfo->lSpeedStartTime = m_OnFrontTime;

            AddTransVehToList(1, pTransInfo);
            DebugLog(QString("交易完成,车辆进入队列"));
        }
    }
    emit NotifyFrontVehQueChanged();
    return;
}

bool CETCLaneCtrl::SetAllowPass(bool bForceUp, bool bOutTime)
{
    emit NotifySetAllowPass(bForceUp, bOutTime);
    return true;
}

void CETCLaneCtrl::SetRefusePass() { emit NotifySetRefusePass(); }

void CETCLaneCtrl::OnSetRefusePass()
{
    QMutexLocker locker(&m_lockBarMt);
    if (m_pStayOutTimer) {
        m_pStayOutTimer->stop();
    }
    DebugLog("发送落杆指令");
    CDeviceFactory::SetAllow(false);
    m_bBarState = false;
}

void CETCLaneCtrl::OnSetAllowPass(bool bForceUp, bool bOutTime)
{
    //队列内有异常车不抬杆
    DebugLog(QString("接收到抬杆信号,bForceUp:%1,bOutTime:%2").arg(bForceUp).arg(bOutTime));
    if (!bForceUp) {
        QString sError;
        quint32 dwOBUId = 0;
        if (bHaseAbnormalVehInQueue(sError, dwOBUId)) {
            DebugLog("车辆队列有未交易车辆,不抬杆");
            return;
        }
    }

    {
        QMutexLocker locker(&m_lockBarMt);
        DebugLog("发送抬杆指令");
        CDeviceFactory::SetAllow(true);  // GetIOCard()->SetAllowPass();
        m_bBarState = true;
    }
    if (bOutTime) {
        if (m_pStayOutTimer) {
            if (m_pStayOutTimer->isActive()) {
                m_pStayOutTimer->stop();
            }
            m_pStayOutTimer->start(90000);
            emit NotifyStayOutTime("", 0);
        }
    }
    return;
}

//车辆压上后线圈时处理
void CETCLaneCtrl::VehPass()
{
    CTransInfo *pTransInfo = NULL;
    QDateTime curTime = QDateTime::currentDateTime();
    //只检测是否有要离开的车辆
    if (!bAllowllContinuePass(false)) {
        /*
        pTransInfo = RemoveTransInfoFromQue(true, false);
        if (!pTransInfo) {
            DebugLog("当前过车队列为空,车辆闯关");
            //            pTransInfo = new CTransInfo();
            // AddTransVehToList(1, pTransInfo);
        }
        CTransInfo transInfo;
        if (pTransInfo) {  //双天线闯关车
            transInfo = *pTransInfo;
            GetETCAutoRegInfo(DevIndex_Second, transInfo.AutoRegInfo);
            DebugLog(QString("闯关车,获取车牌识别结果,id:%1,vlp:%2")
                         .arg(transInfo.AutoRegInfo.id)
                         .arg(transInfo.AutoRegInfo.sAutoVehPlate));
        } else {
            pTransInfo = &transInfo;
            if (!Ptr_Info->bHaveFrontDev()) {
                //未避免误报警，不自定删除识别信息
                GetETCAutoRegInfo(DevIndex_Second, transInfo.AutoRegInfo, false);
                DebugLog(QString("闯关车,获取车牌识别结果,id:%1,vlp:%2")
                             .arg(transInfo.AutoRegInfo.id)
                             .arg(transInfo.AutoRegInfo.sAutoVehPlate));
            }
        }

        pTransInfo->bInvoice = true;
        pTransInfo->nDetectNum = 1;
        int nMsgCode = lsNormalWorking == m_LaneStatus ? 21 : 20;
        if (Ptr_Info->IsEntryLane())
            InvoceWaste_En(nMsgCode, curTime, pTransInfo);
        else
            InvoceWaste_Ex(nMsgCode, curTime, pTransInfo);
        pTransInfo->SetSaveResult(true);

        if (Ptr_Info->bHaveBackDev()) {
            CDeviceFactory::FareDisplay_ShowHelpMsg(DevIndex_Second, QString("车辆闯关"));
            CDeviceFactory::StartAlarm(DevIndex_Second, 5000);
            CDeviceFactory::GetVCRDev()->RemoveFirstCar();  //闯关删除第一辆车
        }
        emit NotifyVehInvoice();*/
        StartVehDetectTimer();
    } else {
        pTransInfo = RemoveTransInfoFromQue(true, false);
        quint32 nFirstCarId = 0;
        QString sVehPlate;
        if (pTransInfo) {
            sVehPlate = GB2312toUnicode(pTransInfo->VehInfo.szVehPlate);
            SetImageInfoByVcr(pTransInfo);
            nFirstCarId = pTransInfo->m_vcrResult.dwCarID;

            if (pTransInfo->bWaitToSave()) {
                DebugLog(QString("车辆压上后线圈,CarId:%1 %2，报文未保存，继续保存")
                         .arg(pTransInfo->m_vcrResult.dwCarID)
                         .arg(sVehPlate));
                pTransInfo->lSpeedStopTime = QDateTime::currentDateTime().toMSecsSinceEpoch();
                SaveLastTransInfo(1, pTransInfo, true, false);
            } else {
                DebugLog(QString("车辆压上后线圈，CarId:%1 %2,报文已保存")
                         .arg(pTransInfo->m_vcrResult.dwCarID)
                         .arg(sVehPlate));
            }
        } else {
            int nVehCount = Ptr_ETCCtrl->GetVehCount_FrontQue();
            DebugLog(QString("后线圈过车，过车队列为空，取交易完等待离开队列。队列内车辆数：%1")
                     .arg(nVehCount));
            if (nVehCount > 0) {
                pTransInfo = PopTransInfoFromFrontList();
                if (pTransInfo) {
                    sVehPlate = GB2312toUnicode(pTransInfo->VehInfo.szVehPlate);
                    DebugLog(QString("取前队列车辆%1").arg(sVehPlate));
                    SetImageInfoByVcr(pTransInfo);
                    nFirstCarId = pTransInfo->m_vcrResult.dwCarID;
                    pTransInfo->lSpeedStopTime = QDateTime::currentDateTime().toMSecsSinceEpoch();
                    AddTransVehToList(1, pTransInfo);
                    if (pTransInfo->bWaitToSave()) {
                        DebugLog("过车时保存等待进队列车辆流水");
                        SaveLastTransInfo(1, pTransInfo, true, false);
                    }
                }
            }
        }
        if (Ptr_Info->bHaveBackDev()) {
            CFareDisplayer_GB *pFDBack = CDeviceFactory::GetETCFareDisPlayer(DevIndex_Second);
            if (pFDBack) pFDBack->ClearAll();
            if (nFirstCarId > 0)
                CDeviceFactory::GetVCRDev()->RemoveFirstCar(nFirstCarId);
            else {
                if (sVehPlate.length() > 0) CDeviceFactory::GetVCRDev()->RemoveFirstCar(sVehPlate);
            }
            /*
            if (Ptr_Info->IsMTCLane()) {
                CVehAxisInfo vehAxisInfo;
                VehWeightInfo::GetVehWeightInfo()->RemoveFirst(&vehAxisInfo);
                DebugLog(QString("车辆过车,删除计重车辆,总重: %1")
                             .arg(vehAxisInfo.GetConfirmedTotalRawWeight()));
            }*/
        }
    }
    m_bVehLeadOut = false;
    return;
}

void CETCLaneCtrl::SimulateDownBar()
{
    CTransInfo *pTransInfo = NULL;
    {
        QMutexLocker locker(&m_transInfoMt);
        int nVehCnt = m_VehQueue.size();
        DebugLog(QString("模拟过车,队列剩余车辆:%1").arg(nVehCnt));
        while (m_VehQueue.size() > 0) {
            pTransInfo = m_VehQueue.front();
            if (pTransInfo) {
                if (pTransInfo->bTransOk()) {
                    if (pTransInfo->bWaitToSave()) {
                        SaveLastTransInfo(0, pTransInfo, false, false);
                    }
                    m_VehQueue.pop_front();
                    delete pTransInfo;
                    pTransInfo = NULL;
                    if (!Ptr_Info->bNoWeightDev()) {
                        CVehAxisInfo vehAxisInfo;
                        VehWeightInfo::GetVehWeightInfo()->RemoveFirst(&vehAxisInfo);
                        DebugLog(QString("模拟过车,删除计重车辆,总重: %1")
                                 .arg(vehAxisInfo.GetConfirmedTotalRawWeight()));
                    }

                } else {
                    break;
                }
            }
        }
    }
    if (pTransInfo) {
        SetRefusePass();
        m_bLongVeh = false;
        return;
    }
    if (Ptr_Info->bHaveFrontDev()) {
        QMutexLocker locker(&m_WaitTransMt);
        if (!m_VehWaitQueue.isEmpty()) {
            QList<CTransInfo *>::iterator it = m_VehWaitQueue.begin();
            for (; it != m_VehWaitQueue.end(); ++it) {
                CTransInfo *pTransInfo = *it;
                if (pTransInfo) {
                    SaveLastTransInfo(0, pTransInfo, false, false);
                    delete pTransInfo;
                }
            }
            m_VehWaitQueue.clear();
        }
    }
    SetRefusePass();
    m_bLongVeh = false;
    return;
}

void CETCLaneCtrl::DoLongVeh()
{
    SetAllowPass(true, false);
    m_bLongVeh = true;
    return;
}

/**
 * @brief 取队列首辆车
 * @param  bFront true 队列头  false，队列尾 bDlete =true 从队列内删除
 * @return
 */
CTransInfo *CETCLaneCtrl::RemoveTransInfoFromQue(bool bFront, bool bDelete)
{
    CTransInfo *pTransInfo = NULL;
    {
        QMutexLocker locker(&m_transInfoMt);
        if (m_VehQueue.isEmpty()) return NULL;
        if (bFront) {
            pTransInfo = m_VehQueue.front();
            if (bDelete) m_VehQueue.pop_front();
        } else {
            pTransInfo = m_VehQueue.last();
            if (bDelete) m_VehQueue.pop_back();
        }
    }

    if (pTransInfo && bDelete) {
        DebugLog(QString("删除队列车辆,剩余车辆数[%1]").arg(m_VehQueue.size()));
        emit NotifyVehQueChanged();
    }
    return pTransInfo;
}

//更新队列内车辆，如果队列为空，返回false
bool CETCLaneCtrl::UpdateVehInfoInQueue(const CTransInfo &transInfo, bool bLast)
{
    CTransInfo *pTransInfo = NULL;
    {
        QMutexLocker locker(&m_transInfoMt);
        if (m_VehQueue.isEmpty()) {
            // pTransInfo = new CTransInfo(transInfo);
            // m_VehQueue.push_back(pTransInfo);
            return false;
        } else {
            if (bLast)
                pTransInfo = m_VehQueue.last();
            else
                pTransInfo = m_VehQueue.front();
            *pTransInfo = transInfo;
        }
    }
    emit NotifyVehQueChanged();
    return true;
}

bool CETCLaneCtrl::ReplaceLastVehInfoInQueue(const CTransInfo &transInfo)
{
    CTransInfo *pTransInfo = NULL;
    {
        QMutexLocker locker(&m_transInfoMt);
        if (m_VehQueue.isEmpty()) {
            return false;
        } else {
            pTransInfo = m_VehQueue.last();
            if (pTransInfo->dwOBUID == transInfo.dwOBUID) {
                DebugLog(QString("更新队列内车辆,ObuId:%1").arg(transInfo.dwOBUID));
                *pTransInfo = transInfo;
            } else {
                return false;
            }
        }
    }
    emit NotifyVehQueChanged();
    return true;
}

bool CETCLaneCtrl::SetProvinceFeeGroupToLastTrans(int nIndex, quint32 OBUID,
                                                  const QList<CProvinceFeeInfo> &FeeGroup)
{
    CTransInfo *pTransInfo = NULL;
    if (0 == nIndex) {
        QMutexLocker locker(&m_WaitTransMt);
        if (m_VehWaitQueue.size() > 0) {
            pTransInfo = m_VehWaitQueue.last();
            if (pTransInfo && pTransInfo->dwOBUID == OBUID) {
                pTransInfo->IccInfo.ef04Info.AddProvinceFeeInfo(FeeGroup);
                if (pTransInfo->IccInfo.ef04Info.bProvinceCount_After == FeeGroup.size())

                    pTransInfo->SetOncePaySpEvent(OncePay_Sp_RsuB7Failed, false);
                DebugLog("车辆等待队列保存分省信息成功");
                return true;
            }
        }
    }

    {
        QMutexLocker locker(&m_transInfoMt);
        if (m_VehQueue.size() > 0) {
            if (0 == nIndex)
                pTransInfo = m_VehQueue.last();
            else
                pTransInfo = m_VehQueue.first();

            if (pTransInfo && pTransInfo->dwOBUID == OBUID) {
                pTransInfo->IccInfo.ef04Info.AddProvinceFeeInfo(FeeGroup);
                if (pTransInfo->IccInfo.ef04Info.bProvinceCount_After == FeeGroup.size())
                    pTransInfo->SetOncePaySpEvent(OncePay_Sp_RsuB7Failed, false);
                if (pTransInfo->transState == CTransInfo::Ts_Finished) {
                    DebugLog("保存分省信息时,车道流水已经发送完毕");
                }
                DebugLog("过车队列保存分省信息成功");
                return true;
            }
        }
    }
    DebugLog(QString("保存分省信息,没找到OBUID：%1的对应的车辆信息").arg(OBUID));
    return false;
}

bool CETCLaneCtrl::CheckTheFirstVehInQueIsFrontRsu(QString &sPlate)
{
    CTransInfo *pTransInfo = NULL;
    {
        QMutexLocker locker(&m_transInfoMt);
        int nSize = m_VehQueue.size();
        if (nSize > 0) {
            pTransInfo = m_VehQueue.first();

            if (pTransInfo) {
                if (0 == pTransInfo->lSpeedStopTime) {
                    if (pTransInfo->bTransOk() && pTransInfo->m_nRsuIndex == DevIndex_First) {
                        sPlate = GB2312toUnicode(pTransInfo->VehInfo.szVehPlate);
                        return true;
                    }
                } else {
                    DebugLog("首辆车压在前线圈暂不处理");
                }
            }
        }
    }
    return false;
}

CTransInfo *CETCLaneCtrl::GetLastTransInfo(int nIndex)
{
    //前天线交易
    if (DevIndex_First == nIndex) return &m_lastTransInfo;
    //后天线
    else if (DevIndex_Second == nIndex) {
        //后天线或读写器交易1、取队列内最后一辆车(后天线和人工处理取队列首辆车)
        // 2、如果队列内没有，或者非交易失败车辆则最后交易车辆。
        return &m_backLastTransInfo;
        CTransInfo *pTransInfo = RemoveTransInfoFromQue(true, false);
        if ((!pTransInfo) || pTransInfo->bTransOk()) {
            return &m_backLastTransInfo;
        } else {
            return pTransInfo;
        }
    } else {  // mtc 处理 (后天线和人工处理取队列首辆车)
        return &m_backLastTransInfo;
        CTransInfo *pTransInfo = RemoveTransInfoFromQue(true, false);

        if ((!pTransInfo) || pTransInfo->bTransOk()) {
            return &m_backLastTransInfo;
        } else {
            return pTransInfo;
        }
    }
}

bool CETCLaneCtrl::bHasWaitingTransVeh(int nDevIndex)
{
    if (Ptr_Info->bHaveFrontDev()) {
        if (nDevIndex == DevIndex_Second || nDevIndex == DevIndex_Manual) {
            CTransInfo transInfo;
            if (!bAbnormalVehOfTheFirstVehInQueue(transInfo)) {
                return false;
            }
        }
    }
    return true;
}

bool CETCLaneCtrl::HasTransOkVehInLane()
{
    {
        QMutexLocker locker(&m_WaitTransMt);
        if (m_VehWaitQueue.size() > 0) {
            return true;
        }
    }
    {
        QMutexLocker locker(&m_transInfoMt);
        QList<CTransInfo *>::iterator it = m_VehQueue.begin();
        for (; it != m_VehQueue.end(); ++it) {
            CTransInfo *pTransInfo = *it;
            if (pTransInfo && pTransInfo->bTransOk()) {
                return true;
            }
        }
    }
    return false;
}

quint8 CETCLaneCtrl::GetWasteSn(bool bPeek)
{
    static quint8 nWaste = 0;
    if (bPeek) {
        return nWaste;
    }
    quint8 nRlt = nWaste++;
    if (nWaste > 200) nWaste = 0;
    return nRlt;
}

void CETCLaneCtrl::GetVehQueue(QList<CTransInfo> &TransList)
{
    QMutexLocker locker(&m_transInfoMt);
    QList<CTransInfo *>::iterator it = m_VehQueue.begin();
    for (; it != m_VehQueue.end(); ++it) {
        CTransInfo *pTransInfo = *it;
        if (pTransInfo) {
            TransList.push_back(*pTransInfo);
        }
    }
    return;
}

void CETCLaneCtrl::GetFronVehQueue(QList<CTransInfo> &TransList)
{
    QMutexLocker locker(&m_WaitTransMt);
    QList<CTransInfo *>::iterator it = m_VehWaitQueue.begin();
    for (; it != m_VehWaitQueue.end(); ++it) {
        CTransInfo *pTransInfo = *it;
        if (pTransInfo) {
            TransList.push_back(*pTransInfo);
        }
    }
    return;
}

bool CETCLaneCtrl::saveTransWaste_EN(CTransInfo *pTransInfo)
{
    CLaneWaste_ENLU enlu;
    enlu.headInfo.bMsgId = 1;
    enlu.headInfo.bMsgType = 1;
    enlu.headInfo.ReqType = QString("ENLU");
    enlu.headInfo.sFileName = CHttpMsgManager::GetUpLoadFileName(enlu.headInfo.ReqType);
    QString TransCode = QString("%1%2")
            .arg(enlu.headInfo.bMsgType, 2, 10, QLatin1Char('0'))
            .arg(enlu.headInfo.bMsgId, 2, 10, QLatin1Char('0'));
    FillLaneBasicInfo(pTransInfo->TransTime, enlu.basicInfo, TransCode, pTransInfo);

    /*
    enlu.basicInfo.enTollStation = pTransInfo->m_curGantryInfo.nStationId;
    enlu.basicInfo.enTollLane =
        QString("%1").arg(pTransInfo->m_curGantryInfo.bLaneId, 3, 10, QLatin1Char('0'));
    enlu.basicInfo.enTollStationHex = pTransInfo->m_curGantryInfo.sStationHex;
    enlu.basicInfo.enTollLaneHex = pTransInfo->m_curGantryInfo.sLaneHex;
    enlu.basicInfo.enTollStationId =
        pTransInfo->m_curGantryInfo.sGBStationId;  // Ptr_Info->GetGBStationId();
    enlu.basicInfo.enTollLaneId =
        pTransInfo->m_curGantryInfo.sGBLaneId;  // Ptr_Info->GetGBLaneId();
        */

    FillLaneVehInfo(pTransInfo, enlu.vehInfo);

    FillIccInfoLane(pTransInfo, enlu.iccInfo);
    FillPayCardInfoLane(pTransInfo, enlu.payCardInfo, true);
    FillWeightInfoLane(pTransInfo, enlu.weightInfo);
    FillAuthInfoLane(enlu.authInfo, pTransInfo);
    FillKeyInfo(enlu.KeyInfo);

    FillEnAddInfoLane(pTransInfo, enlu.AddInfo);

    char LaneDeal[256];
    memset(LaneDeal, '0', sizeof LaneDeal);
    pTransInfo->FillDealStatus(LaneDeal);
    LaneDeal[PT_YSNORMAL] = '1';

    FillDealInfoLane(pTransInfo, LaneDeal, enlu.dealInfo);

    m_ShiftMgr.Sum(enlu, pTransInfo->gantryFeeInfo.cardFee);

    CShiftSumInfo workSumInfo;
    m_ShiftMgr.GetShiftSumInfo(workSumInfo);
    DebugLog(QString("保存流水，当前合计数,车辆计数%1,cpc卡数:%2,etc卡数:%3,纸卡数:%4")
             .arg(workSumInfo.SumRecord.vCnt)
             .arg(workSumInfo.SumRecord.CPCCnt)
             .arg(workSumInfo.SumRecord.etcCardCnt)
             .arg(workSumInfo.SumRecord.papCnt));

    QString sJson = CHttpMsgManager::GetEnLuJSon(enlu);

    QDataToSave dataToSave;
    CHttpMsgManager::PackMsgToSave(dataToSave, sJson, enlu.headInfo.ReqType, enlu.headInfo.bMsgType,
                                   enlu.headInfo.bMsgId);
    // DebugLog("开始发送过车报文");
    QString sTmpFileName;
    bool bRlt =
            m_pDataMgr->SaveData(dataToSave, sTmpFileName, (quint8 *)&workSumInfo, sizeof workSumInfo);

    emit NotifySaveWaste();

    if (pTransInfo->mediaType == MediaType_OBU) {
        RemoteMsgMgr::GetSingleInst()->SendETCTransMsg_Entry(pTransInfo, 5);
    } else {
        if (pTransInfo->mediaType == MediaType_CPC) {
            RemoteMsgMgr::GetSingleInst()->SendCPCTransMsg_Entry(pTransInfo, 5);
        }
    }

    return bRlt;
}

bool CETCLaneCtrl::saveQuanFanChe_En(CTransInfo *pTransInfo)
{
    CLaneWaste_ENLU enlu;
    enlu.headInfo.bMsgId = 1;
    enlu.headInfo.bMsgType = 1;
    enlu.headInfo.ReqType = QString("ENLU");
    enlu.headInfo.sFileName = CHttpMsgManager::GetUpLoadFileName(enlu.headInfo.ReqType);
    QString TransCode = QString("%1%2")
            .arg(enlu.headInfo.bMsgType, 2, 10, QLatin1Char('0'))
            .arg(enlu.headInfo.bMsgId, 2, 10, QLatin1Char('0'));
    FillLaneBasicInfo(pTransInfo->TransTime, enlu.basicInfo, TransCode, pTransInfo);

    FillLaneVehInfo(pTransInfo, enlu.vehInfo);
    enlu.vehInfo.vCount = 0;
    enlu.vehInfo.inductCnt = 0;

    FillIccInfoLane(pTransInfo, enlu.iccInfo);
    FillPayCardInfoLane(pTransInfo, enlu.payCardInfo, true);
    FillWeightInfoLane(pTransInfo, enlu.weightInfo);
    FillAuthInfoLane(enlu.authInfo);

    FillEnAddInfoLane(pTransInfo, enlu.AddInfo);
    FillKeyInfo(enlu.KeyInfo);

    char LaneDeal[256];
    memset(LaneDeal, '0', sizeof LaneDeal);
    pTransInfo->FillDealStatus(LaneDeal);
    LaneDeal[PT_YSNORMAL] = '1';
    LaneDeal[PT_QuanFanChe] = '1';

    FillDealInfoLane(pTransInfo, LaneDeal, enlu.dealInfo);

    QString sJson = CHttpMsgManager::GetEnLuJSon(enlu);

    QDataToSave dataToSave;
    CHttpMsgManager::PackMsgToSave(dataToSave, sJson, enlu.headInfo.ReqType, enlu.headInfo.bMsgType,
                                   enlu.headInfo.bMsgId);
    // DebugLog("开始发送过车报文");
    QString stmpFileName;
    bool bRlt = m_pDataMgr->SaveData(dataToSave, stmpFileName, NULL, 0);

    return bRlt;
}

bool CETCLaneCtrl::saveLoginWaste_En(const QDateTime &loginTime, quint8 bMsgCode)
{
    CLaneWaste_ENLU enlu;
    enlu.headInfo.bMsgType = 3;
    enlu.headInfo.bMsgId = bMsgCode;
    enlu.headInfo.ReqType = QString("ENLU");
    enlu.headInfo.sFileName = CHttpMsgManager::GetUpLoadFileName(enlu.headInfo.ReqType);
    QString TransCode = QString("%1%2")
            .arg(enlu.headInfo.bMsgType, 2, 10, QLatin1Char('0'))
            .arg(enlu.headInfo.bMsgId, 2, 10, QLatin1Char('0'));

    CTransInfo transInfo;
    FillLaneBasicInfo(loginTime, enlu.basicInfo, TransCode, NULL);
    FillAuthInfoLane(enlu.authInfo);
    FillLaneVehInfo(&transInfo, enlu.vehInfo);
    enlu.vehInfo.vCount = 0;
    enlu.vehInfo.inductCnt = 0;
    FillWeightInfoLane(NULL, enlu.weightInfo);
    enlu.iccInfo.cardId = QString("0000000000000000");
    enlu.payCardInfo.terminalNo = QString("000000000000");
    enlu.payCardInfo.terminalTransNo = QString("00000000");

    char LaneDeal[256];
    memset(LaneDeal, '0', sizeof LaneDeal);
    transInfo.FillDealStatus(LaneDeal);

    if (bMsgCode == 1) {
        LaneDeal[PT_YSAPPON] = '0';
        LaneDeal[PT_YSAPPOFF] = '0';
        LaneDeal[PT_YSLOGIN] = '1';
        LaneDeal[PT_YSLOGOUT] = '0';
    } else {
        LaneDeal[PT_YSAPPON] = '0';
        LaneDeal[PT_YSAPPOFF] = '0';
        LaneDeal[PT_YSLOGIN] = '0';
        LaneDeal[PT_YSLOGOUT] = '1';
    }

    FillDealInfoLane(NULL, LaneDeal, enlu.dealInfo);
    enlu.iccInfo.mediaType = 9;
    FillEnAddInfoLane(NULL, enlu.AddInfo);
    FillKeyInfo(enlu.KeyInfo);

    QString sJson = CHttpMsgManager::GetEnLuJSon(enlu);
    QDataToSave dataToSave;
    CHttpMsgManager::PackMsgToSave(dataToSave, sJson, enlu.headInfo.ReqType, enlu.headInfo.bMsgType,
                                   enlu.headInfo.bMsgId);

    int nOpType = 1 == bMsgCode ? 3 : 4;
    RemoteMsgMgr::GetSingleInst()->SendLaneInfoMsg(nOpType);

    QString stmpFileName;
    return m_pDataMgr->SaveData(dataToSave, stmpFileName, 0, 0);
}

bool CETCLaneCtrl::SaveAppStartWaste_En(QDateTime &OccurTime, quint8 bMsgCode)
{
    CLaneWaste_ENLU enlu;
    enlu.headInfo.bMsgType = 4;
    enlu.headInfo.bMsgId = bMsgCode;
    enlu.headInfo.ReqType = QString("ENLU");
    enlu.headInfo.sFileName = CHttpMsgManager::GetUpLoadFileName(enlu.headInfo.ReqType);
    QString TransCode = QString("%1%2")
            .arg(enlu.headInfo.bMsgType, 2, 10, QLatin1Char('0'))
            .arg(enlu.headInfo.bMsgId, 2, 10, QLatin1Char('0'));

    FillLaneBasicInfo(OccurTime, enlu.basicInfo, TransCode);
    FillAuthInfoLane(enlu.authInfo);
    CTransInfo transInfo;
    FillWeightInfoLane(&transInfo, enlu.weightInfo);
    FillLaneVehInfo(&transInfo, enlu.vehInfo);
    enlu.vehInfo.vCount = 0;
    enlu.vehInfo.inductCnt = 0;

    char LaneDeal[256];
    memset(LaneDeal, '0', sizeof LaneDeal);
    transInfo.FillDealStatus(LaneDeal);
    enlu.iccInfo.cardId = QString("0000000000000000");
    enlu.payCardInfo.terminalNo = QString("000000000000");
    enlu.payCardInfo.terminalTransNo = QString("00000000");

    if (bMsgCode == 1) {
        LaneDeal[PT_YSAPPON] = '1';
        LaneDeal[PT_YSAPPOFF] = '0';
        LaneDeal[PT_YSLOGIN] = '0';
        LaneDeal[PT_YSLOGOUT] = '0';
    } else {
        LaneDeal[PT_YSAPPON] = '0';
        LaneDeal[PT_YSAPPOFF] = '1';
        LaneDeal[PT_YSLOGIN] = '0';
        LaneDeal[PT_YSLOGOUT] = '0';
    }
    FillDealInfoLane(&transInfo, LaneDeal, enlu.dealInfo);
    enlu.iccInfo.mediaType = 9;
    FillEnAddInfoLane(NULL, enlu.AddInfo);
    FillKeyInfo(enlu.KeyInfo);

    QString sJson = CHttpMsgManager::GetEnLuJSon(enlu);
    QDataToSave dataToSave;
    CHttpMsgManager::PackMsgToSave(dataToSave, sJson, enlu.headInfo.ReqType, enlu.headInfo.bMsgType,
                                   enlu.headInfo.bMsgId);

    int OpType = 1 == bMsgCode ? 1 : 2;
    RemoteMsgMgr::GetSingleInst()->SendLaneInfoMsg(OpType);
    QString stmpFileName;
    return m_pDataMgr->SaveData(dataToSave, stmpFileName, 0, 0);
}

bool CETCLaneCtrl::InvoceWaste_En(quint8 bMsgCode, QDateTime &occurTime, CTransInfo *pTransInfo)
{
    CLaneWaste_ENLU enlu;
    enlu.headInfo.bMsgType = 1;
    enlu.headInfo.bMsgId = bMsgCode;
    enlu.headInfo.ReqType = QString("ENLU");
    enlu.headInfo.sFileName = CHttpMsgManager::GetUpLoadFileName(enlu.headInfo.ReqType);
    QString TransCode = QString("%1%2")
            .arg(enlu.headInfo.bMsgType, 2, 10, QLatin1Char('0'))
            .arg(enlu.headInfo.bMsgId, 2, 10, QLatin1Char('0'));
    FillLaneBasicInfo(occurTime, enlu.basicInfo, TransCode, pTransInfo);
    FillAuthInfoLane(enlu.authInfo);
    FillIccInfoLane(pTransInfo, enlu.iccInfo);

    char LaneDeal[256];
    memset(LaneDeal, '0', sizeof LaneDeal);

    if (pTransInfo) {
        FillLaneVehInfo(pTransInfo, enlu.vehInfo);
        FillWeightInfoLane(pTransInfo, enlu.weightInfo);
        pTransInfo->FillDealStatus(LaneDeal);
    }
    //函数内部针对pTransInfo是否为空进行判断
    FillDealInfoLane(pTransInfo, LaneDeal, enlu.dealInfo);

    enlu.iccInfo.mediaType = 9;  //无介质
    FillEnAddInfoLane(NULL, enlu.AddInfo);
    enlu.iccInfo.cardId = QString("0000000000000000");
    enlu.payCardInfo.terminalNo = QString("000000000000");
    enlu.payCardInfo.terminalTransNo = QString("00000000");

    QString sJson = CHttpMsgManager::GetEnLuJSon(enlu);
    QDataToSave dataToSave;
    CHttpMsgManager::PackMsgToSave(dataToSave, sJson, enlu.headInfo.ReqType, enlu.headInfo.bMsgType,
                                   enlu.headInfo.bMsgId);
    QString stmpFileName;
    return m_pDataMgr->SaveData(dataToSave, stmpFileName, 0, 0);
}

/*
 * 车队过车报文
 * bMsgId 2=车队开始 3=车队过车 4-车队结束
 */

bool CETCLaneCtrl::MotorWaste_Entry(quint8 bMsgId, QDateTime occurTime, CTransInfo *pTransInfo)
{
    CLaneWaste_ENLU enlu;
    enlu.headInfo.bMsgType = 1;
    enlu.headInfo.bMsgId = bMsgId;

    enlu.headInfo.ReqType = QString("ENLU");
    enlu.headInfo.sFileName = CHttpMsgManager::GetUpLoadFileName(enlu.headInfo.ReqType);
    QString TransCode = QString("%1%2")
            .arg(enlu.headInfo.bMsgType, 2, 10, QLatin1Char('0'))
            .arg(enlu.headInfo.bMsgId, 2, 10, QLatin1Char('0'));
    FillLaneBasicInfo(occurTime, enlu.basicInfo, TransCode, pTransInfo);
    FillAuthInfoLane(enlu.authInfo);
    FillIccInfoLane(NULL, enlu.iccInfo);
    FillKeyInfo(enlu.KeyInfo);

    char LaneDeal[256];
    memset(LaneDeal, '0', sizeof LaneDeal);

    if (pTransInfo) {
        FillLaneVehInfo(pTransInfo, enlu.vehInfo);
        FillWeightInfoLane(pTransInfo, enlu.weightInfo);
        pTransInfo->FillDealStatus(LaneDeal);
    }
    if (3 == bMsgId) enlu.vehInfo.vehicleClass = UVT_MotorCade;
    //函数内部针对pTransInfo是否为空进行判断
    FillDealInfoLane(pTransInfo, LaneDeal, enlu.dealInfo);

    enlu.iccInfo.mediaType = 9;  //无介质
    FillEnAddInfoLane(NULL, enlu.AddInfo);
    enlu.iccInfo.cardId = QString("0000000000000000");
    enlu.payCardInfo.terminalNo = QString("000000000000");
    enlu.payCardInfo.terminalTransNo = QString("00000000");

    if (3 == bMsgId) m_ShiftMgr.AddMotor(1);

    CShiftSumInfo workSumInfo;
    m_ShiftMgr.GetShiftSumInfo(workSumInfo);

    QString sJson = CHttpMsgManager::GetEnLuJSon(enlu);
    QDataToSave dataToSave;
    CHttpMsgManager::PackMsgToSave(dataToSave, sJson, enlu.headInfo.ReqType, enlu.headInfo.bMsgType,
                                   enlu.headInfo.bMsgId);
    QString sTmpFileName;
    return m_pDataMgr->SaveData(dataToSave, sTmpFileName, (quint8 *)&workSumInfo,
                                sizeof workSumInfo);
}

bool CETCLaneCtrl::MotorWaste_Exit(quint8 bMsgId, QDateTime occurTime, CTransInfo *pTransInfo)
{
    CLaneWaste_EXLU exlu;
    exlu.headInfo.bMsgType = 2;
    exlu.headInfo.bMsgId = bMsgId;

    exlu.headInfo.ReqType = QString("EXLU");
    exlu.headInfo.sFileName = CHttpMsgManager::GetUpLoadFileName(exlu.headInfo.ReqType);
    QString TransCode = QString("%1%2")
            .arg(exlu.headInfo.bMsgType, 2, 10, QLatin1Char('0'))
            .arg(exlu.headInfo.bMsgId, 2, 10, QLatin1Char('0'));
    FillLaneBasicInfo(occurTime, exlu.basicInfo, TransCode, pTransInfo);
    FillAuthInfoLane(exlu.authInfo);
    char LaneDeal[256];
    memset(LaneDeal, '0', sizeof LaneDeal);

    if (pTransInfo) {
        FillLaneVehInfo(pTransInfo, exlu.vehInfo);
        pTransInfo->FillDealStatus(LaneDeal);
    }
    if (3 == bMsgId) exlu.vehInfo.vehicleClass = UVT_MotorCade;
    FillDealInfoLane(pTransInfo, LaneDeal, exlu.dealInfo);

    FillAuthInfoLane(exlu.authInfo);
    FillKeyInfo(exlu.keyInfo);
    CTransInfo transInfo;

    transInfo.TransTime = occurTime;
    transInfo.vehEntryInfo.sEnGBLaneId = QString("0");
    transInfo.vehEntryInfo.sEnGBStationId = QString("0");
    transInfo.vehEntryInfo.sEnNetWorkIdHex = QString("0");
    transInfo.vehEntryInfo.sEnStationHex = QString("0");
    transInfo.vehEntryInfo.EnTime = QDateTime::currentDateTime();

    exlu.payCardInfo.algorithmIdentifier = 1;
    exlu.payCardInfo.terminalNo = QString("000000000000");
    exlu.payCardInfo.terminalTransNo = QString("00000000");

    FillIccInfoLane(&transInfo, exlu.iccInfo);
    exlu.iccInfo.mediaType = 9;
    FillVehEnInfoLane(&transInfo, exlu.enInfo);
    // FillIccInfoLane(&transInfo,exlu.iccInfo);

    exlu.intervalInfo.provinceGroup = QString("360201");
    exlu.intervalInfo.tollFeeGroup = QString("0");
    FillWeightInfoLane(&transInfo, exlu.weightInfo);
    FillExAddInfoLane(&transInfo, exlu.addInfo);

    quint8 *bSyncData = NULL;
    int nSyncDataLen = 0;
    CShiftSumInfo workSumInfo;

    if (3 == bMsgId) {
        m_ShiftMgr.AddMotor(1);
        m_ShiftMgr.GetShiftSumInfo(workSumInfo);
        bSyncData = (quint8 *)&workSumInfo;
        nSyncDataLen = sizeof workSumInfo;
    }

    QString sJson = CHttpMsgManager::GetExLuJSon(exlu);
    QDataToSave dataToSave;
    CHttpMsgManager::PackMsgToSave(dataToSave, sJson, exlu.headInfo.ReqType, exlu.headInfo.bMsgType,
                                   exlu.headInfo.bMsgId);
    QString sTmpFileName;
    return m_pDataMgr->SaveData(dataToSave, sTmpFileName, bSyncData, nSyncDataLen);
}

bool CETCLaneCtrl::saveHedgeWaste_Ex(CTransInfo *pTransInfo, const QString &sSourceId)
{
    CLaneWaste_EXLU exlu;
    exlu.headInfo.bMsgType = 2;
    exlu.headInfo.bMsgId = 5;
    exlu.headInfo.ReqType = QString("EXLU");
    exlu.headInfo.sFileName = CHttpMsgManager::GetUpLoadFileName(exlu.headInfo.ReqType);
    QString TransCode = QString("%1%2")
            .arg(exlu.headInfo.bMsgType, 2, 10, QLatin1Char('0'))
            .arg(exlu.headInfo.bMsgId, 2, 10, QLatin1Char('0'));
    FillLaneBasicInfo(pTransInfo->TransTime, exlu.basicInfo, TransCode, pTransInfo);

    FillLaneVehInfo(pTransInfo, exlu.vehInfo);
    exlu.vehInfo.vCount = 0 - exlu.vehInfo.vCount;
    exlu.vehInfo.inductCnt = 0 - exlu.vehInfo.inductCnt;

    FillIccInfoLane(pTransInfo, exlu.iccInfo);
    exlu.iccInfo.cardCnt = 0 - exlu.iccInfo.cardCnt;

    FillPayCardInfoLane(pTransInfo, exlu.payCardInfo, false);

    char LaneDeal[256];
    memset(LaneDeal, '0', sizeof LaneDeal);
    pTransInfo->FillDealStatus(LaneDeal);

    FillDealInfoLane(pTransInfo, LaneDeal, exlu.dealInfo);
    exlu.dealInfo.modifyFlag = 2;
    exlu.dealInfo.sourceId = sSourceId;

    FillVehEnInfoLane(pTransInfo, exlu.enInfo);
    FillTollBaseInfoLane(pTransInfo, exlu.tollBaseInfo);
    FillTollFeeInfoLane(pTransInfo, exlu.tollFeeInfo);
    exlu.tollFeeInfo.payFee = 0 - exlu.tollFeeInfo.payFee;
    exlu.tollFeeInfo.discountFee = 0 - exlu.tollFeeInfo.discountFee;
    exlu.tollFeeInfo.fee = 0 - exlu.tollFeeInfo.fee;

    exlu.tollFeeInfo.rebateMoney = 0 - exlu.tollFeeInfo.rebateMoney;
    exlu.tollFeeInfo.cardCostFee = 0 - exlu.tollFeeInfo.cardCostFee;
    exlu.tollFeeInfo.unpayFee =
            0 - exlu.tollFeeInfo
            .unpayFee;  // TollFeeInfo.payFee -TollFeeInfo.discountFee - TollFeeInfo.fee;
    exlu.tollFeeInfo.unpayCardCost = 0 - exlu.tollFeeInfo.unpayCardCost;
    exlu.tollFeeInfo.ticketFee = 0 - exlu.tollFeeInfo.ticketFee;
    exlu.tollFeeInfo.unifiedFee = 0 - exlu.tollFeeInfo.unifiedFee;

    exlu.tollFeeInfo.enTollMoney = 0 - exlu.tollFeeInfo.enTollMoney;
    exlu.tollFeeInfo.enFreeMoney = 0 - exlu.tollFeeInfo.enFreeMoney;
    exlu.tollFeeInfo.enLastMoney = 0 - exlu.tollFeeInfo.enLastMoney;
    exlu.tollFeeInfo.collectFee = 0 - exlu.tollFeeInfo.collectFee;
    exlu.tollFeeInfo.payOrderNum.clear();
    exlu.invoiceInfo.Clear();

    FiillTollIntervalInfo(pTransInfo, exlu.intervalInfo);
    FillAuthInfoLane(exlu.authInfo);
    FillWeightInfoLane(pTransInfo, exlu.weightInfo);

    FillExAddInfoLane(pTransInfo, exlu.addInfo);
    exlu.addInfo.originFee = 0 - exlu.addInfo.originFee;
    FillIntervalInfo_Lane(exlu.basicInfo.id, pTransInfo, exlu.splitInfo);

    m_ShiftMgr.Sum(exlu, pTransInfo->gantryFeeInfo.cardFee);

    CShiftSumInfo workSumInfo;
    m_ShiftMgr.GetShiftSumInfo(workSumInfo);

    QString sJson = CHttpMsgManager::GetExLuJSon(exlu);
    QDataToSave dataToSave;
    CHttpMsgManager::PackMsgToSave(dataToSave, sJson, exlu.headInfo.ReqType, exlu.headInfo.bMsgType,
                                   exlu.headInfo.bMsgId);
    QString sTmpFileName;
    return m_pDataMgr->SaveData(dataToSave, sTmpFileName, (quint8 *)&workSumInfo,
                                sizeof workSumInfo);
}

bool CETCLaneCtrl::saveTransWaste_EX(CTransInfo *pTransInfo)
{
    CLaneWaste_EXLU exlu;
    exlu.headInfo.bMsgType = 2;
    exlu.headInfo.bMsgId = 1;
    exlu.headInfo.ReqType = QString("EXLU");
    exlu.headInfo.sFileName = CHttpMsgManager::GetUpLoadFileName(exlu.headInfo.ReqType);
    QString TransCode = QString("%1%2")
            .arg(exlu.headInfo.bMsgType, 2, 10, QLatin1Char('0'))
            .arg(exlu.headInfo.bMsgId, 2, 10, QLatin1Char('0'));
    FillLaneBasicInfo(pTransInfo->TransTime, exlu.basicInfo, TransCode, pTransInfo);
    FillLaneVehInfo(pTransInfo, exlu.vehInfo);

    FillIccInfoLane(pTransInfo, exlu.iccInfo);

    FillPayCardInfoLane(pTransInfo, exlu.payCardInfo, false);

    char LaneDeal[256];
    memset(LaneDeal, '0', sizeof LaneDeal);
    pTransInfo->FillDealStatus(LaneDeal);
    LaneDeal[PT_YSNORMAL] = '1';

    FillDealInfoLane(pTransInfo, LaneDeal, exlu.dealInfo);

    FillVehEnInfoLane(pTransInfo, exlu.enInfo);
    FillTollBaseInfoLane(pTransInfo, exlu.tollBaseInfo);
    FillTollFeeInfoLane(pTransInfo, exlu.tollFeeInfo);
    FillInvoiceInfoLane(pTransInfo, exlu.invoiceInfo);
    FiillTollIntervalInfo(pTransInfo, exlu.intervalInfo);
    FillAuthInfoLane(exlu.authInfo);
    FillWeightInfoLane(pTransInfo, exlu.weightInfo);

    FillExAddInfoLane(pTransInfo, exlu.addInfo);
    FillKeyInfo(exlu.keyInfo);
    // ETC 车道不用填
    FillIntervalInfo_Lane(exlu.basicInfo.id, pTransInfo, exlu.splitInfo);
    quint8 *pSyncData = NULL;
    int nSyncDataLen = 0;
    CShiftSumInfo workSumInfo;

    m_ShiftMgr.GetShiftSumInfo(workSumInfo);
    DebugLog(QString("保存流水，当前合计数,车辆计数%1，现金:%2,支付宝:%3,微信:%4")
             .arg(workSumInfo.SumRecord.vCnt)
             .arg(workSumInfo.SumRecord.CashFee)
             .arg(workSumInfo.SumRecord.alipayFee)
             .arg(workSumInfo.SumRecord.weChatFee));

    if (pTransInfo->m_sTmpFileName.isEmpty()) {
        m_ShiftMgr.Sum(exlu, pTransInfo->gantryFeeInfo.cardFee);
        m_ShiftMgr.GetShiftSumInfo(workSumInfo);
        pSyncData = (quint8 *)&workSumInfo;
        nSyncDataLen = sizeof workSumInfo;
        DebugLog(QString("保存流水，更新合计数,车辆计数%1，现金:%2,支付宝:%3,微信:%4")
                 .arg(workSumInfo.SumRecord.vCnt)
                 .arg(workSumInfo.SumRecord.CashFee)
                 .arg(workSumInfo.SumRecord.alipayFee)
                 .arg(workSumInfo.SumRecord.weChatFee));
    } else {
        DebugLog(QString("保存流水,对应临时文件:%1").arg(pTransInfo->m_sTmpFileName));
    }

    QString sJson = CHttpMsgManager::GetExLuJSon(exlu);
    QDataToSave dataToSave;
    CHttpMsgManager::PackMsgToSave(dataToSave, sJson, exlu.headInfo.ReqType, exlu.headInfo.bMsgType,
                                   exlu.headInfo.bMsgId);
    bool bRlt =
            m_pDataMgr->SaveData(dataToSave, pTransInfo->m_sTmpFileName, pSyncData, nSyncDataLen);

    if (pTransInfo->mediaType == MediaType_OBU) {
        RemoteMsgMgr::GetSingleInst()->SendETCTransMsg_Exit(pTransInfo, 5);
    } else {
        if (MediaType_CPC == pTransInfo->mediaType)
            RemoteMsgMgr::GetSingleInst()->SendCPCTransMsg_Exit(pTransInfo, 5);
    }
    emit NotifySaveWaste();

    return bRlt;
}

bool CETCLaneCtrl::saveTmpTransWaste_Ex(CTransInfo *pTransInfo)
{
    CLaneWaste_EXLU exlu;
    exlu.headInfo.bMsgType = 2;
    exlu.headInfo.bMsgId = 1;
    exlu.headInfo.ReqType = QString("EXLU");
    exlu.headInfo.sFileName = CHttpMsgManager::GetUpLoadFileName(exlu.headInfo.ReqType);
    QString TransCode = QString("%1%2")
            .arg(exlu.headInfo.bMsgType, 2, 10, QLatin1Char('0'))
            .arg(exlu.headInfo.bMsgId, 2, 10, QLatin1Char('0'));
    FillLaneBasicInfo(pTransInfo->TransTime, exlu.basicInfo, TransCode, pTransInfo);
    FillLaneVehInfo(pTransInfo, exlu.vehInfo);

    FillIccInfoLane(pTransInfo, exlu.iccInfo);

    FillPayCardInfoLane(pTransInfo, exlu.payCardInfo, false);

    char LaneDeal[256];
    memset(LaneDeal, '0', sizeof LaneDeal);
    pTransInfo->FillDealStatus(LaneDeal);
    LaneDeal[PT_YSNORMAL] = '1';

    FillDealInfoLane(pTransInfo, LaneDeal, exlu.dealInfo);

    FillVehEnInfoLane(pTransInfo, exlu.enInfo);
    FillTollBaseInfoLane(pTransInfo, exlu.tollBaseInfo);
    FillTollFeeInfoLane(pTransInfo, exlu.tollFeeInfo);
    FillInvoiceInfoLane(pTransInfo, exlu.invoiceInfo);
    FiillTollIntervalInfo(pTransInfo, exlu.intervalInfo);
    FillAuthInfoLane(exlu.authInfo);
    FillWeightInfoLane(pTransInfo, exlu.weightInfo);

    FillExAddInfoLane(pTransInfo, exlu.addInfo);
    // ETC 车道不用填
    FillIntervalInfo_Lane(exlu.basicInfo.id, pTransInfo, exlu.splitInfo);

    CShiftSumInfo workSumInfo;
    m_ShiftMgr.GetShiftSumInfo(workSumInfo);
    DebugLog(QString("临时数据保存合计数之前,车辆计数%1，现金:%2,支付宝:%3,微信:%4,etc:%5")
             .arg(workSumInfo.SumRecord.vCnt)
             .arg(workSumInfo.SumRecord.CashFee)
             .arg(workSumInfo.SumRecord.alipayFee)
             .arg(workSumInfo.SumRecord.weChatFee)
             .arg(workSumInfo.SumRecord.etcFee));

    m_ShiftMgr.Sum(exlu, pTransInfo->gantryFeeInfo.cardFee);

    m_ShiftMgr.GetShiftSumInfo(workSumInfo);
    DebugLog(QString("临时数据保存合计数之后,车辆计数%1，现金:%2,支付宝:%3,微信:%4,etc:%5")
             .arg(workSumInfo.SumRecord.vCnt)
             .arg(workSumInfo.SumRecord.CashFee)
             .arg(workSumInfo.SumRecord.alipayFee)
             .arg(workSumInfo.SumRecord.weChatFee)
             .arg(workSumInfo.SumRecord.etcFee));

    QString sJson = CHttpMsgManager::GetExLuJSon(exlu);
    QDataToSave dataToSave;
    CHttpMsgManager::PackMsgToSave(dataToSave, sJson, exlu.headInfo.ReqType, exlu.headInfo.bMsgType,
                                   exlu.headInfo.bMsgId);
    QString sTmpFileName;
    bool bRlt = m_pDataMgr->SaveTmpData(dataToSave, (quint8 *)&workSumInfo, sizeof workSumInfo,
                                        sTmpFileName);

    pTransInfo->m_sTmpFileName = sTmpFileName;
    return bRlt;
}

bool CETCLaneCtrl::saveTransWaste_Repay(CTransInfo *pTransInfo)
{
    if (!pTransInfo) return false;
    CLaneWaste_EXLU exlu;
    exlu.headInfo.bMsgType = 2;
    exlu.headInfo.bMsgId = 6;
    exlu.headInfo.ReqType = QString("EXLU");
    exlu.headInfo.sFileName = CHttpMsgManager::GetUpLoadFileName(exlu.headInfo.ReqType);
    QString TransCode = QString("%1%2")
            .arg(exlu.headInfo.bMsgType, 2, 10, QLatin1Char('0'))
            .arg(exlu.headInfo.bMsgId, 2, 10, QLatin1Char('0'));
    FillLaneBasicInfo(pTransInfo->TransTime, exlu.basicInfo, TransCode, pTransInfo);
    FillLaneVehInfo(pTransInfo, exlu.vehInfo);

    FillIccInfoLane(pTransInfo, exlu.iccInfo);

    FillPayCardInfoLane(pTransInfo, exlu.payCardInfo, false);

    char LaneDeal[256];
    memset(LaneDeal, '0', sizeof LaneDeal);
    pTransInfo->FillDealStatus(LaneDeal);
    LaneDeal[PT_YSNORMAL] = '1';

    FillDealInfoLane(pTransInfo, LaneDeal, exlu.dealInfo);

    FillVehEnInfoLane(pTransInfo, exlu.enInfo);
    FillTollBaseInfoLane(pTransInfo, exlu.tollBaseInfo);
    FillTollFeeInfoLane(pTransInfo, exlu.tollFeeInfo);
    FillInvoiceInfoLane(pTransInfo, exlu.invoiceInfo);
    FiillTollIntervalInfo_Repay(pTransInfo, exlu.intervalInfo);
    FillAuthInfoLane(exlu.authInfo);
    FillWeightInfoLane(NULL, exlu.weightInfo);

    FillExAddInfoLane(pTransInfo, exlu.addInfo);

    FillIntervalInfo_Lane_Repay(exlu.basicInfo.id, pTransInfo, exlu.intervalInfo, exlu.splitInfo);
    FillKeyInfo(exlu.keyInfo);

    m_ShiftMgr.Sum(exlu, pTransInfo->gantryFeeInfo.cardFee);

    CShiftSumInfo workSumInfo;
    m_ShiftMgr.GetShiftSumInfo(workSumInfo);

    QString sJson = CHttpMsgManager::GetExLuJSon(exlu);
    QDataToSave dataToSave;
    CHttpMsgManager::PackMsgToSave(dataToSave, sJson, exlu.headInfo.ReqType, exlu.headInfo.bMsgType,
                                   exlu.headInfo.bMsgId);
    QString sTmpFileName;
    bool bRlt =
            m_pDataMgr->SaveData(dataToSave, sTmpFileName, (quint8 *)&workSumInfo, sizeof workSumInfo);

    return bRlt;
}

bool CETCLaneCtrl::saveTransWaste_Tac(CTransInfo *pTransInfo, quint8 bType)
{
    CLaneWaste_EXLU exlu;
    exlu.headInfo.bMsgType = 2;
    exlu.headInfo.bMsgId = 0 == bType ? 9 : 10;
    exlu.headInfo.ReqType = QString("EXLU");
    exlu.headInfo.sFileName = CHttpMsgManager::GetUpLoadFileName(exlu.headInfo.ReqType);
    QString TransCode = QString("%1%2")
            .arg(exlu.headInfo.bMsgType, 2, 10, QLatin1Char('0'))
            .arg(exlu.headInfo.bMsgId, 2, 10, QLatin1Char('0'));
    FillLaneBasicInfo(pTransInfo->TransTime, exlu.basicInfo, TransCode, pTransInfo);
    FillLaneVehInfo(pTransInfo, exlu.vehInfo);
    /*
    if(pTransInfo->bTransOk())
        exlu.vehInfo.vehicleSign
    =QString("0x%1").arg(pTransInfo->IccInfo.CardTollInfo.bVehState,2,16,QLatin1Char('0'));
        */

    FillIccInfoLane(pTransInfo, exlu.iccInfo);
    FillPayCardInfoLane(pTransInfo, exlu.payCardInfo, false);
    char LaneDeal[256];
    memset(LaneDeal, '0', sizeof LaneDeal);
    pTransInfo->FillDealStatus(LaneDeal);

    FillDealInfoLane(pTransInfo, LaneDeal, exlu.dealInfo);
    LaneDeal[PT_YSNORMAL] = '1';

    FillVehEnInfoLane(pTransInfo, exlu.enInfo);
    FillTollBaseInfoLane(pTransInfo, exlu.tollBaseInfo);
    FillTollFeeInfoLane(pTransInfo, exlu.tollFeeInfo);
    FiillTollIntervalInfo(pTransInfo, exlu.intervalInfo);
    FillAuthInfoLane(exlu.authInfo);
    FillWeightInfoLane(pTransInfo, exlu.weightInfo);
    FillExAddInfoLane(pTransInfo, exlu.addInfo);
    // ETC 车道不用填
    FillIntervalInfo_Lane(exlu.basicInfo.id, pTransInfo, exlu.splitInfo);

    QString sJson = CHttpMsgManager::GetExLuJSon(exlu);
    QDataToSave dataToSave;
    CHttpMsgManager::PackMsgToSave(dataToSave, sJson, exlu.headInfo.ReqType, exlu.headInfo.bMsgType,
                                   exlu.headInfo.bMsgId);
    QString stmpFileName;
    return m_pDataMgr->SaveData(dataToSave, stmpFileName, 0, 0);
}

bool CETCLaneCtrl::saveLoginWaste_Ex(const QDateTime &loginTime, quint8 bMsgCode)
{
    CLaneWaste_EXLU exlu;
    exlu.headInfo.bMsgType = 3;
    exlu.headInfo.bMsgId = bMsgCode;
    exlu.headInfo.ReqType = QString("EXLU");
    exlu.headInfo.sFileName = CHttpMsgManager::GetUpLoadFileName(exlu.headInfo.ReqType);
    QString TransCode = QString("%1%2")
            .arg(exlu.headInfo.bMsgType, 2, 10, QLatin1Char('0'))
            .arg(exlu.headInfo.bMsgId, 2, 10, QLatin1Char('0'));
    FillLaneBasicInfo(loginTime, exlu.basicInfo, TransCode);
    FillAuthInfoLane(exlu.authInfo);

    CTransInfo transInfo;
    transInfo.TransTime = QDateTime::currentDateTime();

    // qsnprintf(transInfo.VehInfo.szVehPlate,sizeof transInfo.VehInfo.szVehPlate,"无");
    transInfo.vehEntryInfo.sEnGBLaneId = QString("0");
    transInfo.vehEntryInfo.sEnGBStationId = QString("0");
    transInfo.vehEntryInfo.sEnNetWorkIdHex = QString("0");
    transInfo.vehEntryInfo.sEnStationHex = QString("0");
    transInfo.vehEntryInfo.EnTime = QDateTime::currentDateTime();

    // qsnprintf(transInfo.OBUBaseInfo.szContractSerialNumber,sizeof
    // transInfo.OBUBaseInfo.szContractSerialNumber,"0000000000000000");
    memset(transInfo.IccInfo.ProCardBasicInfo.szCardNo, '0', 16);

    FillLaneVehInfo(&transInfo, exlu.vehInfo);
    exlu.vehInfo.vCount = 0;
    exlu.vehInfo.inductCnt = 0;
    FillIccInfoLane(&transInfo, exlu.iccInfo);
    exlu.iccInfo.mediaType = 9;
    FillVehEnInfoLane(&transInfo, exlu.enInfo);
    FillWeightInfoLane(&transInfo, exlu.weightInfo);

    exlu.intervalInfo.provinceGroup = QString("360201");
    exlu.intervalInfo.tollFeeGroup = QString("0000");
    exlu.payCardInfo.algorithmIdentifier = 1;
    exlu.payCardInfo.terminalNo = QString("000000000000");
    exlu.payCardInfo.terminalTransNo = QString("00000000");

    char LaneDeal[256];
    memset(LaneDeal, '0', sizeof LaneDeal);
    transInfo.FillDealStatus(LaneDeal);

    if (bMsgCode == 1) {
        LaneDeal[PT_YSAPPON] = '0';
        LaneDeal[PT_YSAPPOFF] = '0';
        LaneDeal[PT_YSLOGIN] = '1';
        LaneDeal[PT_YSLOGOUT] = '0';
    } else {
        LaneDeal[PT_YSAPPON] = '0';
        LaneDeal[PT_YSAPPOFF] = '0';
        LaneDeal[PT_YSLOGIN] = '0';
        LaneDeal[PT_YSLOGOUT] = '1';
    }
    FillDealInfoLane(&transInfo, LaneDeal, exlu.dealInfo);
    FillExAddInfoLane(&transInfo, exlu.addInfo);
    FillKeyInfo(exlu.keyInfo);

    QString sJson = CHttpMsgManager::GetExLuJSon(exlu);
    QDataToSave dataToSave;
    CHttpMsgManager::PackMsgToSave(dataToSave, sJson, exlu.headInfo.ReqType, exlu.headInfo.bMsgType,
                                   exlu.headInfo.bMsgId);

    int nOpType = 1 == bMsgCode ? 3 : 4;
    RemoteMsgMgr::GetSingleInst()->SendLaneInfoMsg(nOpType);
    QString stmpFileName;
    return m_pDataMgr->SaveData(dataToSave, stmpFileName, 0, 0);
}

bool CETCLaneCtrl::SaveAppStartWaste_Ex(QDateTime &OccurTime, quint8 bMsgCode)
{
    CLaneWaste_EXLU exlu;
    exlu.headInfo.bMsgType = 4;
    exlu.headInfo.bMsgId = bMsgCode;
    exlu.headInfo.ReqType = QString("EXLU");
    exlu.headInfo.sFileName = CHttpMsgManager::GetUpLoadFileName(exlu.headInfo.ReqType);
    QString TransCode = QString("%1%2")
            .arg(exlu.headInfo.bMsgType, 2, 10, QLatin1Char('0'))
            .arg(exlu.headInfo.bMsgId, 2, 10, QLatin1Char('0'));
    FillLaneBasicInfo(OccurTime, exlu.basicInfo, TransCode);
    FillAuthInfoLane(exlu.authInfo);
    CTransInfo transInfo;

    transInfo.TransTime = QDateTime::currentDateTime();

    // qsnprintf(transInfo.VehInfo.szVehPlate,sizeof transInfo.VehInfo.szVehPlate,"无");
    transInfo.vehEntryInfo.sEnGBLaneId = QString("0");
    transInfo.vehEntryInfo.sEnGBStationId = QString("0");
    transInfo.vehEntryInfo.sEnNetWorkIdHex = QString("0");
    transInfo.vehEntryInfo.sEnStationHex = QString("0");
    transInfo.vehEntryInfo.EnTime = QDateTime::currentDateTime();

    // qsnprintf(transInfo.OBUBaseInfo.szContractSerialNumber,sizeof
    // transInfo.OBUBaseInfo.szContractSerialNumber,"0000000000000000");
    memset(transInfo.IccInfo.ProCardBasicInfo.szCardNo, '0', 16);

    FillLaneVehInfo(&transInfo, exlu.vehInfo);
    exlu.vehInfo.vCount = 0;
    exlu.vehInfo.inductCnt = 0;

    FillIccInfoLane(&transInfo, exlu.iccInfo);
    exlu.iccInfo.mediaType = 9;
    FillVehEnInfoLane(&transInfo, exlu.enInfo);

    FillWeightInfoLane(&transInfo, exlu.weightInfo);

    exlu.intervalInfo.provinceGroup = QString("360201");
    exlu.intervalInfo.tollFeeGroup = QString("0000");
    exlu.payCardInfo.algorithmIdentifier = 1;

    exlu.payCardInfo.terminalNo = QString("000000000000");
    exlu.payCardInfo.terminalTransNo = QString("00000000");

    char LaneDeal[256];
    memset(LaneDeal, '0', sizeof LaneDeal);
    transInfo.FillDealStatus(LaneDeal);

    if (bMsgCode == 1) {
        LaneDeal[PT_YSAPPON] = '1';
        LaneDeal[PT_YSAPPOFF] = '0';
        LaneDeal[PT_YSLOGIN] = '0';
        LaneDeal[PT_YSLOGOUT] = '0';
    } else {
        LaneDeal[PT_YSAPPON] = '0';
        LaneDeal[PT_YSAPPOFF] = '1';
        LaneDeal[PT_YSLOGIN] = '0';
        LaneDeal[PT_YSLOGOUT] = '0';
    }
    FillDealInfoLane(&transInfo, LaneDeal, exlu.dealInfo);
    FillExAddInfoLane(&transInfo, exlu.addInfo);
    FillKeyInfo(exlu.keyInfo);

    QString sJson = CHttpMsgManager::GetExLuJSon(exlu);
    QDataToSave dataToSave;
    CHttpMsgManager::PackMsgToSave(dataToSave, sJson, exlu.headInfo.ReqType, exlu.headInfo.bMsgType,
                                   exlu.headInfo.bMsgId);
    int OpType = 1 == bMsgCode ? 1 : 2;
    RemoteMsgMgr::GetSingleInst()->SendLaneInfoMsg(OpType);
    QString stmpFileName;
    return m_pDataMgr->SaveData(dataToSave, stmpFileName, 0, 0);
}

/**
 * @brief
 * @param bMsgCode =21 闯关流水
 * @return
 */
bool CETCLaneCtrl::InvoceWaste_Ex(quint8 bMsgCode, QDateTime &occurTime, CTransInfo *pTransInfo)
{
    CLaneWaste_EXLU exlu;
    exlu.headInfo.bMsgType = 2;
    exlu.headInfo.bMsgId = bMsgCode;
    exlu.headInfo.ReqType = QString("EXLU");
    exlu.headInfo.sFileName = CHttpMsgManager::GetUpLoadFileName(exlu.headInfo.ReqType);
    QString TransCode = QString("%1%2")
            .arg(exlu.headInfo.bMsgType, 2, 10, QLatin1Char('0'))
            .arg(exlu.headInfo.bMsgId, 2, 10, QLatin1Char('0'));
    FillLaneBasicInfo(occurTime, exlu.basicInfo, TransCode, pTransInfo);
    FillAuthInfoLane(exlu.authInfo);
    char LaneDeal[256];
    memset(LaneDeal, '0', sizeof LaneDeal);

    if (pTransInfo) {
        FillLaneVehInfo(pTransInfo, exlu.vehInfo);
        pTransInfo->FillDealStatus(LaneDeal);
    }
    FillDealInfoLane(pTransInfo, LaneDeal, exlu.dealInfo);

    FillAuthInfoLane(exlu.authInfo);
    CTransInfo transInfo;

    transInfo.TransTime = occurTime;
    transInfo.vehEntryInfo.sEnGBLaneId = QString("0");
    transInfo.vehEntryInfo.sEnGBStationId = QString("0");
    transInfo.vehEntryInfo.sEnNetWorkIdHex = QString("0");
    transInfo.vehEntryInfo.sEnStationHex = QString("0");
    //闯关车入口时间不能超过出口时间
    transInfo.vehEntryInfo.EnTime = QDateTime::currentDateTime().addSecs(-300);

    exlu.payCardInfo.algorithmIdentifier = 1;
    exlu.payCardInfo.terminalNo = QString("000000000000");
    exlu.payCardInfo.terminalTransNo = QString("00000000");

    FillIccInfoLane(&transInfo, exlu.iccInfo);
    exlu.iccInfo.mediaType = 9;
    FillVehEnInfoLane(&transInfo, exlu.enInfo);
    // FillIccInfoLane(&transInfo,exlu.iccInfo);

    exlu.intervalInfo.provinceGroup = QString("360201");
    exlu.intervalInfo.tollFeeGroup = QString("0");
    FillWeightInfoLane(&transInfo, exlu.weightInfo);
    FillExAddInfoLane(&transInfo, exlu.addInfo);

    QString sJson = CHttpMsgManager::GetExLuJSon(exlu);
    QDataToSave dataToSave;
    CHttpMsgManager::PackMsgToSave(dataToSave, sJson, exlu.headInfo.ReqType, exlu.headInfo.bMsgType,
                                   exlu.headInfo.bMsgId);
    QString stmpFileName;
    return m_pDataMgr->SaveData(dataToSave, stmpFileName, 0, 0);
}

bool CETCLaneCtrl::SendWorkSum_En(const CShiftSumInfo &ShiftSumInfo)
{
    CShiftSum_ENLSU enlsu;
    QString sLoginTime;
    QDateTime loginTime;
    if (GBTimeStr2QDateTime(ShiftSumInfo.szStartTime, loginTime)) {
        sLoginTime = loginTime.toString("yyyyMMddhhmmss");
    }

    enlsu.batchNum = Ptr_Info->GetGBLaneId() + sLoginTime + QString::number(ShiftSumInfo.nOperID);
    enlsu.modifyFlag = 1;
    enlsu.station = ShiftSumInfo.nStationID;
    enlsu.lane = ShiftSumInfo.nLaneID;
    enlsu.stationId = Ptr_Info->GetGBStationId();
    enlsu.laneId = Ptr_Info->GetGBLaneId();  //国标
    QDateTime workDate;
    ConvertChar14ToDateTime(workDate, ShiftSumInfo.szLDate);
    enlsu.workDate = workDate.toString("yyyy-MM-dd");  //工作日
    enlsu.shift = ShiftSumInfo.wShiftID;
    enlsu.tollCollectorID = QString::number(ShiftSumInfo.nOperID);  //操作员工号
    enlsu.tollcollectorName = GB2312toUnicode(ShiftSumInfo.szOperName);
    enlsu.laneType =
            Ptr_Info->ConverLaneTypeToTrans();  // 1;            //车道类型 1-ETC 2-Mtc 3-me
    enlsu.bl_SubCenter = QString("%1").arg(Ptr_Info->GetBL_SubCenter());
    enlsu.bl_Center = QString("%1").arg(Ptr_Info->GetBL_Center());
    enlsu.startTime = QString::fromAscii(ShiftSumInfo.szStartTime);  // yyyy-MM-ddThh:mm:ss
    enlsu.endTime = QString::fromAscii(ShiftSumInfo.szEndTime);

    enlsu.invStartNo = "0";                    //发票起号
    enlsu.statusFlag = 0;                      //状态标志 0-上班未下 1-已下班 2-已交班
    enlsu.invNum = 0;                          //发票数
    enlsu.badInvNum = 0;                       //废票数
    enlsu.vCnt = ShiftSumInfo.SumRecord.vCnt;  //总车辆数,含下班后闯关流水，不包括承载门夹流水
    enlsu.inductCnt = ShiftSumInfo.SumRecord.inducCnt;  //线圈检测数
    enlsu.CPCCnt = ShiftSumInfo.SumRecord.CPCCnt;
    enlsu.papCnt = ShiftSumInfo.SumRecord.papCnt;
    enlsu.damCardCnt = ShiftSumInfo.SumRecord.damCardCnt;  //坏卡数
    enlsu.ETCCardCnt =
            ShiftSumInfo.SumRecord.etcCardCnt;  // ETC卡计数,不包括承载门夹功能的车道产生的流水
    enlsu.transCnt = ShiftSumInfo.SumRecord.transCnt;  // ETC扣费交易笔数（承载门架功能的交易流水）
    enlsu.transFeeNum =
            ShiftSumInfo.SumRecord.TransFeeSum;  //交易金额，扣费交易金额合计（承载ETc门夹功能流水）
    enlsu.invEndNo = QString("%1").arg(ShiftSumInfo.SumRecord.invEndNo);  //发票止号
    enlsu.unsendcardcnt = ShiftSumInfo.SumRecord.unsendCardCnt;
    enlsu.shiftTransCount = ShiftSumInfo.SumRecord.shiftTransCount;
    enlsu.shiftTransFeeSum = ShiftSumInfo.SumRecord.shiftTransFeeSum;

    QString sJson;
    sJson = CHttpMsgManager::GetEnlsuJSon(enlsu);
    // DebugLog(QString("入口小班汇总:%1").arg(sJson));
    QDataToSave dataToSave;
    CHttpMsgManager::PackMsgToSave(dataToSave, sJson, QString("ENLSU"), 0, 0);
    QString stmpFileName;
    return m_pDataMgr->SaveData(dataToSave, stmpFileName, 0, 0);
}

bool CETCLaneCtrl::SendWorkSum_Ex(const CShiftSumInfo &ShiftSumInfo)
{
    QString sLoginTime;
    QDateTime loginTime;
    if (GBTimeStr2QDateTime(ShiftSumInfo.szStartTime, loginTime)) {
        sLoginTime = loginTime.toString("yyyyMMddhhmmss");
    }

    CShiftSum_EXLSU exlsu;
    exlsu.batchNum =
            Ptr_Info->GetGBLaneId() + sLoginTime +
            QString::number(ShiftSumInfo.nOperID);  //批次号车道编号（国标）+上班时间+收费员工号
    exlsu.modifyFlag = 1;
    exlsu.station = ShiftSumInfo.nStationID;
    exlsu.lane = ShiftSumInfo.nLaneID;
    exlsu.stationId = Ptr_Info->GetGBStationId();  //国标
    exlsu.laneId = Ptr_Info->GetGBLaneId();        //国标

    QDateTime workDate;
    ConvertChar14ToDateTime(workDate, ShiftSumInfo.szLDate);

    exlsu.workDate = workDate.toString("yyyy-MM-dd");
    exlsu.shift = ShiftSumInfo.wShiftID;
    exlsu.tollCollectorID = QString::number(ShiftSumInfo.nOperID);
    exlsu.tollcollectorName = GB2312toUnicode(ShiftSumInfo.szOperName);
    exlsu.laneType = Ptr_Info->ConverLaneTypeToTrans();
    exlsu.bl_SubCenter = QString::number(Ptr_Info->GetBL_SubCenter());
    exlsu.bl_Center = QString::number(Ptr_Info->GetBL_Center());
    exlsu.startTime = QString::fromAscii(ShiftSumInfo.szStartTime);
    exlsu.endTime = QString::fromAscii(ShiftSumInfo.szEndTime);
    exlsu.statusFlag = ShiftSumInfo.statusFlag;
    exlsu.invStartNo =
            QString("%1").arg(ShiftSumInfo.SumRecord.invStartNo, 8, 10, QLatin1Char('0'));
    exlsu.invNum = ShiftSumInfo.SumRecord.invNum;
    exlsu.badInvNum = ShiftSumInfo.SumRecord.badInvNum;
    exlsu.vCnt = ShiftSumInfo.SumRecord.vCnt;
    exlsu.inductCnt = ShiftSumInfo.SumRecord.inducCnt;
    exlsu.CPCCnt = ShiftSumInfo.SumRecord.CPCCnt;
    exlsu.papCnt = ShiftSumInfo.SumRecord.papCnt;
    exlsu.damCardCnt = ShiftSumInfo.SumRecord.damCardCnt;
    exlsu.ETCCardCnt = ShiftSumInfo.SumRecord.etcCardCnt;
    exlsu.transCnt = ShiftSumInfo.SumRecord.transCnt;
    exlsu.transFeeNum = ShiftSumInfo.SumRecord.TransFeeSum;
    // exlsu.unsendcardcnt = ShiftSumInfo.SumRecord.unsendCardCnt;
    exlsu.invEndNo = ShiftSumInfo.SumRecord.invEndNo;
    exlsu.cashRecordCnt = ShiftSumInfo.SumRecord.cashRecordCnt;
    exlsu.cashFee = ShiftSumInfo.SumRecord.CashFee;
    exlsu.thirdPayFee = ShiftSumInfo.SumRecord.thirdPayFee;
    exlsu.unipayFee = ShiftSumInfo.SumRecord.unipayFee;
    exlsu.etcFee = ShiftSumInfo.SumRecord.etcFee;
    exlsu.alipayFee = ShiftSumInfo.SumRecord.alipayFee;
    exlsu.weChatFee = ShiftSumInfo.SumRecord.weChatFee;
    exlsu.cashCardFee = ShiftSumInfo.SumRecord.cashCardFee;
    exlsu.thirdCardFee = ShiftSumInfo.SumRecord.thirdCardFee;
    exlsu.unipayCardFee = ShiftSumInfo.SumRecord.unipayCardFee;
    exlsu.etcCardFee = ShiftSumInfo.SumRecord.etcCardFee;
    exlsu.aplipayCardFee = ShiftSumInfo.SumRecord.aplipayCardFee;
    exlsu.weChatCardFee = ShiftSumInfo.SumRecord.weChatCardFee;
    exlsu.unpayCardCost = ShiftSumInfo.SumRecord.unpayCardCost;
    exlsu.cashFreeMoney = ShiftSumInfo.SumRecord.cashFreeMoney;
    exlsu.cashRebateMoney = ShiftSumInfo.SumRecord.cashRebateMoney;
    exlsu.posFreeMoney = ShiftSumInfo.SumRecord.posFreeMoney;
    exlsu.posRebateMoney = ShiftSumInfo.SumRecord.posRebateMoney;
    exlsu.ticketFee = ShiftSumInfo.SumRecord.ticketFee;
    exlsu.unifiedFee = ShiftSumInfo.SumRecord.unifiedFee;
    exlsu.invEndNo = QString("%1").arg(ShiftSumInfo.SumRecord.invEndNo, 8, 10, QLatin1Char('0'));
    exlsu.shiftTransCount = ShiftSumInfo.SumRecord.shiftTransCount;
    exlsu.shiftTransFeeSum = ShiftSumInfo.SumRecord.shiftTransFeeSum;

    QString sJson;
    sJson = CHttpMsgManager::GetExlsuJSon(exlsu);
    DebugLog(QString("出口小班汇总:%1").arg(sJson));
    QDataToSave dataToSave;
    CHttpMsgManager::PackMsgToSave(dataToSave, sJson, QString("EXLSU"), 0, 0);
    QString stmpFileName;
    return m_pDataMgr->SaveData(dataToSave, stmpFileName, 0, 0);
}

/**
 * @brief CETCLaneCtrl::LaneIsIdle
 *        车道是否空闲（不含未通过车辆）
 * @return
 */
bool CETCLaneCtrl::LaneIsIdle()
{
    if (GetVehCount_FrontQue() > 0) return false;

    QMutexLocker locker(&m_transInfoMt);
    foreach (CTransInfo *pTransInfo, m_VehQueue) {
        if (pTransInfo->bTransOk()) return false;
    }
    return true;
}
/**
 * @brief CETCLaneCtrl::DoViolate
 *        处理闯关
 * @param nViolateId
 * @return
 */
int CETCLaneCtrl::DoViolate(int nViolateId)
{
    Q_UNUSED(nViolateId)

    /*TODO
    QString sCapFileName,sVLPFileName;
    //保存图像
    SaveVehPic(sCapFileName);
    //保存车辆信息
    if(!m_curVehTranInfo){
        GetCurVehTranInfo();
    }else{
        m_curVehTranInfo->m_bIsUsing=true;
    }
    //根据闯关类型
    QString sMsg;
    switch(nViolateId){
    case ViolateT_Violate:{
        int nMsgType = LST_TYPE_VIOLATE;
        if( lsUnlogin==GetLaneStatus())
            nMsgType= LST_TYPE_VIOLATE_LOGOUT;
        m_curVehTranInfo->SetCapFileName(sCapFileName,sVLPFileName);
      //  ClearAutoVehPlate();
        saveViolateMsg(nMsgType);
        Reset();
        sMsg =QString("车辆闯关");
        break;
    }
    case ViolateT_FalseAlarm:
        //this->SaveLaneEventMsg(LEV_FALSEALARM);
        sMsg = QString("设备误报警");
        break;
    case ViolateT_ViolateHeldBack:
        //this->SaveLaneEventMsg(LEV_VIALATEHELDBACK);
        sMsg=QString("闯关车辆被拦截");
        break;
    default:
        return 0;
    }
    //显示一下闯关信息
    CAbstractState::GetCurState()->ShowErrorMessage(sMsg);
    //发交易完成的通知（更新界面）
    emit NotifyTransComplete(false,nViolateId);
    if(m_curVehTranInfo){
        m_curVehTranInfo->m_bIsUsing=false;
        m_curVehTranInfo=NULL;
    }
*/
    return nViolateId;
}

/**
 * @brief CETCLaneCtrl::SaveLaneEventMsg
 *        发送车道事件报文
 * @param nMsgType
 * @return
 */
bool CETCLaneCtrl::SaveLaneEventMsg(qint32 nMsgType) { return true; }

bool CETCLaneCtrl::SaveDoorFrameWaste_New(CTransInfo *pTransInfo)
{
    CalcFee *pFee = (CalcFee *)CParamFileMgr::GetParamFile(cfFareDll);
    if (!pFee) {
        return false;
    }

    if (!pFee->HasLoaded()) {
        ErrorLog(QString("保存门架流水,当前门架计费模块未加载成功"));
    }

    if ((pTransInfo->mediaType == MediaType_OBU && pTransInfo->IccInfo.ef04Info.IsValid()) ||
            (pTransInfo->mediaType == MediaType_CPC && !pTransInfo->m_bBadCard)) {
        CDoorWaste_ETCTU_FD doorMsg;
        doorMsg.Clear();
        FillGantryMsg_New(pTransInfo, doorMsg);

        QString sJson = CHttpMsgManager::GetEtcTuJSon(doorMsg);
        // DebugLog(QString("新版门架流水:%1").arg(sJson));
        QDataToSave dataToSave;
        CHttpMsgManager::PackMsgToSave(dataToSave, sJson, QString("ETCTU"), 0, 0);
        CTollGantryMgr::GetTollGantryMgr()->GantrySum_New(doorMsg,
                                                          pTransInfo->m_curGantryInfo.sGantryId);
        QByteArray gantrySumInfo;
        CTollGantryMgr::GetTollGantryMgr()->GetGantrySumInfo_New(gantrySumInfo);

        QString sTmpFileName;
        return m_pDataMgr->SaveData(dataToSave, sTmpFileName, (quint8 *)gantrySumInfo.constData(),
                                    gantrySumInfo.size(), QSqliteDataSaver::SumType_Hour);
    }
    return true;
}

bool CETCLaneCtrl::SendGantrySumInfo(const CGantrySumInfo_H &SumInfo, const QString &sLaneId)
{
    CDoorTransSumUpload_ETCSU etcSu;
    etcSu.collectId =
            QString::fromAscii(SumInfo.szGantryId) + QString::fromAscii(SumInfo.szCollectHourBatch);
    etcSu.laneNum = sLaneId;  // Ptr_Info->GetGBLaneId();
    etcSu.modifyFlag = 1;

    etcSu.gantryId = QString::fromAscii(SumInfo.szGantryId);
    if (etcSu.gantryId.isEmpty()) {
        DebugLog("etcSu.gantryId isEmpty");
        return true;
    }
    etcSu.computerOrder = 1;
    etcSu.collectDate = QString::fromAscii(SumInfo.szCollectDate);
    etcSu.collectHourBatch = QString::fromAscii(SumInfo.szCollectHourBatch);
    etcSu.batchCount = SumInfo.batchCount;

    for (int i = 0; i < sizeof(SumInfo.TypeCount) / sizeof(CVehCountByCard); ++i) {
        if (SumInfo.TypeCount[i].etcCount > 0)
            etcSu.etcTypeCount += QString("|%1:%2").arg(i).arg(SumInfo.TypeCount[i].etcCount);
        if (SumInfo.TypeCount[i].cpcCount > 0)
            etcSu.cpcTypeCount += QString("|%1:%2").arg(i).arg(SumInfo.TypeCount[i].cpcCount);
    }
    if (etcSu.etcTypeCount.length() > 0) {
        etcSu.etcTypeCount.remove(0, 1);
    }
    if (etcSu.cpcTypeCount.length() > 0) {
        etcSu.cpcTypeCount.remove(0, 1);
    }

    for (int i = 0; i < sizeof(SumInfo.ClassCount) / sizeof(CVehCountByCard); ++i) {
        if (SumInfo.ClassCount[i].etcCount > 0) {
            etcSu.etcClassCount += QString("|%1:%2").arg(i).arg(SumInfo.ClassCount[i].etcCount);
        }
        if (SumInfo.ClassCount[i].cpcCount > 0) {
            etcSu.cpcClassCount += QString("|%1:%2").arg(i).arg(SumInfo.ClassCount[i].cpcCount);
        }
    }

    if (etcSu.etcClassCount.length() > 0) {
        etcSu.etcClassCount.remove(0, 1);
    }
    if (etcSu.cpcClassCount.length() > 0) {
        etcSu.cpcClassCount.remove(0, 1);
    }

    etcSu.etcSuccessCount = SumInfo.etcSucessCount;
    etcSu.etcSuccessFee = SumInfo.etcSuccessFee;
    etcSu.etcFailCount = SumInfo.etcFailCount;

    QString sJson = CHttpMsgManager::GetEtcsuJSon(etcSu);
    // DebugLog(QString("etcsu:%1").arg(sJson));
    QDataToSave dataToSave;
    CHttpMsgManager::PackMsgToSave(dataToSave, sJson, QString("ETCSU"), 0, 0);
    QString stmpFileName;
    return m_pDataMgr->SaveData(dataToSave, stmpFileName, 0, 0);
}

bool CETCLaneCtrl::UploadLog1()
{
    QDate day = QDate::currentDate();
    QDate lastDate = day.addDays(-1);
    //获取最后一次上传成功的日志日期
    QDate uploadDate = Ptr_Info->GetUploadLogDate();
    if (!uploadDate.isValid() ||
            uploadDate >= day)  //若是日期无效或者日期大于等于当前日期 取昨天的日期
    {
        uploadDate = lastDate;

    } else {
        //若时间有效则判断日期是否为昨天
        DebugLog(
                    QString("开始检查上传日志,之前上传时间:%1").arg(uploadDate.toString("yyyy-MM-dd")));
        if (uploadDate == lastDate)  //如果是昨天则直接返回
            return true;
        else
            uploadDate = uploadDate.addDays(1);
    }

    QDateTime now = QDateTime::currentDateTime();
    QString sNow = now.toString("yyyyMMddHHmmsszzz");
    QString sDay = uploadDate.toString("yyyyMMdd");

    CLog4Qt *pLog = get_log(LOGGBSTD);
    if (!pLog) {
        DebugLog("上传日志获取日志对象为空");
        return false;
    }

    QString sFileName = QString("%1/%2.log.zip").arg(pLog->GetPath()).arg(sDay);
    DebugLog(QString("开始上传日志%1").arg(sFileName));

    if (!FileExists(sFileName)) {
        DebugLog(QString("要上传的日志文件不存在[%1]").arg(sFileName));
        //当日志文件不存在时跳过
        if (uploadDate < lastDate) {  //如果上传日志为昨天以前，认为文件不存在，更新上传时间
            Ptr_Info->SetUploadLogDate(uploadDate);
        }
        return false;
    }

    CLog_BASEINFO msg;
    CLogRet_UPLOADRESULT msgret;
    msg.laneId = Ptr_Info->GetGBLaneId();
    msg.stationId = Ptr_Info->GetGBStationId();
    msg.stationName = Ptr_Info->GetStationName();
    qint32 nStationid = Ptr_Info->GetStationID();
    qint32 road = nStationid / 100 % 1000;
    msg.uploadFileName = QString("fangxing_36_%1_%2_%3_3_%4.zip")
            .arg(road)
            .arg(msg.stationId)
            .arg(msg.laneId)
            .arg(sDay);

    QFile file(sFileName);
    if (!file.open(QIODevice::ReadOnly)) {
        DebugLog(QString("要上传的日志文件打开失败[%1]").arg(sFileName));
        return false;
    }
    QByteArray filebody = file.readAll();
    file.close();

    msg.uploadFileMd5 = CalcMD5Code(filebody.data(), filebody.size());
    msg.uploadTime = now.toString("yyyy-MM-ddThh:mm:ss");
    QString filename = QString("FTP_BASEINFO_REQ_%1_%2.zip").arg(msg.laneId).arg(sNow);
    QString sJson = CHttpMsgManager::GetLogBaseInfoJSon(msg);
    filebody = compress(sJson.toUtf8());
    QString sUrl = Ptr_Info->GetUpLoadLogUrl();  // ("http://*************:8890/api/v2/upload");
    DebugLog(QString("upload log,url:%1").arg(sUrl));
    DataSyncResponse retVal;
    retVal.Clear();
    WebUtils::UploadWaste(retVal, filename, filebody, sUrl);

    DebugLogCategory(LOGNETWORK,
                     QString("发送日志请求上传报文[%1][%2]返回[%3][%4]")
                     .arg(filename)
                     .arg(sJson)
                     .arg(retVal.HttpStatusCode)
                     .arg(QString::fromUtf8(retVal.Content.data(), retVal.Content.size())));

    QString sUserName, sPassword, sFileUploadPath;
    if (200 == retVal.HttpStatusCode) {
        QxtJSON JsonParser;
        QVariantMap vaMap =
                JsonParser.parse(QString::fromUtf8(retVal.Content.data(), retVal.Content.size()))
                .toMap();
        QString subcode = JsonParser.stringify(vaMap["subCode"]);
        if (QString("200") != subcode) {
            DebugLog("日志文件上传获取ftp服务器信息失败");
            return false;
        }
        msgret.reqId = JsonParser.stringify(vaMap["reqId"]);
        QVariantList vaList = vaMap["ftpList"].toList();
        if (vaList.isEmpty()) return false;
        foreach (QVariant varData, vaList) {
            QVariantMap subMap = varData.toMap();
            sUserName = JsonParser.stringify(subMap["username"]);
            sUserName = CryptographicCommon::AESDecrypt(sUserName);
            sPassword = JsonParser.stringify(subMap["password"]);
            sPassword = CryptographicCommon::AESDecrypt(sPassword);
            sFileUploadPath = JsonParser.stringify(subMap["fileUploadPath"]);
            if ((!sUserName.isEmpty()) && (!sPassword.isEmpty()) && (!sFileUploadPath.isEmpty()))
                break;
        }
        if (sUserName.isEmpty() || sPassword.isEmpty() || sFileUploadPath.isEmpty()) return false;
    } else {
        return false;
    }

    bool bFlag =
            WebUtils::PutFtpFile(sFileName, sUserName, sPassword,
                                 QString("%1/%2").arg(sFileUploadPath).arg(msg.uploadFileName));
    if (bFlag) {
        Ptr_Info->SetUploadLogDate(uploadDate);
        QString sNewFile = QString("%1.bak").arg(sFileName);
        RenameFile(sFileName, sNewFile);
        DebugLog(QString("日志文件:%1上传成功").arg(sFileName));
    } else {
        DebugLog(QString("日志文件:%1上传失败").arg(sFileName));
    }
    msgret.uploadResult = bFlag ? QString("1") : QString("2");
    msgret.uploadFinishedTime = QDateTime::currentDateTime().toString("yyyy-MM-ddThh:mm:ss");
    // msgret.remark;
    filename = QString("FTP_UPLOADRESULT_REQ_%1_%2.zip").arg(msg.laneId).arg(sNow);
    sJson = CHttpMsgManager::GetLogUploadResultJSon(msgret);
    filebody = compress(sJson.toUtf8());
    retVal.Clear();
    WebUtils::UploadWaste(retVal, filename, filebody, sUrl);
    DebugLogCategory(LOGNETWORK,
                     QString("第一次发送日志请求上传结果报文[%1][%2]返回[%3][%4]")
                     .arg(filename)
                     .arg(sJson)
                     .arg(retVal.HttpStatusCode)
                     .arg(QString::fromUtf8(retVal.Content.data(), retVal.Content.size())));
    if (200 != retVal.HttpStatusCode) {
        QString sDataUrl, sBinFileAuth, sDataUrlBak;
        Ptr_Info->GetDataUrl(sDataUrl, sBinFileAuth, sDataUrlBak);
        retVal.Clear();
        WebUtils::UploadWaste(retVal, filename, filebody, sDataUrl);
        DebugLogCategory(LOGNETWORK,
                         QString("第二次发送日志请求上传结果报文[%1][%2]返回[%3][%4]")
                         .arg(filename)
                         .arg(sJson)
                         .arg(retVal.HttpStatusCode)
                         .arg(QString::fromUtf8(retVal.Content.data(), retVal.Content.size())));
    }

    return true;
}

bool CETCLaneCtrl::sendHeartbeat()
{
    DebugLogCategory(LOGNETWORK, "开始发送心跳");
    CLaneHeart_LHBU_FD msg;
    FillLaneHeart(msg);
    QString sJson = CHttpMsgManager::GetLhbuJSon(msg);
    DebugLogCategory(LOGNETWORK, sJson);
    QDataToSave dataToSave;
    CHttpMsgManager::PackMsgToSave(dataToSave, sJson, QString("LHBU"), 0, 0);
    dataToSave.bInstantMsg = 1;  //即时发送报文，不保存
    QString stmpFileName;
    return m_pDataMgr->SaveData(dataToSave, stmpFileName, 0, 0);
}

void CETCLaneCtrl::OnStayOutTimer()
{
    DebugLog("等待车辆离开超时...");
    m_pStayOutTimer->stop();
    QString sPlate = GB2312toUnicode(m_lastTransInfo.VehInfo.szVehPlate);
    emit NotifyStayOutTime(sPlate, 1);
    return;
}

void CETCLaneCtrl::OnDownBarTimer()
{
    QMutexLocker locker(&m_DownBarMt);
    m_pDownBarTimer->stop();
    CIOCard *pIOCard = CDeviceFactory::GetIOCard();
    if (pIOCard) {
        CIODevStatus ioStatus;
        pIOCard->GetDIPortStatus(DI_LoopBack, ioStatus);
        if (ioStatus.bStatus) {
            VehPass();
            //车辆离开
        } else {
            DebugLog("车辆压上后线圈，但是信号不稳定，为防止za砸车不按过车处理");
        }
    }
    return;
}

void CETCLaneCtrl::OnVehLeaveTimer()
{
    {
        QMutexLocker locker(&m_vehLeaveMx);
        if (m_vehLeaveTimer.isActive()) m_vehLeaveTimer.stop();
    }
    RemoteMsgMgr::GetSingleInst()->SendCallVideoReq(QString("2"));
}

void CETCLaneCtrl::OnVehDetectTimer()
{
    StopVehDetectTimer();
    CTransInfo *pTransInfo = RemoveTransInfoFromQue(true, false);
    CTransInfo transInfo;
    if (pTransInfo) {
        transInfo = *pTransInfo;
        if (transInfo.bTransOk()) {
            DebugLog(QString("闯关车延时判断,当前车辆已交易,不处理"));
            return;
        }
        GetETCAutoRegInfo(DevIndex_Second, transInfo.AutoRegInfo);
        DebugLog(QString("闯关车,获取车牌识别结果,id:%1,vlp:%2")
                 .arg(transInfo.AutoRegInfo.id)
                 .arg(transInfo.AutoRegInfo.sAutoVehPlate));
    } else {
        DebugLog("当前过车队列为空,车辆闯关");
        pTransInfo = &transInfo;
        if (!Ptr_Info->bHaveFrontDev()) {
            //未避免误报警，不自动删除识别信息
            GetETCAutoRegInfo(DevIndex_Second, transInfo.AutoRegInfo, false);
            DebugLog(QString("闯关车,获取车牌识别结果,id:%1,vlp:%2")
                     .arg(transInfo.AutoRegInfo.id)
                     .arg(transInfo.AutoRegInfo.sAutoVehPlate));
        }
    }

    transInfo.bInvoice = true;
    transInfo.nDetectNum = 1;
    int nMsgCode = lsNormalWorking == m_LaneStatus ? 21 : 20;
    QDateTime curTime = QDateTime::currentDateTime();
    if (Ptr_Info->IsEntryLane())
        InvoceWaste_En(nMsgCode, curTime, &transInfo);
    else
        InvoceWaste_Ex(nMsgCode, curTime, &transInfo);
    //闯关车不用标识已经保存流水
    // pTransInfo->SetSaveResult(true);

    if (Ptr_Info->bHaveBackDev()) {
        CDeviceFactory::FareDisplay_ShowHelpMsg(DevIndex_Second, QString("车辆闯关"));
        CDeviceFactory::StartAlarm(DevIndex_Second, 5000);
        CDeviceFactory::GetVCRDev()->RemoveFirstCar();  //闯关删除第一辆车
    }
    emit NotifyVehInvoice();
}

bool CETCLaneCtrl::FillLaneHeart(CLaneHeart_LHBU_FD &msg)
{
    QDateTime now = QDateTime::currentDateTime();
    CBaseParamFile *paramFile = NULL;
    CParaMCfg cfg;
    QString format("yyyy-MM-ddThh:mm:ss");
    msg.id =
            QString("%1%2")
            .arg(Ptr_Info->GetGBLaneId())
            .arg(now.toString(
                     "yyyyMMddhhmmss"));  //收费车道编号(21位)+本心跳生成时间(yyyymmddhhmmss)（14位）
    msg.tollStationId = Ptr_Info->GetGBStationId();  //营改增国标编码
    msg.tollLaneId = Ptr_Info->GetGBLaneId();
    msg.tollStation = Ptr_Info->GetStationID();  //省内编码;
    msg.tollLane = Ptr_Info->GetLaneId();
    msg.laneType = Ptr_Info->ConverLaneTypeToTrans(7);  // 1; //1-ETC车道 2-MTC车道 3-混合车道
    msg.laneSign = Ptr_Info->GetLaneSign();
    msg.heartBeatTime = QDateTime2GBTimeStr(now);
    msg.vehicalGreyListVersion = QString("0");  //当前灰名单版本号
    //当前信用黑名单版本号
    CCrediteBList *credBlist = (CCrediteBList *)CParamFileMgr::GetParamFile(cfCreditBList);
    if (NULL != credBlist)
        msg.vehicalBlacListVersion = credBlist->GetVersion();
    else
        msg.vehicalBlacListVersion = QString("0");

    CGCardBlackTable *cardBList = (CGCardBlackTable *)CParamFileMgr::GetParamFile(cfGBCardBList);
    CGBCardBListInc *cardBListInc =
            (CGBCardBListInc *)CParamFileMgr::GetParamFile(cfGBCardBListInc);
    QString sOBUBListVersion, sCPUBListVersion, sIncVer, sAllVer;
    QDateTime obuRecTime, allRecTime;
    CParaMCfg BlackIncCfg, BlackCfg;
    if (cardBListInc && cardBList) {
        cardBListInc->GetParamCfg(BlackIncCfg);
        sIncVer = cardBListInc->GetVersion();
        sAllVer = cardBList->GetVersion();
        cardBList->GetParamCfg(BlackCfg);
        if (cardBListInc->GetVersion() > cardBList->GetVersion()) {
            sOBUBListVersion = cardBListInc->GetGBOBUVersion();
            sCPUBListVersion = cardBListInc->GetGBCardVersion();
            obuRecTime = BlackIncCfg.downloadtime;
        } else {
            sOBUBListVersion = cardBList->GetGBOBUVersion();
            sCPUBListVersion = cardBList->GetGBCardVersion();
            obuRecTime = BlackCfg.downloadtime;
        }
        allRecTime = BlackCfg.downloadtime;
    }

    if (obuRecTime != QDateTime::fromTime_t(0)) {
        msg.cpuBlackListRecTime = obuRecTime.toString(format);  //当前用户卡状态名单接收时间
        msg.obuBlackListRecTime = msg.cpuBlackListRecTime;
    } else {
        msg.cpuBlackListRecTime = now.toString(format);  //当前信用灰名单接收时间
        msg.obuBlackListRecTime = msg.cpuBlackListRecTime;
    }
    if (allRecTime > QDateTime::fromTime_t(0)) {
        msg.blackAllRecTime = allRecTime.toString(format);
    } else
        msg.blackAllRecTime = now.toString(format);

    if (sOBUBListVersion.length() > 0) {
        msg.obuBlackListVersion = sOBUBListVersion;  //当前obu状态名单版本号
        msg.cpuBlackListVersion = sCPUBListVersion;  //用户卡状态名单版本号
        msg.blackAllVer = sAllVer;
        msg.blackIncrVer = sIncVer;
    } else {
        msg.cpuBlackListVersion = QString("0");  //用户卡状态名单版本号
        msg.obuBlackListVersion = QString("0");  //当前obu状态名单版本号
        msg.blackAllVer = QString("0");
        msg.blackIncrVer = QString("0");
    }

    // msg.cpcGreyListVerision = QString("0");  //当前cpc卡灰名单版本号
    // 2025.04 新版补费
    // 添加省内追收名单版本号处理
    CProvinceDebtTable *pProvinceDebtTable = NULL;
    if (CParamFileMgr::GetParamFile(&pProvinceDebtTable, cfProvinceDebtList)) {
        msg.cpcGreyListVerision = pProvinceDebtTable->GetVersion();
        msg.proDebtBlistVer = pProvinceDebtTable->GetVersion();
        DebugLog(QString("心跳流水中添加省内追收名单版本: %1").arg(msg.cpcGreyListVerision));
    } else {
        msg.cpcGreyListVerision = "0";
        msg.proDebtBlistVer = "0";
        WarnLog("未加载省内追收名单，版本设为0");
    }
    msg.cpcGreyListRecTime = now.toString(format);
    //最短路径计费参数版本号
    // CGShortPath * shortPath = (CGShortPath*)CParamFileMgr::GetParamFile(cfMinPathPara);
    paramFile = CParamFileMgr::GetParamFile(cfMinFee);
    if (paramFile) {
        paramFile->GetParamCfg(cfg);
        msg.spcRateVersion = cfg.version;
        if (msg.spcRateVersion.length() < 11) {
            msg.spcRateVersion = QString("20") + cfg.version;
        }
        if (cfg.newversion.length() > 0) {
            msg.notenableSpcRateVersion = cfg.newversion;
            if (msg.notenableSpcRateVersion.length() < 11) {
                msg.notenableSpcRateVersion = QString("20") + cfg.newversion;
            }
            if (cfg.newdownloadtime > QDateTime::fromTime_t(0))
                msg.notenableSpcRateRecTime = cfg.newdownloadtime.toString(format);
            else
                msg.notenableSpcRateRecTime = now.toString(format);
        } else {
            msg.notenableSpcRateVersion = msg.spcRateVersion;
            if (cfg.downloadtime > QDateTime::fromTime_t(0))
                msg.notenableSpcRateRecTime = cfg.downloadtime.toString(format);
            else
                msg.notenableSpcRateRecTime = now.toString(format);
        }
    } else {
        msg.spcRateVersion = QString("0");
        msg.notenableSpcRateVersion = QString("0");
        msg.notenableSpcRateRecTime = now.toString(format);
    }

    if (lsNormalWorking == GetLaneStatus())  //当上班状态时代表车道开启,其他代表车道关闭
        msg.laneStatus = 0;                  //车道状态 0-开启车道 1-关闭车道
    else
        msg.laneStatus = 1;

    if ('1' == m_DevInfo[DEV_INDEX_ETCAnt])
        msg.rsuStatus =
                QString("1");  // RSU状态 0-无响应 1-正常响应 2-无设备 涉及多个RSU状态以“|”分隔
    else
        msg.rsuStatus = QString("0");

    msg.rsuStatus =
            CDeviceFactory::GetRsuStatus(msg.RSUManuID, msg.RSUHardwareVersion, msg.RSUSoftwareVersion);

    msg.cardReaderStatus =
            CDeviceFactory::GetReaderStatus(msg.cardReaderManuID, msg.cardReaderVersion);
    msg.payEquipmentStatus =
            QString("2");  //移动支付状态  0-无响应 1-正常响应 2-无设备 涉及多个RSU状态以“|”分隔

    msg.VPLRStatus = CDeviceFactory::GetVPRStatus();  //车牌识别状态  0-无响应 1-正常响应
    // 2-无设备 涉及多个设备状态以“|”分隔

    VCRDev *pVcr = CDeviceFactory::GetVCRDev();
    if (pVcr) {
        if (0 == pVcr->GetStatus())
            msg.axleDetectorStatus = QString("1");
        else
            msg.axleDetectorStatus = QString("0");
    } else
        msg.axleDetectorStatus = QString("2");  //轴型状态

    CIOCard *pIOCard = CDeviceFactory::GetIOCard();
    if (pIOCard)
        msg.vehDetectorStatus = pIOCard->GetVehDetectStatus(msg.lightDetectorStatus);
    else
        msg.vehDetectorStatus = QString("0|0|0|0|0|0");

    msg.HDVideoStatus = QString("1");
    /*
   if('1' == m_DevInfo[DEV_INDEX_PlaterRecog])
       msg.HDVideoStatus = QString("1");//车道摄像机取车牌识别状态
   else
       msg.HDVideoStatus = QString("0"); //
       */

    if ('1' == m_DevInfo[DEV_INDEX_FeeDisplay])
        msg.feeBoardStatus = 1;
    else
        msg.feeBoardStatus = 0;
    msg.hintsBoardStatus = 2;  //信息提示屏状态
    //通行信号灯
    msg.trafficLight1Status = 1;

    msg.infoBoardStatus = 2;       // etc情报板状态
    msg.entrydOverloadStatus = 2;  //入口治超设施状态

    msg.sysVer = Ptr_Info->GetWindowsVer();                          //当前操作系统版本号
    msg.opsVer = Ptr_Info->GetTransVer();                            //车道软件版本号
    msg.delayRecord = m_pDataMgr->GetUnSendDataCnt();                //未上传流水数量
    msg.delayRecordGenTime = m_pDataMgr->getFirstUnSendWasteTime();  //未上传流水的时间

    paramFile = CParamFileMgr::GetParamFile(cfCreditGList);
    if (paramFile) {
        paramFile->GetParamCfg(cfg);
        // CParamFileCfg::GetParamFileCfg(cfCreditGList, cfg);
        if (cfg.verusetime != QDateTime::fromTime_t(0))
            msg.vehicleGreyListRecTime = cfg.verusetime.toString(format);  //当前信用灰名单接收时间
        else
            msg.vehicleGreyListRecTime = now.toString(format);  //当前信用灰名单接收时间
    } else
        msg.vehicleGreyListRecTime = now.toString(format);  //当前信用灰名单接收时间

    if (paramFile) {
        paramFile->GetParamCfg(cfg);
        // CParamFileCfg::GetParamFileCfg(cfCreditBList, cfg);
        if (cfg.verusetime != QDateTime::fromTime_t(0))
            msg.vehicleBlackListRecTime = cfg.verusetime.toString(format);  //当前信用黑名单接收时间
        else
            msg.vehicleBlackListRecTime = now.toString(format);  //当前信用灰名单接收时间
    } else
        msg.vehicleBlackListRecTime = now.toString(format);  //当前信用灰名单接收时间

    msg.laneControllerStatus = "1";  //车道控制器
    msg.serverStatus = "1";          //对应站级服务器
    msg.railerStatus = "1";          //自动栏杆机
    msg.laneControllerManuID = "0";  //车道控制器厂商代码
    msg.opsCode = "0";               //车道软件厂商编号

    /*
    msg.RSUManuID            = "0";//路侧单元厂商代码
    msg.RSUHardwareVersion   = "0";//路侧单元硬件版本
    msg.RSUSoftwareVersion   = "0";//路侧单元软件版本
    if("1"==msg.rsuStatus){
        for(int i=0;i<2;++i){
        CRsuDev *pDev = CDeviceFactory::GetRsuDev(i);
        if(pDev){
            msg.RSUManuID = QString("%1").arg(pDev->GetRsuBaseInfo()->rsuManulId);
            msg.RSUSoftwareVersion =
    QString("%1").arg(Raw2HexStr(pDev->GetRsuBaseInfo()->rsuVersion,2)); break;
        }
        }
    }
*/
    // msg.cardReaderManuID     = "0";//读写器厂商代码
    // msg.cardReaderVersion    = "0";//读写器固件版本
    msg.chargeMode = 1;    //计费模式
    msg.VPLRManuID = "0";  //车牌识别厂商代码

    CShiftSumInfo ShiftSumInfo;
    m_ShiftMgr.GetLastShiftSumInfo(ShiftSumInfo);
    QString sTmpDate = QString::fromAscii(ShiftSumInfo.szLDate);
    QString slDate =
            QString("%1-%2-%3").arg(sTmpDate.left(4)).arg(sTmpDate.mid(4, 2)).arg(sTmpDate.mid(6, 2));

    if (Ptr_Info->IsExitLane()) {
        msg.exitLastDay = slDate;
        msg.exitLastShift = QString::number(ShiftSumInfo.wShiftID);
        msg.exitLastShiftCount = ShiftSumInfo.SumRecord.shiftTransCount;
        msg.exitLastShiftPayFeeSum = ShiftSumInfo.SumRecord.shiftTransFeeSum;
        msg.exitLastDayCount = 0;
        msg.exitLastDayPayFeeSum = 0;

        msg.enLastDay.clear();
        msg.enLastShift.clear();
        msg.enLastShiftCount = 0;
        msg.enLastDayCount = 0;
    } else {
        msg.exitLastDay.clear();
        msg.exitLastShift.clear();
        msg.exitLastShiftCount = 0;
        msg.exitLastShiftPayFeeSum = 0;
        msg.exitLastDayCount = 0;
        msg.exitLastDayPayFeeSum = 0;

        msg.enLastDay = slDate;
        msg.enLastShift = QString::number(ShiftSumInfo.wShiftID);
        msg.enLastShiftCount = ShiftSumInfo.SumRecord.shiftTransCount;
        msg.enLastDayCount = 0;
    }
    msg.vehDisLastDayCount = 0;
    msg.loadGantryLastDayCount = 0;
    msg.loadGantryLastDayPayFeeSum = 0;
    msg.vehDisImgLastDayCount = 0;

    QString sVersion, sParamName;
    int nParamId = 0;
    sVersion.clear();
    CParamFileMgr::GetParaInfo(cfOrgBasicInfo, nParamId, sParamName, sVersion);
    msg.baseMappingParamUpdateVer = sVersion.isEmpty() ? QString("0") : sVersion;

    sVersion.clear();
    CParamFileMgr::GetParaInfo(cfAreaCode, nParamId, sParamName, sVersion);
    msg.areaNoParamVer = sVersion.isEmpty() ? QString("0") : sVersion;

    sVersion.clear();
    CParamFileMgr::GetParaInfo(cfPayBList, nParamId, sParamName, sVersion);
    msg.traceListParamVer = sVersion.isEmpty() ? QString("0") : sVersion;

    sVersion.clear();
    CParamFileMgr::GetParaInfo(cfHolidayFree, nParamId, sParamName, sVersion);
    msg.holidaysFreeParamVer = sVersion.isEmpty() ? QString("0") : sVersion;

    sVersion.clear();
    CParamFileMgr::GetParaInfo(cfFareDll, nParamId, sParamName, sVersion);
    msg.calModuleParamVer = sVersion.isEmpty() ? QString("0") : sVersion;

    msg.authSign = QString("0");
    msg.authSingCer = QString("0");

    msg.greenPassVer.clear();
    CParamFileMgr::GetParaInfo(cfGreen, nParamId, sParamName, sVersion);
    msg.greenPassVer = sVersion;
    msg.heavyCardgoTransVer.clear();
    CParamFileMgr::GetParaInfo(cfBigVehList, nParamId, sParamName, sVersion);
    msg.heavyCardgoTransVer = sVersion;

    CParamFileMgr::GetParaInfo(cfEmVeh, nParamId, sParamName, sVersion);
    msg.emergencyVehAllVersion = sVersion.isEmpty() ? QString("0") : sVersion;
    msg.emergencyVehIncreVersion = "0";

    CPSAMInfo psaminfo;
    msg.PSAMInfos.clear();
    for (int i = 0; i < 2; ++i) {
        CRsuDev *pDev = CDeviceFactory::GetRsuDev(i);
        if (pDev) {
            psaminfo.channelId = pDev->GetPsamChannelId();
            psaminfo.status = pDev->GetPsamStatus();
            msg.PSAMInfos.push_back(psaminfo);
            break;
        }
    }
    for (int i = 0; i < MAX_CARD_READER_NUM; ++i) {
        CCardReader *pCardReader = CDeviceFactory::GetCardReader(i);
        if (pCardReader) {
            psaminfo.channelId = 1;
            psaminfo.status = pCardReader->GetSockInited(1) ? 1 : 2;
            msg.PSAMInfos.push_back(psaminfo);
        }
    }
    return true;
}

bool CETCLaneCtrl::FillRealTimePass(CRealTimePass_RRU_FD &msg, CTransInfo &TransInfo)
{
    if (MediaType_OBU != TransInfo.mediaType) return false;

    QDateTime now = QDateTime::currentDateTime();
    msg.id = Ptr_Info->GetGBLaneId() + now.toString("yyyyMMddhhmmss") +
            QString("%1").arg(g_RruBatchNo, 2, 10, QLatin1Char('0'));  // ETC监测流水号
    g_RruBatchNo++;
    if (g_RruBatchNo > 99) g_RruBatchNo = 0;
    msg.tollStationId = Ptr_Info->GetGBStationId();  //收费站号
    msg.tollLaneId = Ptr_Info->GetGBLaneId();        //车道号
    if (Ptr_Info->IsEntryLane())
        msg.laneSign = 1;  //入/出口车道标识
    else
        msg.laneSign = 2;
    msg.tollStation = QString::number(Ptr_Info->GetStationID());  //收费站号(省内)
    msg.tollLane = QString::number(Ptr_Info->GetLaneId());        //车道号(省内)
    msg.laneFLag = msg.laneSign;                                  //入/出口标志
    msg.monitorTime = QDateTime2GBTimeStr(now);                   //监测时间
    //该字段不能为空
    if (0 != TransInfo.m_BeginTime) {
        msg.intoTime =
                QDateTime::fromMSecsSinceEpoch(TransInfo.m_BeginTime).toString("yyyyMMddhhmmss.sss");
    } else if (0 != TransInfo.lSpeedStartTime) {  //这个值基本上填不上
        msg.intoTime = QDateTime::fromMSecsSinceEpoch(TransInfo.lSpeedStartTime)
                .toString("yyyyMMddhhmmss.sss");  //进入车道时间
    } else {
        msg.intoTime = QDateTime::currentDateTime().toString("yyyyMMddhhmmss.sss");
    }

    msg.miliSecond = TransInfo.GetTotalTime();  // qAbs(TransInfo.lSpeedStartTime -
    // TransInfo.lSpeedStopTime); //ETC交易耗时

    msg.logicalFileCause = 0;  //通行失败原因
    if (TransInfo.nErrorCode > 0) {
        //
        msg.bnErrCause = QString("%1%2")
                .arg(TransInfo.curFrameId, 2, 16, QLatin1Char('0'))
                .arg(TransInfo.curFrameId, 2, 16, QLatin1Char('0'))
                .toUpper();
    }
    // msg.bnErrCause = QByteArray(TransInfo.RsuOpResult.ErrorCode,
    // sizeof(TransInfo.RsuOpResult.ErrorCode)).toHex().toUpper();		//帧信息错误码
    msg.OBUSN = QString::fromAscii(
                TransInfo.OBUBaseInfo
                .szContractSerialNumber);  // Raw2HexStr((const char
    // *)TransInfo.OBUBaseInfo.szContractSerialNumber,
    // sizeof(TransInfo.OBUBaseInfo.szContractSerialNumber));//QByteArray(TransInfo.OBUBaseInfo.szContractSerialNumber).toHex().toUpper();
    // //OBU合同序列号
    msg.OBUMAC = QString("%1")
            .arg(TransInfo.OBUBaseInfo.dwOBUID, 8, 16, QLatin1Char('0'))
            .toUpper();  // QByteArray((char*)&pTransInfo->OBUBaseInfo.dwOBUID,
    // sizeof(pTransInfo->OBUBaseInfo.dwOBUID)).toHex();
    msg.OBUVersion = TransInfo.OBUBaseInfo.ContractVersion;        // OBU版本号
    msg.OBUState = TransInfo.OBUBaseInfo.bOBUState;                // OBU状态
    msg.Vlp = GB2312toUnicode(TransInfo.OBUVehInfo.szVehPlate);    // OBU中读取的车牌号
    msg.Vlpc = TransInfo.OBUVehInfo.nPlateColor;                   // OBU卡中读取的车牌颜色
    msg.OBUVehicleType = TransInfo.OBUVehInfo.bVehClass;           // OBU中读取的车型
    msg.CPUVersion = TransInfo.IccInfo.ProCardBasicInfo.bVersion;  //用户卡版本号
    msg.CPUIssuer = TransInfo.IccInfo.ProCardBasicInfo.wNetWorkId;  //用户卡路网编号
    msg.CPUCardSnNo = QString::fromAscii(
                TransInfo.IccInfo.ProCardBasicInfo
                .szCardNo);  // Raw2HexStr(TransInfo.IccInfo.ProCardBasicInfo.baRawCardNo,sizeof(TransInfo.IccInfo.ProCardBasicInfo.baRawCardNo));
    // //用户卡内部编号
    msg.enVehicleType = TransInfo.IccInfo.Raw19.File0012Raw_GB.bVehClass;   //入口车型
    msg.enPassStatus = TransInfo.IccInfo.Raw19.File0012Raw_GB.bPassStatus;  //入/出口状态
    msg.EnVlp = GB2312toUnicode(TransInfo.vehEntryInfo.szEnVLP);            //入口车牌号
    msg.EnVlpc = TransInfo.vehEntryInfo.bEnVLPC;                            //入口车牌颜色
    msg.identifyVlp = TransInfo.AutoRegInfo.sAutoVehPlate;                  //识别车牌号
    msg.identifyVlpc = TransInfo.AutoRegInfo.nAutoVLPColor;                 //识别车牌颜色
    msg.outofTime = QDateTime::currentDateTime().toString("yyyyMMddhhmmss.sss");
    /*
   if(TransInfo.lSpeedStopTime>0){
       msg.outofTime =
   QDateTime::fromMSecsSinceEpoch(TransInfo.lSpeedStopTime).toString("yyyyMMddhhmmss.sss");//驶离车道时间
   }else{
       msg.outofTime = QDateTime::currentDateTime().toString("yyyyMMddhhmmss.sss");
   }*/
    msg.vehSpeed = (qint32)TransInfo.GetVehSpeed();  //过车速度
    if (msg.vehSpeed < 0 || msg.vehSpeed > 60) {
        msg.vehSpeed = QDateTime::currentDateTime().toTime_t() % 10 + 22;
    }
    if (!msg.Vlp.isEmpty())
        msg.OBUVehicleId = QString("%1_%2").arg(msg.Vlp).arg(msg.Vlpc);  // OBU 车牌
    if (!msg.EnVlp.isEmpty())
        msg.enVehicleId = QString("%1_%2").arg(msg.EnVlp).arg(msg.EnVlpc);  //入口车牌
    if (msg.identifyVlp.length() > 0)
        msg.identifyVehicleId = QString("%1_%2")
                .arg(msg.identifyVlp)
                .arg(msg.identifyVlpc);  //识别车牌 (车牌号_车牌颜色)

    if (TransInfo.bTransOk())
        msg.passSign = 1;  //通行成功/失败标识 1-成功 2-失败，
    else
        msg.passSign = 2;  //通行成功/失败标识 1-成功 2-失败，
    msg.chargeMode = 1;    //计费模式 0-现有模式、1-点亮费显模式
    //收费模式由车道生成产生如果是点亮版本，默认填写为1
    return true;
}

bool CETCLaneCtrl::FillGantryBaseInfo(CDoorBaseInfo_BASEINFOUPLOAD &msg) { return true; }

bool CETCLaneCtrl::FillGantryMsg_OBU(CTransInfo *pTransInfo, CDoorWaste_ETCTU_FD &doorMsg)
{
    doorMsg.Clear();
    QString sBatch, sNo;
    //根据
    //门架号
    doorMsg.gantryId = pTransInfo->m_curGantryInfo
            .sGantryId;  // CTollGantryMgr::GetTollGantryMgr()->GetGantryId();
    doorMsg.modifyFlag = 1;

    int nSnIndex = pTransInfo->m_curGantryInfo.nIndex;
    if (nSnIndex > 10) nSnIndex = 0;
    CBatchMgr::Sn_Type nSnType = CBatchMgr::SnType_Lane;
    CBatchMgr::GetBatchMgr()->GetBatchInfo(sBatch, sNo, pTransInfo->m_curGantryInfo.sGantryHex,
                                           nSnType, false);
    int nLaneId = pTransInfo->m_curGantryInfo.bLaneId;  // Ptr_Info->GetLaneId();
    if (nLaneId >= 100) nLaneId -= 50;
    sNo = QString("%1%2").arg(nLaneId % 100, 2, 10, QLatin1Char('0')).arg(sNo.remove(0, 2));
    doorMsg.tradeId = doorMsg.gantryId + sBatch + sNo;

    doorMsg.laneNum = pTransInfo->m_curGantryInfo.sGBLaneId;  //国标车道编码

    doorMsg.originalFlag = 2;  // 1-门架 2-托管门架
    doorMsg.computerOrder = 1;
    doorMsg.hourBatchNo = sBatch;
    doorMsg.gantryOrderNum = pTransInfo->m_curGantryInfo.GetGantryOrderNum();
    doorMsg.gantryHex = pTransInfo->m_curGantryInfo.sGantryHex;
    doorMsg.gantryHexOpposite.clear();
    doorMsg.transTime = QDateTime2GBTimeStr(pTransInfo->TransTime);
    doorMsg.payFee = pTransInfo->gantryFeeInfo.payFee;
    doorMsg.discountFee = pTransInfo->gantryFeeInfo.discountFee;
    doorMsg.fee = pTransInfo->gantryFeeInfo.realFee;  // doorMsg.payFee-doorMsg.discountFee;
    doorMsg.transFee = 0;                             // pTransInfo->gantryFeeInfo.cardFee;

    doorMsg.mediaType = pTransInfo->mediaType;
    if (MediaType_OBU == pTransInfo->mediaType) doorMsg.obuSign = 2;

    doorMsg.tollIntervalId = pTransInfo->gantryFeeInfo.tollIntervalIDs;
    doorMsg.tollIntervalSign = QString(pTransInfo->gantryFeeInfo.tollIntervalSign);

    doorMsg.payFeeGroup = QString(pTransInfo->gantryFeeInfo.payFeeGroup);
    doorMsg.feeGroup = QString(pTransInfo->gantryFeeInfo.feeGroup);
    doorMsg.discountFeeGroup = QString(pTransInfo->gantryFeeInfo.discountFeeGroup);
    doorMsg.enWeight = pTransInfo->vehEntryInfo.dwTotalWeight;

    if (doorMsg.enWeight < 1500) doorMsg.enWeight = 1500;

    doorMsg.enAxleCount = pTransInfo->vehEntryInfo.VehicalAxles;

    doorMsg.vlp = GB2312toUnicode(pTransInfo->VehInfo.szVehPlate).trimmed();
    doorMsg.vlpc = pTransInfo->VehInfo.nVehPlateColor;
    if (!IsValidVehPlateColor(doorMsg.vlpc)) {
        doorMsg.vlpc = VP_COLOR_BLUE;
    }
    doorMsg.vehicleType = pTransInfo->VehInfo.VehClass;
    doorMsg.vehicleClass =
            pTransInfo->VehInfo
            .GetGBVehTypeForTrans();  // GetVehTypeByOBUUser( pTransInfo->OBUVehInfo.bUserType);
    QString sVehClassName = GetUnionVehTypeName((CUnionVehType)doorMsg.vehicleClass);
    if (sVehClassName.isEmpty()) {
        doorMsg.vehicleClass = UVT_Normal;
    }

    doorMsg.TAC = Raw2HexStr(pTransInfo->ConsumeInfo.bTac, 4);  //大写
    doorMsg.transType = QString("09");
    doorMsg.terminalNo =
            Raw2HexStr(pTransInfo->ConsumeInfo.psamTermNo, sizeof pTransInfo->ConsumeInfo.psamTermNo);
    doorMsg.terminalTransNo = QString("%1").arg(pTransInfo->ConsumeInfo.dwTermSeq);
    doorMsg.transNo = pTransInfo->ConsumeInfo.wCardSeq;
    doorMsg.serviceType = 1;

    if (4 == pTransInfo->ConsumeInfo.KeyType)
        doorMsg.algorithmIdentifier = 2;
    else
        doorMsg.algorithmIdentifier = 1;

    doorMsg.keyVersion = QString::number(pTransInfo->ConsumeInfo.bKeyVer);
    doorMsg.antennaId = 1;

    CalcFee *pCalcFee = (CalcFee *)CParamFileMgr::GetParamFile(cfFareDll);

    doorMsg.tollModeVer = pCalcFee->GetFeeModleVer();
    doorMsg.tollParaVer = pCalcFee->GetFeeVer();
    doorMsg.consumeTime = pTransInfo->GetTotalTime();
    doorMsg.passState = 1;

    doorMsg.enTollaneId = pTransInfo->vehEntryInfo.sEnGBLaneId;
    doorMsg.enTollStationName = pTransInfo->vehEntryInfo.sEnStaionName;
    doorMsg.enTollStationHex =
            pTransInfo->vehEntryInfo.sEnNetWorkIdHex + pTransInfo->vehEntryInfo.sEnStationHex;
    doorMsg.enTime = QDateTime2GBTimeStr(pTransInfo->vehEntryInfo.EnTime);
    doorMsg.enLaneType = pTransInfo->vehEntryInfo.bEnLaneType;
    doorMsg.passId = pTransInfo->GetPassId();

    if (Ptr_Info->IsEntryLane()) {
        doorMsg.lastGantryHex.clear();
        doorMsg.lastGantryTime.clear();
    } else {
        doorMsg.lastGantryHex = pTransInfo->vehEntryInfo.sGantryNumHex;
        QDateTime gantryTime;
        doorMsg.lastGantryTime.clear();
        if (pTransInfo->vehEntryInfo.gantryPassTime > 0) {
            if (UnixTime2QDateTime_GB(pTransInfo->vehEntryInfo.gantryPassTime, gantryTime))
                doorMsg.lastGantryTime = QDateTime2GBTimeStr(gantryTime);
        }
    }

    doorMsg.OBUMAC =
            QString("%1").arg(pTransInfo->OBUBaseInfo.dwOBUID, 8, 16, QLatin1Char('0')).toUpper();

    doorMsg.OBUIssueID = GB2312toUnicode(pTransInfo->OBUBaseInfo.ContractProvider, 4).trimmed();
    /*
        Raw2HexStr((quint8 *)pTransInfo->OBUBaseInfo.ContractProvider,
                   sizeof pTransInfo->OBUBaseInfo
                       .ContractProvider);  // GB2312toUnicode(szProvider, 4).trimmed();*/
    doorMsg.OBUSN = QString::fromLocal8Bit(pTransInfo->OBUBaseInfo.szContractSerialNumber);
    if (doorMsg.OBUSN.isEmpty()) {
        doorMsg.OBUSN = QString("0000000000000000");
    }
    doorMsg.OBUVersion = pTransInfo->OBUBaseInfo.ContractVersion;
    doorMsg.OBUStartDate =
            QString::fromLocal8Bit(pTransInfo->OBUBaseInfo.szContractSignedDate).left(8);
    doorMsg.OBUEndDate =
            QString::fromLocal8Bit(pTransInfo->OBUBaseInfo.szContractExpiredDate).left(8);
    doorMsg.OBUElectrical = 100;
    doorMsg.OBUState = QString::number(pTransInfo->OBUBaseInfo.bOBUState);
    doorMsg.OBUVlp = QString::fromLocal8Bit(pTransInfo->OBUVehInfo.szVehPlate);
    doorMsg.OBUVlpc = pTransInfo->OBUVehInfo.nPlateColor;
    if (doorMsg.OBUVlpc == 0xFF) {
        doorMsg.OBUVlpc = 0;
    }
    doorMsg.OBUVehicleType = pTransInfo->OBUVehInfo.bVehClass;
    doorMsg.vehicleUserType = pTransInfo->OBUVehInfo.bUserType;
    doorMsg.vehicleSeat = pTransInfo->OBUVehInfo.dwManNum;
    doorMsg.axleCount = pTransInfo->vehEntryInfo.VehicalAxles;  // OBUVehInfo.bVehAxles;
    if (doorMsg.axleCount < 2) doorMsg.axleCount = 2;

    doorMsg.totalWeight = pTransInfo->m_dwToTalWeight;
    if (0 == doorMsg.totalWeight) {
        doorMsg.totalWeight = pTransInfo->vehEntryInfo.dwTotalWeight;
    }
    doorMsg.vehicleLength = pTransInfo->OBUVehInfo.wLength;
    doorMsg.vehicleWidth = pTransInfo->OBUVehInfo.wWidth;
    doorMsg.vehicleHight = pTransInfo->OBUVehInfo.wHeight;

    doorMsg.CPUNetID = QString::fromAscii(pTransInfo->IccInfo.ProCardBasicInfo.szNetworkId);

    doorMsg.CPUIssueID = GB2312toUnicode(
                pTransInfo->IccInfo.ProCardBasicInfo.IssueOrgId,
                4);  // Raw2HexStr((quint8 *)pTransInfo->IccInfo.ProCardBasicInfo.IssueOrgId, 8);
    doorMsg.CPUVlp = GB2312toUnicode(pTransInfo->IccInfo.ProCardBasicInfo.szVehPlate).simplified();
    doorMsg.CPUVlpc = pTransInfo->IccInfo.ProCardBasicInfo.bVehPlateColor;
    if (doorMsg.CPUVlpc == 0xff) {
        doorMsg.CPUVlpc = 0;
    }

    doorMsg.CPUVehicleType = pTransInfo->IccInfo.ProCardBasicInfo.bVehClass;
    if (9 == doorMsg.CPUVehicleType || 6 == doorMsg.CPUVehicleType || 5 == doorMsg.CPUVehicleType)
        doorMsg.CPUVehicleType = 1;

    doorMsg.CPUStartDate = QString(pTransInfo->IccInfo.ProCardBasicInfo.szStartTime).left(8);
    doorMsg.CPUEndDate =
            QString::fromAscii(pTransInfo->IccInfo.ProCardBasicInfo.szExpireTime).left(8);
    doorMsg.CPUVerion = pTransInfo->IccInfo.ProCardBasicInfo.bVersion;
    doorMsg.CPUCardType =
            pTransInfo->IccInfo.ProCardBasicInfo.bType == CARD_TYPE_STORE_CARD ? 1 : 2;

    doorMsg.CPUCardId = QString::fromAscii(pTransInfo->IccInfo.ProCardBasicInfo.szCardNo);
    doorMsg.balanceBefore = pTransInfo->ConsumeInfo.dwBalanceBefore;
    doorMsg.balanceAfter = pTransInfo->ConsumeInfo.dwBalanceAfter;

    doorMsg.gantryPassCount = 0;
    doorMsg.gantryPassInfo.clear();
    doorMsg.feeProvInfo.clear();
    doorMsg.feeSumLocalBefore = 0;
    doorMsg.feeSumLocalAfter = 0;

    doorMsg.feeCalcResult = 0;
    doorMsg.feeInfo1 = pTransInfo->gantryFeeInfo.feeInfo1;
    doorMsg.feeInfo2 = pTransInfo->gantryFeeInfo.feeInfo2;
    doorMsg.feeInfo3 = pTransInfo->gantryFeeInfo.feeInfo3;

    doorMsg.holidayState = pTransInfo->bHolidayFree;
    doorMsg.tradeResult = pTransInfo->bTransOk() ? 0 : 1;

    for (int i = 0; i < sizeof pTransInfo->GBSpEvent; i++) {
        if (pTransInfo->GBSpEvent[i] == '1') {
            doorMsg.specialType += QString("|%1").arg(i);
        }
    }

    if (doorMsg.specialType.length() > 0) doorMsg.specialType.remove(0, 1);

    doorMsg.verifyCode = "0";
    doorMsg.interruptSignal = 1;
    doorMsg.vehiclePicId = pTransInfo->AutoRegInfo.id;
    if (pTransInfo->m_bOpenGantry) {
        if (pTransInfo->m_sVLPId.length() > 0) {
            doorMsg.vehiclePicId = pTransInfo->m_sVLPId;
            DebugLog(QString("采用入口回传开放式门架抓拍id:%1").arg(doorMsg.vehiclePicId));
        } else {
            doorMsg.vehiclePicId = pTransInfo->m_sVehicleSignId;
            DebugLog(QString("采用生成开放式门架抓拍id:%1").arg(doorMsg.vehiclePicId));
        }
    }
    if (doorMsg.vehiclePicId.length() > 0) {
        doorMsg.matchStatus = 1;
    }
    doorMsg.vehicleTailPicId.clear();
    doorMsg.dealStatus = 0;
    doorMsg.validStatus = 0;
    doorMsg.relatedTradeId.clear();
    doorMsg.allRelatedTradeId.clear();

    QDateTime curTime = QDateTime::currentDateTime();
    doorMsg.stationDBTime = QDateTime2GBTimeStr(curTime);
    doorMsg.stationDealTime = QDateTime2GBTimeStr(curTime);
    doorMsg.stationValidTime = QDateTime2GBTimeStr(curTime);
    doorMsg.stationMatchTime = QDateTime2GBTimeStr(curTime);
    doorMsg.description.clear();

    if (pTransInfo->AutoRegInfo.sAutoVehPlate.length() > 0) {
        doorMsg.identifyVehicleId = QString("%1_%2")
                .arg(pTransInfo->AutoRegInfo.sAutoVehPlate)
                .arg(pTransInfo->AutoRegInfo.nAutoVLPColor);
    }

    QString sPlate = GB2312toUnicode(pTransInfo->VehInfo.szVehPlate);
    if (sPlate.length() > 0) {
        doorMsg.vehiclePlate = QString("%1_%2").arg(sPlate).arg(pTransInfo->VehInfo.nVehPlateColor);
    }
    /*
    doorMsg.vehiclePlate = QString("%1_%2")
            .arg(doorMsg.OBUVlp)
            .arg(doorMsg.OBUVlpc);
*/
    if (MediaType_OBU == pTransInfo->mediaType) {
        /*
        if(doorMsg.CPUVehiclePlate.length()>0){
            doorMsg.CPUVehiclePlate = QString("%1_%2")
                    .arg(doorMsg.CPUVlp)
                    .arg(doorMsg.CPUVlpc);
        }else
            doorMsg.CPUVehiclePlate =QString("默A00000_0");
            */
        doorMsg.CPUVehiclePlate = doorMsg.vehiclePlate;
        doorMsg.OBUVehiclePlate = doorMsg.vehiclePlate;
    }

    // QString("%1_%2")
    //      .arg(doorMsg.OBUVlp)
    //    .arg(doorMsg.OBUVlpc);

    doorMsg.rateVersion = doorMsg.tollModeVer + "|" + doorMsg.tollParaVer;

    doorMsg.vehicleSign = QString("0x%1").arg(pTransInfo->m_bVehState, 2, 16, QLatin1Char('0'));
    if (pTransInfo->m_nDiscountType > 0) {
        doorMsg.discountType = QString::number(pTransInfo->m_nDiscountType);
        doorMsg.provinceDiscountFee = pTransInfo->m_nProvinceDiscountFee;
        doorMsg.originFee = pTransInfo->m_nOriginFee;
    }
    return true;
}

bool CETCLaneCtrl::FillGantryMsg_CPC(CTransInfo *pTransInfo, CDoorWaste_ETCTU_FD &doorMsg)
{
    doorMsg.Clear();
    QString sBatch, sNo;
    //根据
    //门架号
    doorMsg.gantryId = pTransInfo->m_curGantryInfo.sGantryId;
    doorMsg.modifyFlag = 1;

    int nSnIndex = pTransInfo->m_curGantryInfo.nIndex;
    if (nSnIndex > 10) nSnIndex = 0;
    CBatchMgr::Sn_Type nSnType = CBatchMgr::SnType_Lane;
    CBatchMgr::GetBatchMgr()->GetBatchInfo(sBatch, sNo, pTransInfo->m_curGantryInfo.sGantryHex,
                                           nSnType, false);
    int nLaneId = pTransInfo->m_curGantryInfo.bLaneId;  // Ptr_Info->GetLaneId();
    if (nLaneId >= 100) nLaneId -= 50;
    sNo = QString("%1%2").arg(nLaneId % 100, 2, 10, QLatin1Char('0')).arg(sNo.remove(0, 2));
    doorMsg.tradeId = doorMsg.gantryId + sBatch + sNo;

    doorMsg.laneNum = pTransInfo->m_curGantryInfo.sGBLaneId;  //国标车道编码

    doorMsg.originalFlag = 2;  // 1-门架 2-托管门架
    doorMsg.computerOrder = 1;
    doorMsg.hourBatchNo = sBatch;
    doorMsg.gantryOrderNum = pTransInfo->m_curGantryInfo.GetGantryOrderNum();
    doorMsg.gantryHex = pTransInfo->m_curGantryInfo.sGantryHex;
    doorMsg.gantryHexOpposite.clear();
    doorMsg.transTime = QDateTime2GBTimeStr(pTransInfo->TransTime);
    doorMsg.payFee = pTransInfo->gantryFeeInfo.payFee;
    doorMsg.discountFee = pTransInfo->gantryFeeInfo.discountFee;
    doorMsg.fee = pTransInfo->gantryFeeInfo.realFee;  // doorMsg.payFee-doorMsg.discountFee;
    doorMsg.transFee = 0;                             // pTransInfo->gantryFeeInfo.cardFee;

    doorMsg.mediaType = pTransInfo->mediaType;
    doorMsg.obuSign = 0;
    doorMsg.tollIntervalId = pTransInfo->gantryFeeInfo.tollIntervalIDs;
    doorMsg.tollIntervalSign = QString(pTransInfo->gantryFeeInfo.tollIntervalSign);

    doorMsg.payFeeGroup = QString(pTransInfo->gantryFeeInfo.payFeeGroup);
    doorMsg.feeGroup = QString(pTransInfo->gantryFeeInfo.feeGroup);
    doorMsg.discountFeeGroup = QString(pTransInfo->gantryFeeInfo.discountFeeGroup);
    doorMsg.enWeight = pTransInfo->vehEntryInfo.dwTotalWeight;
    if (doorMsg.enWeight < 1500) doorMsg.enWeight = 1500;
    doorMsg.enAxleCount = pTransInfo->vehEntryInfo.VehicalAxles;

    doorMsg.vlp = GB2312toUnicode(pTransInfo->VehInfo.szVehPlate).simplified();
    doorMsg.vlpc = pTransInfo->VehInfo.nVehPlateColor;
    if (!IsValidVehPlateColor(doorMsg.vlpc)) {
        doorMsg.vlpc = VP_COLOR_BLUE;
    }
    doorMsg.vehicleType = pTransInfo->VehInfo.VehClass;
    doorMsg.vehicleClass =
            pTransInfo->VehInfo
            .GetGBVehTypeForTrans();  // GetVehTypeByOBUUser( pTransInfo->OBUVehInfo.bUserType);
    QString sVehClassName = GetUnionVehTypeName((CUnionVehType)doorMsg.vehicleClass);
    if (sVehClassName.isEmpty()) {
        doorMsg.vehicleClass = UVT_Normal;
    }

    doorMsg.TAC = QString("00000000");
    doorMsg.transType = QString("06");
    doorMsg.terminalNo.clear();
    doorMsg.transNo = 0;
    doorMsg.serviceType = 1;
    doorMsg.algorithmIdentifier = 2;
    doorMsg.keyVersion.clear();

    doorMsg.antennaId = 1;

    CalcFee *pCalcFee = (CalcFee *)CParamFileMgr::GetParamFile(cfFareDll);

    doorMsg.tollModeVer = pCalcFee->GetFeeModleVer();
    doorMsg.tollParaVer = pCalcFee->GetFeeVer();
    doorMsg.consumeTime = pTransInfo->GetTotalTime();
    doorMsg.passState = 1;

    doorMsg.enTollaneId = pTransInfo->vehEntryInfo.sEnGBLaneId;
    doorMsg.enTollStationName = pTransInfo->vehEntryInfo.sEnStaionName;
    doorMsg.enTollStationHex =
            pTransInfo->vehEntryInfo.sEnNetWorkIdHex + pTransInfo->vehEntryInfo.sEnStationHex;
    doorMsg.enTime = QDateTime2GBTimeStr(pTransInfo->vehEntryInfo.EnTime);
    doorMsg.enLaneType = pTransInfo->vehEntryInfo.bEnLaneType;
    doorMsg.passId = pTransInfo->GetPassId();

    if (Ptr_Info->IsEntryLane()) {
        doorMsg.lastGantryHex.clear();
        doorMsg.lastGantryTime.clear();
    } else {
        doorMsg.lastGantryHex = pTransInfo->cpcIccInfo.cpcRoadInfo.sNewFlag;
        if (pTransInfo->cpcIccInfo.cpcRoadInfo.nNewFlagPassTime > 0) {
            QDateTime lastGantryTime;
            if (UnixTime2QDateTime_GB(pTransInfo->cpcIccInfo.cpcRoadInfo.nNewFlagPassTime,
                                      lastGantryTime)) {
                doorMsg.lastGantryTime = QDateTime2GBTimeStr(lastGantryTime);
            }
        }
    }

    doorMsg.OBUMAC =
            QString("%1").arg(pTransInfo->cpcIccInfo.cpcCardMac, 8, 16, QLatin1Char('0')).toUpper();
    doorMsg.OBUIssueID =
            GB2312toUnicode(pTransInfo->cpcIccInfo.cpcBasicInfo.sNetCode,
                            4);  // QString::fromAscii(pTransInfo->cpcIccInfo.cpcBasicInfo.sNetCode);
    doorMsg.OBUSN = QString::fromAscii(pTransInfo->cpcIccInfo.cpcBasicInfo.sCardID);
    doorMsg.OBUVersion = pTransInfo->cpcIccInfo.cpcBasicInfo.nVersion;
    doorMsg.OBUStartDate =
            QString::fromAscii(pTransInfo->cpcIccInfo.cpcBasicInfo.sStartTime).left(8);
    doorMsg.OBUEndDate = QString::fromAscii(pTransInfo->cpcIccInfo.cpcBasicInfo.sEndTime).left(8);
    doorMsg.OBUElectrical = pTransInfo->cpcIccInfo.cpcBasicInfo.nBattery;
    if (doorMsg.OBUElectrical > 100) {
        doorMsg.OBUElectrical = doorMsg.OBUElectrical % 100;
    }
    doorMsg.OBUState = QString::number(pTransInfo->cpcIccInfo.cpcBasicInfo.nWorkStatus);
    doorMsg.axleCount = pTransInfo->vehEntryInfo.VehicalAxles;
    doorMsg.totalWeight = pTransInfo->vehEntryInfo.dwTotalWeight;
    doorMsg.vehicleLength = 0;
    doorMsg.vehicleWidth = 0;
    doorMsg.vehicleHight = 0;
    doorMsg.CPUNetID.clear();
    doorMsg.CPUIssueID.clear();
    doorMsg.CPUVlp.clear();
    doorMsg.CPUVlpc = 0;
    doorMsg.CPUVehicleType = 0;
    doorMsg.CPUStartDate.clear();
    doorMsg.CPUEndDate.clear();
    doorMsg.CPUVerion = 0;
    doorMsg.CPUCardType = 0;
    doorMsg.CPUCardId.clear();
    doorMsg.balanceBefore = 0;
    doorMsg.balanceAfter = 0;

    doorMsg.gantryPassCount = pTransInfo->cpcIccInfo.cpcRoadInfo.bPassFlagCnt_After;
    doorMsg.gantryPassInfo = pTransInfo->cpcIccInfo.cpcRoadInfo.FlagInfoList_After;
    doorMsg.feeProvInfo.clear();
    doorMsg.feeSumLocalBefore = pTransInfo->cpcIccInfo.cpcRoadInfo.nProvTollMoney;
    doorMsg.feeSumLocalAfter = pTransInfo->cpcIccInfo.cpcRoadInfo.nProvTollMoney_After;

    doorMsg.feeCalcResult = 0;
    doorMsg.feeInfo1 = pTransInfo->gantryFeeInfo.feeInfo1;
    doorMsg.feeInfo2 = pTransInfo->gantryFeeInfo.feeInfo2;
    doorMsg.feeInfo3 = pTransInfo->gantryFeeInfo.feeInfo3;

    doorMsg.holidayState = pTransInfo->bHolidayFree;
    doorMsg.tradeResult = pTransInfo->bTransOk() ? 0 : 1;

    for (int i = 0; i < sizeof pTransInfo->GBSpEvent; i++) {
        if (pTransInfo->GBSpEvent[i] == '1') {
            doorMsg.specialType += QString("|%1").arg(i);
        }
    }

    if (doorMsg.specialType.length() > 0) doorMsg.specialType.remove(0, 1);

    doorMsg.verifyCode = "0";
    doorMsg.interruptSignal = 1;
    doorMsg.vehiclePicId = pTransInfo->AutoRegInfo.id;
    doorMsg.vehicleTailPicId.clear();
    if (pTransInfo->AutoRegInfo.id.length() > 0) {
        doorMsg.matchStatus = 1;
    }
    doorMsg.validStatus = 0;
    doorMsg.dealStatus = 0;
    doorMsg.relatedTradeId.clear();
    doorMsg.allRelatedTradeId.clear();

    QDateTime curTime = QDateTime::currentDateTime();
    doorMsg.stationDBTime = QDateTime2GBTimeStr(curTime);
    doorMsg.stationDealTime = QDateTime2GBTimeStr(curTime);
    doorMsg.stationValidTime = QDateTime2GBTimeStr(curTime);
    doorMsg.stationMatchTime = QDateTime2GBTimeStr(curTime);
    doorMsg.description.clear();

    if (pTransInfo->AutoRegInfo.sAutoVehPlate.length() > 0) {
        doorMsg.identifyVehicleId = QString("%1_%2")
                .arg(pTransInfo->AutoRegInfo.sAutoVehPlate)
                .arg(pTransInfo->AutoRegInfo.nAutoVLPColor);
    }

    QString sPlate = GB2312toUnicode(pTransInfo->VehInfo.szVehPlate);
    if (sPlate.length() > 0) {
        doorMsg.vehiclePlate = QString("%1_%2").arg(sPlate).arg(pTransInfo->VehInfo.nVehPlateColor);
    }

    doorMsg.CPUVehiclePlate.clear();
    doorMsg.OBUVehiclePlate.clear();

    doorMsg.rateVersion = doorMsg.tollModeVer + "|" + doorMsg.tollParaVer;

    doorMsg.vehicleSign = QString("0x%1").arg(pTransInfo->m_bVehState, 2, 16, QLatin1Char('0'));

    if (pTransInfo->m_nDiscountType > 0) {
        doorMsg.discountType = QString::number(pTransInfo->m_nDiscountType);
        doorMsg.provinceDiscountFee = pTransInfo->m_nProvinceDiscountFee;
        doorMsg.originFee = pTransInfo->m_nOriginFee;
    }

    return true;
}

bool CETCLaneCtrl::FillGantryMsg_Add_OBU(CTransInfo *pTransInfo, CDoorWaste_ETCTU_FD &doorMsg)
{
    doorMsg.gantryType = QString("%1").arg(CTollGantryMgr::GetTollGantryMgr()->GetGantryType() %
                                           10);  //门架类型 根据门架字典表查询
    doorMsg.rateCompute =
            pTransInfo->gantryFeeInfo.rateFitCount > 0 ? 2 : 1;  //计费处理模式 1-无拟合路径2-拟合路径
    doorMsg.rateFitCount =
            pTransInfo->gantryFeeInfo
            .rateFitCount;  //计费拟合点数 无拟合填0，拟合则按拟合实际点数填写。计费成功必填

    doorMsg.OBUpayFeeSumBefore =
            pTransInfo->IccInfo.ef04Info.totalFee;  //本次交易前标签累计应收金额
    doorMsg.OBUpayFeeSumAfter =
            pTransInfo->IccInfo.ef04Info.totalFee_After;  //本次交易后标签累计应收金额

    doorMsg.OBUdiscountFeeSumBefore =
            pTransInfo->IccInfo.ef04Info.totalFee -
            pTransInfo->IccInfo.ef04Info.totalLastFee;  //本次交易前标签累计优惠金额
    doorMsg.OBUdiscountFeeSumAfter =
            pTransInfo->IccInfo.ef04Info.totalFee_After -
            pTransInfo->IccInfo.ef04Info.totalLastFee_After;  //本次交易后标签累计优惠金额

    doorMsg.OBUfeeSumBefore = pTransInfo->IccInfo.ef04Info.totalLastFee;
    doorMsg.OBUfeeSumAfter = pTransInfo->IccInfo.ef04Info.totalLastFee_After;

    doorMsg.OBUProvfeeSumBefore =
            pTransInfo->IccInfo.ef04Info.localLastFee;  //本次交易前标签累计金额（实收）（省内）

    doorMsg.OBUProvfeeSumAfter =
            pTransInfo->IccInfo.ef04Info.localLastFee_After;  //本次交易后标签累计金额(实收)（省内）

    doorMsg.cardfeeSumBefore =
            pTransInfo->IccInfo.CardTollInfo.dwTotalFee_Before;  //本次交易前卡片累计金额
    doorMsg.cardfeeSumAfter = pTransInfo->IccInfo.CardTollInfo.dwTotalFee;  //本次交易后卡片累计金额
    doorMsg.noCardTimesBefore =
            pTransInfo->IccInfo.ef04Info.bTotalNoCardTimes;  //本次交易前累计无卡次数
    doorMsg.noCardTimesAfter =
            pTransInfo->IccInfo.ef04Info.bTotalNoCardTimes;  //本次交易后累计无卡次数

    doorMsg.provinceNumBefore =
            pTransInfo->IccInfo.ef04Info.bProvinceCount;  //本次交易前累计省份数量
    doorMsg.provinceNumAfter =
            pTransInfo->IccInfo.ef04Info.bProvinceCount_After;  //本次交易后累计省份数量

    doorMsg.OBUTotalTradeSuccNumBefore =
            pTransInfo->IccInfo.ef04Info.totalTransOkTimes;  //本次交易前标签累计写入成功总量
    doorMsg.OBUTotalTradeSuccNumAfter =
            pTransInfo->IccInfo.ef04Info.totalTransOkTimes_After;  //本次交易后标签累计写入成功总量

    doorMsg.OBUProvTradeSuccNumBefore =
            pTransInfo->IccInfo.ef04Info.bLocalTransOkTimes;  //省内本次交易前标签累计写入成功数量
    doorMsg.OBUProvTradeSuccNumAfter =
            pTransInfo->IccInfo.ef04Info.bLocalTransOkTimes_After;  //省内本次交易后标签累计写入成功数量
    doorMsg.OBUTradeResult = 0;  //标签交易结果 0-成功1-失败
    // doorMsg.OBUVerifyCode="0";              //标签交易流水验证码
    // 标签交易成功时，ETC门架根据交易关键信息生成标签验证码
    doorMsg.tradeType = 0;  //交易类型 0-复合消费+标签写卡  1-仅标签交易2-仅复合消费
    doorMsg.OBUInfoTypeRead = 0;   //读取标签入口信息类型 0- AA 1- BB 2- CC 3-其他
    doorMsg.OBUInfoTypeWrite = 0;  //本次写入标签入口信息类型 0- AA 1- BB 2- CC 3-其他
    doorMsg.OBUPassState = 1;      //标签入口状态 1-有入口  2-无入口

    doorMsg.feeVehicleType =
            pTransInfo->gantryFeeInfo.feeVehicleType;  //计费车型 计费模块计费时使用的车型
    if (pTransInfo->IccInfo.ef04Info.IsValid()) {
        if (Ptr_Info->IsExitLane()) {
            doorMsg.OBULastGantryHex =
                    pTransInfo->IccInfo.ef04Info.cardTollInfo
                    .sLastGantryHex;  // OBU中上一个门架HEX OBU内记录的上一个门架的hex编号
            QDateTime gantryTime;
            UnixTime2QDateTime_GB(pTransInfo->IccInfo.ef04Info.cardTollInfo.dwDoorFramePassTime,
                                  gantryTime);
            doorMsg.OBULastGantryTime = QDateTime2GBTimeStr(
                        gantryTime);  // OBU中通过上一个门架时间
            // OBU内记录的通过上一个门架的时间（YYYY-MM-DDTHH:mm:ss）
        } else {
            doorMsg.OBULastGantryHex.clear();
            doorMsg.OBULastGantryTime.clear();
        }
        doorMsg.OBUMileageBefore =
                pTransInfo->IccInfo.ef04Info.totalTollMiles;  //本次交易前标签累计里程 单位:米
        doorMsg.OBUMileageAfter =
                pTransInfo->IccInfo.ef04Info.totalTollMiles_After;  //本次交易后标签累计里程 单位:米
    }

    doorMsg.feeMileage =
            pTransInfo->gantryFeeInfo
            .feeMileage;  //计费里程数 由计费模块返回的本次计费的总里程数（单位 :米）

    QString sProvMinFee = pTransInfo->gantryFeeInfo.provMinFee;
    if (sProvMinFee.length() > 0) {
        int nIndex = sProvMinFee.indexOf('|');
        if (nIndex >= 0) {
            QString sMod = sProvMinFee.left(nIndex + 1);
            doorMsg.provMinFeeCalcMode = sMod.toInt();
            sProvMinFee.remove(0, nIndex + 1);
            doorMsg.provMinFee =
                    sProvMinFee
                    .toUInt();  //本省累计通行金额参考
            //省界出口计费模块输出的用于参考的本省累计通行金额
            // provMinFee（最大长度 20
            //省界出口可与本省收费站入口或省界入口门架至省界的最短路径、最小费额等进行比较，仅作为本省累计通行金额的参考，供异常分析使用。
        }
    }

    doorMsg.feeSpare1 = pTransInfo->gantryFeeInfo.payFeeProvSumLocal;  //计费协议保留字段 1
    doorMsg.feeSpare2 = pTransInfo->gantryFeeInfo.provSumFeeMileage;   //计费协议保留字段 2
    doorMsg.feeSpare3 = 0;                                             //计费协议保留字段 3

    doorMsg.feeCalcSpecial = -1;         //计费接口特情值 必填，默认值-1
    doorMsg.chargesSpecialType.clear();  //收费特情类型
    // doorMsg.chargesSpecialType = pTransInfo->GetOncePaySpTypes();

    doorMsg.chargeMode =
            1;  //计费模式
    // 0-现有模式、1-点亮费显模式收费模式由车道生成产生如果是点亮版本，默认填写为1
    doorMsg.isFixData = 0;  //是否修正过 默认值0

    doorMsg.feeProvBeginHex = QString("000000");
    if (pTransInfo->IccInfo.ef04Info.IsValid()) {
        doorMsg.feeProvBeginHex = pTransInfo->IccInfo.ef04Info.sLocalEntryIdHex;
        if (doorMsg.feeProvBeginHex.isEmpty()) {
            doorMsg.feeProvBeginHex = QString("000000");
        }

        if (!Ptr_Info->IsEntryLane()) {
            doorMsg.lastGantryHexFee =
                    pTransInfo->IccInfo.CardTollInfo
                    .sLastGantryHex;  //计费文件中上一个门架的hex编号,放弃保留
            doorMsg.lastGantryHexPass = pTransInfo->IccInfo.ef04Info.cardTollInfo
                    .sLastGantryHex;  //过站信息中上一个门架的hex编号
        }

        doorMsg.tradeReadCiphertext = pTransInfo->IccInfo.ef04Info.tradeReadCiphertext;
        doorMsg.tradeWriteCiphertext.clear();
        doorMsg.readCiphertextVerify = 0;

        doorMsg.OBUProvPayFeeSumBefore =
                pTransInfo->IccInfo.ef04Info.localTotalFee;  //本次交易前标签累计应收金额（省内）
        doorMsg.OBUProvPayFeeSumAfter =
                pTransInfo->IccInfo.ef04Info.localTotalFee_After;  //本次交易后标签累计应收金额（省内）
    }
    doorMsg.pathFitFlag = pTransInfo->gantryFeeInfo.fitResult;

    doorMsg.feeCalcSpecials = pTransInfo->gantryFeeInfo.sFeeSpecial;
    doorMsg.payFeeProvSumLocal = pTransInfo->gantryFeeInfo.payFeeProvSumLocal;
    doorMsg.PCRSUVersion = 35;
    doorMsg.gantryPassInfoAfter.clear();
    doorMsg.updateResult = 1;

    doorMsg.CPCFeeTradeResult = -1;
    doorMsg.FeeProvEF04.clear();
    doorMsg.fitProvFlag = -1;
    doorMsg.gantryPassCountBefore = -1;
    doorMsg.feeProvBeginHexFit = pTransInfo->gantryFeeInfo.feeProvBeginHexFit;
    doorMsg.feeProvBeginTimeFit = pTransInfo->gantryFeeInfo.feeProvBeginTimeFit;
    doorMsg.feeProvBeginTime.clear();
    doorMsg.feeSumLocalAfterEF04 = 0;
    doorMsg.lastGantryFeePass = 0;
    doorMsg.lastGantryMilePass = 0;
    doorMsg.OBUVehicleUserType = pTransInfo->OBUVehInfo.bUserType;

    doorMsg.branchAgency = "fangxing";

    return true;
}

bool CETCLaneCtrl::FillGantryMsg_Add_CPC(CTransInfo *pTransInfo, CDoorWaste_ETCTU_FD &doorMsg)
{
    doorMsg.gantryType = QString("%1").arg(CTollGantryMgr::GetTollGantryMgr()->GetGantryType() %
                                           10);  //门架类型 根据门架字典表查询
    doorMsg.rateCompute =
            pTransInfo->gantryFeeInfo.rateFitCount > 0 ? 2 : 1;  //计费处理模式 1-无拟合路径2-拟合路径
    doorMsg.rateFitCount =
            pTransInfo->gantryFeeInfo
            .rateFitCount;  //计费拟合点数 无拟合填0，拟合则按拟合实际点数填写。计费成功必填

    doorMsg.OBUpayFeeSumBefore = 0;
    doorMsg.OBUpayFeeSumAfter = 0;
    doorMsg.OBUdiscountFeeSumBefore = 0;
    doorMsg.OBUdiscountFeeSumAfter = 0;
    doorMsg.OBUfeeSumBefore = 0;
    doorMsg.OBUfeeSumAfter = 0;
    doorMsg.OBUProvfeeSumBefore = 0;
    doorMsg.OBUProvfeeSumAfter = 0;
    doorMsg.cardfeeSumBefore = 0;
    doorMsg.cardfeeSumBefore = 0;
    doorMsg.noCardTimesBefore = 0;
    doorMsg.noCardTimesAfter = 0;

    doorMsg.provinceNumBefore = pTransInfo->cpcIccInfo.cpcRoadInfo.nProvCnt;
    doorMsg.provinceNumAfter = pTransInfo->cpcIccInfo.cpcRoadInfo.nProvCnt_After;

    doorMsg.OBUTotalTradeSuccNumBefore = 0;
    doorMsg.OBUTotalTradeSuccNumAfter = 0;
    doorMsg.OBUProvTradeSuccNumBefore = 0;
    doorMsg.OBUProvTradeSuccNumAfter = 0;
    doorMsg.OBUTradeResult = 0;
    doorMsg.tradeType = 0;
    doorMsg.OBUInfoTypeRead = 0;
    doorMsg.OBUInfoTypeWrite = 0;
    doorMsg.OBUPassState = 1;
    doorMsg.feeVehicleType =
            pTransInfo->gantryFeeInfo.feeVehicleType;  //计费车型 计费模块计费时使用的车型
    doorMsg.OBULastGantryHex.clear();
    doorMsg.OBULastGantryTime.clear();

    doorMsg.feeMileage =
            pTransInfo->gantryFeeInfo
            .feeMileage;  //计费里程数 由计费模块返回的本次计费的总里程数（单位 :米）
    doorMsg.OBUMileageBefore = 0;
    doorMsg.OBUMileageAfter = 0;

    QString sProvMinFee = pTransInfo->gantryFeeInfo.provMinFee;
    if (sProvMinFee.length() > 0) {
        int nIndex = sProvMinFee.indexOf('|');
        if (nIndex >= 0) {
            QString sMod = sProvMinFee.left(nIndex + 1);
            doorMsg.provMinFeeCalcMode = sMod.toInt();
            sProvMinFee.remove(0, nIndex + 1);
            doorMsg.provMinFee =
                    sProvMinFee
                    .toUInt();  //本省累计通行金额参考
            //省界出口计费模块输出的用于参考的本省累计通行金额
            // provMinFee（最大长度 20
            //省界出口可与本省收费站入口或省界入口门架至省界的最短路径、最小费额等进行比较，仅作为本省累计通行金额的参考，供异常分析使用。
        }
    }

    doorMsg.feeSpare1 = pTransInfo->gantryFeeInfo.payFeeProvSumLocal;  //计费协议保留字段 1
    doorMsg.feeSpare2 = pTransInfo->gantryFeeInfo.provSumFeeMileage;   //计费协议保留字段 2
    doorMsg.feeSpare3 = 0;                                             //计费协议保留字段 3

    doorMsg.feeProvBeginHex = QString("000000");
    doorMsg.feeProvBeginHex = pTransInfo->cpcIccInfo.cpcRoadInfo.sProvEnFlag;
    if (0 == doorMsg.feeProvBeginHex.length()) {
        doorMsg.feeProvBeginHex = QString("000000");
    }
    if (Ptr_Info->IsEntryLane()) {
        doorMsg.lastGantryHexFee.clear();
        doorMsg.lastGantryHexPass.clear();
        doorMsg.lastGantryFeePass = 0;
        doorMsg.lastGantryMilePass = 0;
    } else {
        doorMsg.lastGantryHexPass = pTransInfo->cpcIccInfo.cpcRoadInfo.sNewFlag;
        doorMsg.lastGantryFeePass = pTransInfo->cpcIccInfo.cpcRoadInfo.nNewFlagRealMoney;
        doorMsg.lastGantryMilePass = pTransInfo->cpcIccInfo.cpcRoadInfo.nNewFlagTradeMeter;
        if (pTransInfo->cpcIccInfo.cpcTollCellInfo.ProCellInfoList.size() > 0) {
            if (pTransInfo->cpcIccInfo.cpcTollCellInfo.ProCellInfoList.last().bProv == 36)
                doorMsg.lastGantryHexFee =
                        pTransInfo->cpcIccInfo.cpcTollCellInfo.ProCellInfoList.last().sNewFlag;
            else
                doorMsg.lastGantryHexFee.clear();
        } else
            doorMsg.lastGantryHexPass.clear();
    }

    doorMsg.feeCalcSpecial = -1;         //计费接口特情值 必填，默认值-1
    doorMsg.chargesSpecialType.clear();  //收费特情类型
    // doorMsg.chargesSpecialType = pTransInfo->GetOncePaySpTypes();

    doorMsg.chargeMode =
            1;  //计费模式
    // 0-现有模式、1-点亮费显模式收费模式由车道生成产生如果是点亮版本，默认填写为1
    doorMsg.isFixData = 0;  //是否修正过 默认值0
    doorMsg.tradeReadCiphertext.clear();
    doorMsg.readCiphertextVerify = 0;
    doorMsg.tradeWriteCiphertext.clear();

    doorMsg.OBUProvPayFeeSumBefore = 0;
    doorMsg.OBUProvPayFeeSumAfter = 0;

    doorMsg.pathFitFlag = pTransInfo->gantryFeeInfo.fitResult;

    doorMsg.feeCalcSpecials = pTransInfo->gantryFeeInfo.sFeeSpecial;
    doorMsg.payFeeProvSumLocal = pTransInfo->gantryFeeInfo.payFeeProvSumLocal;
    doorMsg.PCRSUVersion = 35;
    doorMsg.gantryPassInfoAfter.clear();
    if (MediaType_CPC == pTransInfo->mediaType) {
        doorMsg.gantryPassInfoAfter = pTransInfo->cpcIccInfo.cpcRoadInfo.FlagInfoList_After;
        doorMsg.CPCFeeTradeResult = 0;
        if (pTransInfo->cpcIccInfo.cpcTollCellInfo_After.ProCellInfoList.size() > 0) {
            CProvCellInfo lastCellInfo =
                    pTransInfo->cpcIccInfo.cpcTollCellInfo_After.ProCellInfoList.last();
            CProvCellInfoRaw lastCellInfoRaw;
            memset(&lastCellInfoRaw, 0, sizeof lastCellInfoRaw);
            CCardFileConverter::ProvCellInfo2ProvCellInfoRaw(&lastCellInfo, &lastCellInfoRaw);
            doorMsg.FeeProvEF04 = Raw2HexStr((quint8 *)&lastCellInfoRaw, sizeof lastCellInfoRaw);
        } else {
            doorMsg.FeeProvEF04 = QString("-1");
        }
        doorMsg.fitProvFlag = 0;
        if (!Ptr_Info->IsEntryLane())
            doorMsg.gantryPassCountBefore = pTransInfo->cpcIccInfo.cpcRoadInfo.nProvFlagCnt;
        else
            doorMsg.gantryPassCountBefore = 0;
        doorMsg.OBUVehicleUserType = 0;
    }
    doorMsg.feeProvBeginHexFit = pTransInfo->gantryFeeInfo.feeProvBeginHexFit;
    doorMsg.feeProvBeginTimeFit = pTransInfo->gantryFeeInfo.feeProvBeginTimeFit;
    doorMsg.feeProvBeginTime.clear();
    if (Ptr_Info->IsExitLane()) {
        QDateTime enFlagTime;
        if (pTransInfo->cpcIccInfo.cpcRoadInfo.nProvEnFlagPassTime > 0) {
            UnixTime2QDateTime_GB(pTransInfo->cpcIccInfo.cpcRoadInfo.nProvEnFlagPassTime,
                                  enFlagTime);
            if (enFlagTime.isValid()) doorMsg.feeProvBeginTime = QDateTime2GBTimeStr(enFlagTime);
        }
    }
    doorMsg.feeSumLocalAfterEF04 = pTransInfo->cpcIccInfo.cpcRoadInfo.nProvTollMoney_After;
    doorMsg.updateResult = 1;
    doorMsg.branchAgency = "fangxing";

    return true;
}

bool CETCLaneCtrl::FillGantryMsg_Add(CTransInfo *pTransInfo, CDoorWaste_ETCTU_FD &doorMsg)
{
    // doorMsg.laneNum = pTransInfo->m_curGantryInfo.sGBLaneId;//国标车道编码

    doorMsg.gantryType = QString("%1").arg(CTollGantryMgr::GetTollGantryMgr()->GetGantryType() %
                                           10);  //门架类型 根据门架字典表查询
    doorMsg.rateCompute =
            pTransInfo->gantryFeeInfo.rateFitCount > 0 ? 2 : 1;  //计费处理模式 1-无拟合路径2-拟合路径
    doorMsg.rateFitCount =
            pTransInfo->gantryFeeInfo
            .rateFitCount;  //计费拟合点数 无拟合填0，拟合则按拟合实际点数填写。计费成功必填

    if (MediaType_OBU == pTransInfo->mediaType) {
        doorMsg.OBUpayFeeSumBefore =
                pTransInfo->IccInfo.ef04Info.totalFee;  //本次交易前标签累计应收金额
        doorMsg.OBUpayFeeSumAfter =
                pTransInfo->IccInfo.ef04Info.totalFee_After;  //本次交易后标签累计应收金额

        doorMsg.OBUdiscountFeeSumBefore =
                pTransInfo->IccInfo.ef04Info.totalFee -
                pTransInfo->IccInfo.ef04Info.totalLastFee;  //本次交易前标签累计优惠金额
        doorMsg.OBUdiscountFeeSumAfter =
                pTransInfo->IccInfo.ef04Info.totalFee_After -
                pTransInfo->IccInfo.ef04Info.totalLastFee_After;  //本次交易后标签累计优惠金额

        doorMsg.OBUProvfeeSumBefore =
                pTransInfo->IccInfo.ef04Info.localLastFee;  //本次交易前标签累计金额（实收）（省内）

        doorMsg.OBUProvfeeSumAfter =
                pTransInfo->IccInfo.ef04Info.localLastFee_After;  //本次交易后标签累计金额(实收)（省内）

        doorMsg.cardfeeSumBefore =
                pTransInfo->IccInfo.CardTollInfo.dwTotalFee_Before;  //本次交易前卡片累计金额
        doorMsg.cardfeeSumAfter =
                pTransInfo->IccInfo.CardTollInfo.dwTotalFee;  //本次交易后卡片累计金额
        doorMsg.noCardTimesBefore =
                pTransInfo->IccInfo.ef04Info.bTotalNoCardTimes;  //本次交易前累计无卡次数
        doorMsg.noCardTimesAfter =
                pTransInfo->IccInfo.ef04Info.bTotalNoCardTimes;  //本次交易后累计无卡次数

        doorMsg.provinceNumBefore =
                pTransInfo->IccInfo.ef04Info.bProvinceCount;  //本次交易前累计省份数量
        doorMsg.provinceNumAfter =
                pTransInfo->IccInfo.ef04Info.bProvinceCount_After;  //本次交易后累计省份数量

        doorMsg.OBUTotalTradeSuccNumBefore =
                pTransInfo->IccInfo.ef04Info.totalTransOkTimes;  //本次交易前标签累计写入成功总量
        doorMsg.OBUTotalTradeSuccNumAfter =
                pTransInfo->IccInfo.ef04Info.totalTransOkTimes_After;  //本次交易后标签累计写入成功总量

        doorMsg.OBUProvTradeSuccNumBefore =
                pTransInfo->IccInfo.ef04Info.bLocalTransOkTimes;  //省内本次交易前标签累计写入成功数量
        doorMsg.OBUProvTradeSuccNumAfter =
                pTransInfo->IccInfo.ef04Info
                .bLocalTransOkTimes_After;  //省内本次交易后标签累计写入成功数量
        doorMsg.OBUTradeResult = 0;         //标签交易结果 0-成功1-失败
        // doorMsg.OBUVerifyCode="0";              //标签交易流水验证码
        // 标签交易成功时，ETC门架根据交易关键信息生成标签验证码
        doorMsg.tradeType = 0;  //交易类型 0-复合消费+标签写卡  1-仅标签交易2-仅复合消费
        doorMsg.OBUInfoTypeRead = 0;  //读取标签入口信息类型 0- AA 1- BB 2- CC 3-其他
        doorMsg.OBUInfoTypeWrite = 0;  //本次写入标签入口信息类型 0- AA 1- BB 2- CC 3-其他
        doorMsg.OBUPassState = 1;      //标签入口状态 1-有入口  2-无入口
    } else if (MediaType_CPC == pTransInfo->mediaType) {
        doorMsg.provinceNumBefore = pTransInfo->cpcIccInfo.cpcRoadInfo.nProvCnt;
        doorMsg.provinceNumAfter = pTransInfo->cpcIccInfo.cpcRoadInfo.nProvCnt_After;
        doorMsg.feeSumLocalAfterEF04 = pTransInfo->cpcIccInfo.cpcRoadInfo.nProvTollMoney_After;
    }
    doorMsg.feeVehicleType =
            pTransInfo->gantryFeeInfo.feeVehicleType;  //计费车型 计费模块计费时使用的车型
    if (MediaType_OBU == pTransInfo->mediaType && pTransInfo->IccInfo.ef04Info.IsValid()) {
        if (Ptr_Info->IsExitLane()) {
            doorMsg.OBULastGantryHex =
                    pTransInfo->IccInfo.ef04Info.cardTollInfo
                    .sLastGantryHex;  // OBU中上一个门架HEX OBU内记录的上一个门架的hex编号
            QDateTime gantryTime;
            UnixTime2QDateTime_GB(pTransInfo->IccInfo.ef04Info.cardTollInfo.dwDoorFramePassTime,
                                  gantryTime);
            doorMsg.OBULastGantryTime = QDateTime2GBTimeStr(
                        gantryTime);  // OBU中通过上一个门架时间
            // OBU内记录的通过上一个门架的时间（YYYY-MM-DDTHH:mm:ss）
        }
        doorMsg.OBUMileageBefore =
                pTransInfo->IccInfo.ef04Info.totalTollMiles;  //本次交易前标签累计里程 单位:米
        doorMsg.OBUMileageAfter =
                pTransInfo->IccInfo.ef04Info.totalTollMiles_After;  //本次交易后标签累计里程 单位:米
    }

    doorMsg.feeMileage =
            pTransInfo->gantryFeeInfo
            .feeMileage;  //计费里程数 由计费模块返回的本次计费的总里程数（单位 :米）

    QString sProvMinFee = pTransInfo->gantryFeeInfo.provMinFee;
    if (sProvMinFee.length() > 0) {
        int nIndex = sProvMinFee.indexOf('|');
        if (nIndex >= 0) {
            QString sMod = sProvMinFee.left(nIndex + 1);
            doorMsg.provMinFeeCalcMode = sMod.toInt();
            sProvMinFee.remove(0, nIndex + 1);
            doorMsg.provMinFee =
                    sProvMinFee
                    .toUInt();  //本省累计通行金额参考
            //省界出口计费模块输出的用于参考的本省累计通行金额
            // provMinFee（最大长度 20
            //省界出口可与本省收费站入口或省界入口门架至省界的最短路径、最小费额等进行比较，仅作为本省累计通行金额的参考，供异常分析使用。
        }
    }

    doorMsg.feeSpare1 = pTransInfo->gantryFeeInfo.payFeeProvSumLocal;  //计费协议保留字段 1
    doorMsg.feeSpare2 = pTransInfo->gantryFeeInfo.provSumFeeMileage;   //计费协议保留字段 2
    doorMsg.feeSpare3 = 0;                                             //计费协议保留字段 3

    doorMsg.feeCalcSpecial = -1;         //计费接口特情值 必填，默认值-1
    doorMsg.chargesSpecialType.clear();  //收费特情类型
    // doorMsg.chargesSpecialType = pTransInfo->GetOncePaySpTypes();

    doorMsg.chargeMode =
            1;  //计费模式
    // 0-现有模式、1-点亮费显模式收费模式由车道生成产生如果是点亮版本，默认填写为1
    doorMsg.isFixData = 0;  //是否修正过 默认值0

    doorMsg.feeProvBeginHex = QString("000000");
    if (MediaType_OBU == pTransInfo->mediaType) {
        if (pTransInfo->IccInfo.ef04Info.IsValid()) {
            doorMsg.feeProvBeginHex = pTransInfo->IccInfo.ef04Info.sLocalEntryIdHex;
            if (doorMsg.feeProvBeginHex.isEmpty()) {
                doorMsg.feeProvBeginHex = QString("000000");
            }

            if (!Ptr_Info->IsEntryLane()) {
                doorMsg.lastGantryHexFee =
                        pTransInfo->IccInfo.CardTollInfo
                        .sLastGantryHex;  //计费文件中上一个门架的hex编号,放弃保留
                doorMsg.lastGantryHexPass = pTransInfo->IccInfo.ef04Info.cardTollInfo
                        .sLastGantryHex;  //过站信息中上一个门架的hex编号
            }

            doorMsg.tradeReadCiphertext = pTransInfo->IccInfo.ef04Info.tradeReadCiphertext;
            doorMsg.tradeWriteCiphertext.clear();
            doorMsg.readCiphertextVerify = 0;

            doorMsg.OBUfeeSumBefore =
                    pTransInfo->IccInfo.ef04Info.totalLastFee;  //本次交易前标签累计金额-实收
            doorMsg.OBUfeeSumAfter =
                    pTransInfo->IccInfo.ef04Info.totalLastFee_After;  //本次交易后标签累计金额-实收

            doorMsg.OBUProvPayFeeSumBefore =
                    pTransInfo->IccInfo.ef04Info.localTotalFee;  //本次交易前标签累计应收金额（省内）
            doorMsg.OBUProvPayFeeSumAfter =
                    pTransInfo->IccInfo.ef04Info
                    .localTotalFee_After;  //本次交易后标签累计应收金额（省内）
        }
    } else if (MediaType_CPC == pTransInfo->mediaType) {
        doorMsg.feeProvBeginHex = pTransInfo->cpcIccInfo.cpcRoadInfo.sProvEnFlag;
        if (0 == doorMsg.feeProvBeginHex.length()) {
            doorMsg.feeProvBeginHex = QString("000000");
        }
        if (Ptr_Info->IsEntryLane()) {
            doorMsg.lastGantryHexFee.clear();
            doorMsg.lastGantryHexPass.clear();
            doorMsg.lastGantryFeePass = 0;
            doorMsg.lastGantryMilePass = 0;
        } else {
            doorMsg.lastGantryHexPass = pTransInfo->cpcIccInfo.cpcRoadInfo.sNewFlag;
            doorMsg.lastGantryFeePass = pTransInfo->cpcIccInfo.cpcRoadInfo.nNewFlagRealMoney;
            doorMsg.lastGantryMilePass = pTransInfo->cpcIccInfo.cpcRoadInfo.nNewFlagTradeMeter;
            if (pTransInfo->cpcIccInfo.cpcTollCellInfo.ProCellInfoList.size() > 0) {
                if (pTransInfo->cpcIccInfo.cpcTollCellInfo.ProCellInfoList.last().bProv == 36)
                    doorMsg.lastGantryHexFee =
                            pTransInfo->cpcIccInfo.cpcTollCellInfo.ProCellInfoList.last().sNewFlag;
                else
                    doorMsg.lastGantryHexFee.clear();
            } else
                doorMsg.lastGantryHexPass.clear();
        }
    }
    doorMsg.pathFitFlag = pTransInfo->gantryFeeInfo.fitResult;

    doorMsg.feeCalcSpecials = pTransInfo->gantryFeeInfo.sFeeSpecial;
    doorMsg.payFeeProvSumLocal = pTransInfo->gantryFeeInfo.payFeeProvSumLocal;
    doorMsg.PCRSUVersion = 35;
    doorMsg.gantryPassInfoAfter.clear();
    if (MediaType_CPC == pTransInfo->mediaType) {
        doorMsg.gantryPassInfoAfter = pTransInfo->cpcIccInfo.cpcRoadInfo.FlagInfoList_After;
        doorMsg.CPCFeeTradeResult = 0;
        if (pTransInfo->cpcIccInfo.cpcTollCellInfo_After.ProCellInfoList.size() > 0) {
            CProvCellInfo lastCellInfo =
                    pTransInfo->cpcIccInfo.cpcTollCellInfo_After.ProCellInfoList.last();
            CProvCellInfoRaw lastCellInfoRaw;
            memset(&lastCellInfoRaw, 0, sizeof lastCellInfoRaw);
            CCardFileConverter::ProvCellInfo2ProvCellInfoRaw(&lastCellInfo, &lastCellInfoRaw);
            doorMsg.FeeProvEF04 = Raw2HexStr((quint8 *)&lastCellInfoRaw, sizeof lastCellInfoRaw);
        } else {
            doorMsg.FeeProvEF04 = QString("-1");
        }
        doorMsg.fitProvFlag = 0;
        if (!Ptr_Info->IsEntryLane())
            doorMsg.gantryPassCountBefore = pTransInfo->cpcIccInfo.cpcRoadInfo.nProvFlagCnt;
        else
            doorMsg.gantryPassCountBefore = 0;
        doorMsg.OBUVehicleUserType = 0;
    } else {
        doorMsg.CPCFeeTradeResult = -1;
        doorMsg.FeeProvEF04 = QString("-1");
        doorMsg.fitProvFlag = -1;
        doorMsg.gantryPassCountBefore = -1;
        doorMsg.OBUVehicleUserType = pTransInfo->OBUVehInfo.bUserType;
    }
    doorMsg.updateResult = 1;

    doorMsg.feeProvBeginHexFit = pTransInfo->gantryFeeInfo.feeProvBeginHexFit;
    doorMsg.feeProvBeginTimeFit = pTransInfo->gantryFeeInfo.feeProvBeginTimeFit;

    doorMsg.branchAgency = "fangxing";

    return true;
}

bool CETCLaneCtrl::FillGantryMsg_New(CTransInfo *pTransInfo, CDoorWaste_ETCTU_FD &doorMsg)
{
    if (pTransInfo->mediaType == MediaType_OBU) {
        FillGantryMsg_OBU(pTransInfo, doorMsg);
        FillGantryMsg_Add_OBU(pTransInfo, doorMsg);
    } else {
        FillGantryMsg_CPC(pTransInfo, doorMsg);
        FillGantryMsg_Add_CPC(pTransInfo, doorMsg);
    }
    return true;
}

/*
bool CETCLaneCtrl::FillLaneHeart(CLaneHeart_LHBU &msg)
{
    QDateTime now = QDateTime::currentDateTime();

    msg.id = QString("%1%2").arg(Ptr_Info->GetGBLaneId()).arg(now.toString("yyyyMMddhhmmss"));
//收费车道编号(21位)+本心跳生成时间(yyyymmddhhmmss)（14位） msg.tollStationId =
Ptr_Info->GetGBStationId(); //营改增国标编码 msg.tollLaneId = Ptr_Info->GetGBLaneId();
    msg.tollStation = Ptr_Info->GetStationID();//省内编码;
    msg.tollLane = Ptr_Info->GetLaneId();
    msg.laneType = 1; //1-ETC车道 2-MTC车道 3-混合车道
    msg.laneSign = Ptr_Info->GetLaneSign();
    msg.heartBeatTime = now.toString("yyyy-MM-ddThh:mm:ss");
    msg.vehicalGreyListVersion = QString("0");//当前灰名单版本号
    //当前信用黑名单版本号
    CCrediteBList * credBlist = (CCrediteBList*)CParamFileMgr::GetParamFile(cfCreditBList);
    if(NULL != credBlist)
        msg.vehicalBlacListVersion = credBlist->GetVersion();
    else
        msg.vehicalBlacListVersion = QString("0");
    CGCardBlackTable * cardBlist =
(CGCardBlackTable*)CParamFileMgr::GetParamFile(cfGBCardBList); if(NULL != cardBlist)
        msg.obuBlackListVersion = cardBlist->GetVersion(); //当前obu状态名单版本号
    else
        msg.cpuBlackListVersion = QString("0"); //用户卡状态名单版本号
    msg.cpcGreyListVerision = QString("0"); //当前cpc卡灰名单版本号
    //最短路径计费参数版本号
    CGShortPath * shortPath = (CGShortPath*)CParamFileMgr::GetParamFile(cfMinPathPara);
    if(NULL != shortPath)
        msg.spcRateVersion = shortPath->GetVersion();
    else
        msg.spcRateVersion = QString("0");

    if(lsNormalWorking == GetLaneStatus())//当上班状态时代表车道开启,其他代表车道关闭
        msg.laneStatus = 0;//车道状态 0-开启车道 1-关闭车道
    else
        msg.laneStatus = 1;
    if('1' == m_DevInfo[DEV_INDEX_ETCAnt])
        msg.rsuStatus = QString("1");//RSU状态 0-无响应 1-正常响应 2-无设备
涉及多个RSU状态以“|”分隔 else msg.rsuStatus = QString("0"); msg.cardReaderStatus = QString("2");
//读卡器状态 0-无响应 1-正常响应 2-无设备 涉及多个RSU状态以“|”分隔 msg.payEquipmentStatus =
QString("2"); //移动支付状态  0-无响应 1-正常响应 2-无设备 涉及多个RSU状态以“|”分隔

    msg.VPLRStatus = QString("1"); //车牌识别状态  0-无响应 1-正常响应 2-无设备
涉及多个RSU状态以“|”分隔 msg.vehDetectorStatus = QString("2"); //车检器状态
msg.axleDetectorStatus = QString("2");//轴型状态 msg.lightDetectorStatus =
QString("2");//光栅状态 msg.HDVideoStatus = QString("1");//车道摄像机状态 if('1' ==
m_DevInfo[DEV_INDEX_FeeDisplay]) msg.feeBoardStatus = 1; else msg.feeBoardStatus = 0;

    msg.hintsBoardStatus = 2; //信息提示屏状态
    msg.trafficLight1Status = 2; //同行信号灯状态
    msg.infoBoardStatus = 2; //etc情报板状态
    msg.entrydOverloadStatus = 2; //入口治超设施状态
    msg.sysVer = QString("Winxp");//当前操作系统版本号
    msg.opsVer = Ptr_Info->GetAppVer();//车道软件版本号
    msg.delayRecord = m_pDataMgr->GetUnSendDataCnt(); //未上传流水数量
    msg.delayRecordGenTime = m_pDataMgr->getFirstUnSendWasteTime();//未上传流水的时间
    return true;
}
*/
bool CETCLaneCtrl::FillLaneBasicInfo(const QDateTime &OccurTime, CBasicInfo_Lane &basicInfo,
                                     const QString &TransCode, CTransInfo *pTransInfo)
{
    QString sBatch, sNo;

    quint32 nStationId = Ptr_Info->GetStationID();
    int nLaneId = Ptr_Info->GetLaneId();
    QString sGBStationId = Ptr_Info->GetGBStationId();
    QString sGBLaneId = Ptr_Info->GetGBLaneId();
    QString sTollStationHex = Ptr_Info->GetHexStationID();
    QString sLaneHex = Ptr_Info->GetHexLaneID();

    if (pTransInfo) {
        if (pTransInfo->m_curGantryInfo.sGantryHex.length() > 0) {
            nStationId = pTransInfo->m_curGantryInfo.nStationId;
            nLaneId = pTransInfo->m_curGantryInfo.bLaneId;
            sTollStationHex = pTransInfo->m_curGantryInfo.sStationHex;
            sGBStationId = pTransInfo->m_curGantryInfo.sGBStationId;
            sGBLaneId = pTransInfo->m_curGantryInfo.sGBLaneId;
            sLaneHex = pTransInfo->m_curGantryInfo.sLaneHex;
        }
    }

    if (pTransInfo && pTransInfo->m_sId.length() > 0)
        basicInfo.id = pTransInfo->m_sId;
    else {
        basicInfo.id = sGBLaneId + CBatchMgr::GetBatchMgr()->GetBatchInfo(
                    sBatch, sNo, sLaneHex, CBatchMgr::SnType_Lane, false);
        if (pTransInfo) pTransInfo->m_sId = basicInfo.id;
    }
    basicInfo.sOccurTime = QDateTime2GBTimeStr(OccurTime);
    basicInfo.transCode = TransCode;
    basicInfo.bl_SubCenter = Ptr_Info->GetBL_SubCenter();
    basicInfo.bl_Center = Ptr_Info->GetBL_SubCenter();  // Ptr_Info->GetBL_Center();
    basicInfo.lDate = m_ShiftMgr.m_ShiftParam.tmWorkDate.toString("yyyy-MM-dd");
    basicInfo.shift = m_ShiftMgr.m_ShiftParam.wShift;
    QString sLoginTime = m_ShiftMgr.m_tmLoginTime.toString("yyyyMMddhhmmss");
    basicInfo.batchNum =
            Ptr_Info->GetGBLaneId() + sLoginTime + QString::number(m_ShiftMgr.m_dwOperId);
    basicInfo.loginTime = QDateTime2GBTimeStr(m_ShiftMgr.m_tmLoginTime);
    basicInfo.triggerTime = basicInfo.sOccurTime;
    basicInfo.operId = QString::number(m_ShiftMgr.m_dwOperId);
    if (m_ShiftMgr.m_sOperName.length() == 0)
        basicInfo.operName = QString("000000");
    else
        basicInfo.operName = m_ShiftMgr.m_sOperName;
    basicInfo.laneAppVer = Ptr_Info->GetTransVer();
    basicInfo.laneType = Ptr_Info->ConverLaneTypeToTrans();  // 1-ETC 2-MTC 3-混合

    // quint32 nStationId = Ptr_Info->GetStationID();
    basicInfo.enTollStation = nStationId;
    basicInfo.exStationName = Ptr_Info->GetStationName();  // Ptr_Info->GetOrgCode().sOrgSName;
    basicInfo.enTollLane = QString("%1").arg(nLaneId, 3, 10, QLatin1Char('0'));

    basicInfo.enTollStationHex =
            sTollStationHex;  // Ptr_Info->GetHexStationID();  // Long2Hex(nStationId,4);
    basicInfo.enTollLaneHex =
            sLaneHex;  // Ptr_Info->GetHexLaneID();        // Long2Hex(basicInfo.enTollLane,2);
    basicInfo.enTollStationId = sGBStationId;  // Ptr_Info->GetGBStationId();
    basicInfo.enTollLaneId = sGBLaneId;        // Ptr_Info->GetGBLaneId();

    //称重设备检测编号
    basicInfo.equipCode = QString("CH%1").arg(Ptr_Info->GetGBLaneId());

    basicInfo.paraVer = CParamFileMgr::GetAllParaVer();
    if (Ptr_Info->IsEntryLane())
        basicInfo.direction = 1;
    else
        basicInfo.direction = 2;
    //
    basicInfo.verifyCode = "0";
    return true;
}

bool CETCLaneCtrl::FillLaneBasicInfo_remote(protocol::LaneInfo *pLaneInfo,
                                            const QDateTime &occurTime)
{
    if (!pLaneInfo) return false;
    pLaneInfo->set_lanetype(Ptr_Info->GetLaneType());
    pLaneInfo->set_stationcode(Ptr_Info->GetStationID());
    pLaneInfo->set_lanecode((int)Ptr_Info->GetLaneId());
    QString sStationName = Ptr_Info->GetStationName();
    std::string stdStationName = sStationName.toStdString();
    pLaneInfo->set_stationname(
                stdStationName);  //(char *)bStationName.data(), bStationName.size());
    QByteArray sGBStationId = Ptr_Info->GetGBStationId().toLocal8Bit();
    if (sGBStationId.size() > 0) {
        pLaneInfo->set_stationid(sGBStationId.data(), sGBStationId.size());
    }
    QByteArray sGBLaneId = Ptr_Info->GetGBLaneId().toLocal8Bit();
    if (sGBLaneId.size() > 0) pLaneInfo->set_laneid(sGBLaneId.data(), sGBLaneId.size());

    QByteArray sTime = occurTime.toString("yyyyMMdhhmmss").toLocal8Bit();
    pLaneInfo->set_occurtime(sTime.data(), sTime.size());
    QByteArray lData = m_ShiftMgr.m_ShiftParam.tmWorkDate.toString("yyyyMMdd").toLocal8Bit();
    pLaneInfo->set_ldate(lData.data(), lData.size());
    pLaneInfo->set_shift((int)m_ShiftMgr.m_ShiftParam.wShift);

    QByteArray sLoginTime = m_ShiftMgr.m_tmLoginTime.toString("yyyyMMddhhmmss").toLocal8Bit();
    pLaneInfo->set_logintime(sLoginTime.data(), sLoginTime.size());
    QByteArray bOperId = QString::number(m_ShiftMgr.m_dwOperId).toLocal8Bit();
    pLaneInfo->set_operid(bOperId.data(), bOperId.size());

    QString sName;
    if (m_ShiftMgr.m_sOperName.length() == 0)
        sName = QString("000000");
    else
        sName = m_ShiftMgr.m_sOperName;

    std::string stdName = sName.toStdString();
    pLaneInfo->set_opername(stdName);
    QByteArray sApper = Ptr_Info->GetTransVer().toLocal8Bit();
    pLaneInfo->set_laneappver(sApper.data(), sApper.size());
    QString sParam;
    qint32 nFileNum = 0;
    CParamFileMgr::GetFileInfoStr(cfOrgCode, cfEnd, sParam, nFileNum, false, false);
    QByteArray bParamInfo = sParam.toLocal8Bit();
    pLaneInfo->set_paraver(bParamInfo.data(), bParamInfo.size());
    CParamFileMgr::GetFileInfoStr(cfOrgCode, cfEnd, sParam, nFileNum, false, true);
    bParamInfo = sParam.toLocal8Bit();
    pLaneInfo->set_paravernew(bParamInfo.data(), bParamInfo.size());
    //  pLaneInfo->set_devconnstatus(QString("1111111111111111").toStdString());

    // pLaneInfo -

    return true;
}

bool CETCLaneCtrl::FillLaneVehInfo(CTransInfo *pTransInfo, CVehInfo_Lane &vehInfo)
{
    if (!pTransInfo) return true;

    vehInfo.vehicleType = pTransInfo->VehInfo.VehClass;
    if (0 == vehInfo.vehicleType) vehInfo.vehicleType = 1;
    vehInfo.vehicleClass = pTransInfo->VehInfo.GetGBVehTypeForTrans();
    vehInfo.vlpc = pTransInfo->VehInfo.nVehPlateColor;
    if (vehInfo.vlpc == 0xFF) {
        vehInfo.vlpc = 0;
    }
    char szCehPlate[16];
    RemovePlateSpecChar(szCehPlate, sizeof(szCehPlate), pTransInfo->VehInfo.szVehPlate,
                        sizeof(pTransInfo->VehInfo.szVehPlate));
    vehInfo.vlp = GB2312toUnicode(szCehPlate).trimmed();
    if (vehInfo.vlp.isEmpty()) {
        vehInfo.vlp = QString("默A00000");
    }
    if (vehInfo.vlp.startsWith("闵")) {
        vehInfo.vlp = QString("闽%1").arg(vehInfo.vlp.remove(0, 1));
    }

    if (pTransInfo->AutoRegInfo.id.length() > 0) {
        vehInfo.identifyVlp = pTransInfo->AutoRegInfo.sAutoVehPlate.trimmed();
        vehInfo.identifyVlpc = pTransInfo->AutoRegInfo.nAutoVLPColor;
        if (!IsValidVehPlateColor(vehInfo.identifyVlpc)) {
            vehInfo.identifyVlpc = 0;
        }
        vehInfo.vehicleSignId = pTransInfo->AutoRegInfo.id;
        if (vehInfo.identifyVlp.length() > 0) {
            vehInfo.identifyVehicleId =
                    QString("%1_%2").arg(vehInfo.identifyVlp).arg(vehInfo.identifyVlpc);
        }
    } else {
        vehInfo.identifyVehicleId.clear();
    }

    vehInfo.vCount = 1;
    vehInfo.inductCnt = pTransInfo->nDetectNum;
    vehInfo.vSpeed = pTransInfo->GetVehSpeed();

    vehInfo.vehicleId = QString("%1_%2").arg(vehInfo.vlp).arg(vehInfo.vlpc);
    vehInfo.vehicleSign = "0xff";
    if (pTransInfo->bTransOk()) {
        vehInfo.vehicleSign = QString("0x%1").arg(pTransInfo->m_bVehState, 2, 16, QLatin1Char('0'));
    }
    return true;
}

bool CETCLaneCtrl::FillIccInfoLane(CTransInfo *pTransInfo, CICCInfo_Lane &IccInfo)
{
    IccInfo.Clear();
    if (!pTransInfo) return false;

    IccInfo.mediaType = pTransInfo->mediaType;

    if (MediaType_OBU == pTransInfo->mediaType) {
        IccInfo.obuSign = 2;  // OBU单/双片标识	处理成功时必填。1位数字 1-单片式OBU
        // 2-双片式OBU
        char szProvider[8];
        memset(szProvider, 0, sizeof szProvider);
        memcpy(szProvider, pTransInfo->OBUBaseInfo.ContractProvider, 4);
        IccInfo.obuIssueFlag = GB2312toUnicode(szProvider);  // OBU发行方标识,山东山东
        IccInfo.obuId = QString::fromAscii(pTransInfo->OBUBaseInfo.szContractSerialNumber);
        //应该判断mediatype=1时，obuid不能为空
        if (IccInfo.obuId.isEmpty()) {
            IccInfo.obuId = QString("0000000000000000");
        }
        IccInfo.mediaNo = IccInfo.obuId;  //出口报文字段
        IccInfo.electricalPercentage = 100;
        IccInfo.OBUVehiclePlate =
                GB2312toUnicode(pTransInfo->OBUVehInfo
                                .szVehPlate);  // OBU 内车牌号码
        // 标签内发行的车牌号码+间隔符+车牌颜色，间隔符：“_”
        if (IccInfo.OBUVehiclePlate.isEmpty()) {
            IccInfo.OBUVehiclePlate = QString("-");
        }
        IccInfo.OBUVehicleType = pTransInfo->OBUVehInfo.bVehClass;  // OBU 内车型	EF01 第 15
        // 字节
        int nDevIndex = pTransInfo->m_nRsuIndex;
        CRsuDev *pDev = CDeviceFactory::GetRsuDev(nDevIndex);
        if (pDev) {
            IccInfo.supplierId =
                    QString("%1")
                    .arg(pDev->GetRsuBaseInfo()->rsuManulId, 2, 16, QLatin1Char('0'))
                    .toUpper();  //供应商
        }

        if (pTransInfo->OBUBaseInfo.dwOBUID > 0)
            IccInfo.OBUVersion = QString("%1").arg(pTransInfo->OBUBaseInfo.ContractVersion);
        else {
            IccInfo.OBUVersion = QString("%1").arg(pTransInfo->IccInfo.ProCardBasicInfo.bVersion);
        }

        if (CARD_TYPE_STORE_CARD == pTransInfo->IccInfo.ProCardBasicInfo.bType) {
            IccInfo.cardType = 1;
        } else if (CARD_TYPE_TALLY_CARD == pTransInfo->IccInfo.ProCardBasicInfo.bType)
            IccInfo.cardType = 2;
        else
            IccInfo.cardType = 0;

        IccInfo.cardNet = QString::fromAscii(pTransInfo->IccInfo.ProCardBasicInfo.szNetworkId);
        IccInfo.cardId =
                IccInfo.cardNet + QString::fromLocal8Bit(pTransInfo->IccInfo.ProCardBasicInfo.szCardNo);
        IccInfo.cardCnt = 1;
        IccInfo.cardVersion = QString("%1").arg(pTransInfo->IccInfo.ProCardBasicInfo.bVersion);
        IccInfo.cardSn = 0;

    } else if (MediaType_CPC == pTransInfo->mediaType) {
        IccInfo.obuSign = 0;
        IccInfo.mediaNo = QString::fromLatin1(pTransInfo->cpcIccInfo.cpcBasicInfo.sCardID);
        IccInfo.cardSn = pTransInfo->cpcIccInfo.cpcCardMac;
        if (pTransInfo->cpcIccInfo.cpcBasicInfo.nBattery <= 100)
            IccInfo.electricalPercentage = pTransInfo->cpcIccInfo.cpcBasicInfo.nBattery;
        else
            IccInfo.electricalPercentage = 100;
        IccInfo.cardNet = QString::fromAscii(pTransInfo->cpcIccInfo.cpcBasicInfo.sCardID).left(4);
        IccInfo.cardId = QString::fromLocal8Bit(pTransInfo->cpcIccInfo.cpcBasicInfo.sCardID);
        IccInfo.cardCnt = 1;
        IccInfo.cardVersion =
                QString("%1").arg(pTransInfo->cpcIccInfo.cpcBasicInfo
                                  .nVersion);  //, 2, 16,
        // QLatin1Char('0'));  //
        // pTransInfo->cpcIccInfo.cpcBasicInfo.nVersion;
        // cpc过站数
        IccInfo.gantryPassCount = pTransInfo->cpcIccInfo.cpcRoadInfo.nProvFlagCnt;
        if (pTransInfo->cpcIccInfo.cpcRoadInfo.nProvFlagCnt > 0) {
            IccInfo.gantryPassInfo = pTransInfo->cpcIccInfo.cpcRoadInfo.FlagInfoList;
        } else
            IccInfo.gantryPassInfo.clear();

    } else if (MediaType_Paper == pTransInfo->mediaType) {
        IccInfo.mediaNo = QString("01") + pTransInfo->m_sPaperId;
        IccInfo.cardId = QString("01") + pTransInfo->m_sPaperId;  //入口没有mediaNo
    } else {
        IccInfo.mediaNo = QString("030");  //无卡
        IccInfo.electricalPercentage = -1;
        IccInfo.cardId = QString("030");  //入口
    }

    IccInfo.passId =
            pTransInfo
            ->GetPassId();  // QString("%1%2").arg(IccInfo.obuId).arg(pTransInfo->TransTime.toString("yyyyMMddhhmmss"));

    return true;
}

bool CETCLaneCtrl::FillPayCardInfoLane(CTransInfo *pTransInfo, CPayCardInfo_Lane &payCardInfo,
                                       bool bEn)
{
    //交易类型 9
    payCardInfo.Clear();
    if (!pTransInfo) return false;

    //入口交易结束，transpayType为TransPT_None,因此 pProCardBasicInfo=NULL
    CProCardBasicInfo *pProCardBasicInfo = NULL;

    if (bEn) {
        if (pTransInfo->mediaType == MediaType_OBU) {
            pProCardBasicInfo = &pTransInfo->IccInfo.ProCardBasicInfo;
            payCardInfo.transType = QString("09");
        } else {
            payCardInfo.transType = QString("06");
        }
    } else {
        // etc刷卡交易，如果最终不是etc卡支付，支付卡信息，填写etc卡信息
        if (MediaType_OBU == pTransInfo->mediaType) {
            payCardInfo.transType = QString("09");
            pProCardBasicInfo = &pTransInfo->IccInfo.ProCardBasicInfo;
        } else {
            payCardInfo.transType = QString("06");
        }

        if (PayType_ETC == pTransInfo->m_payType) {  //刷ETC卡支付的，
            payCardInfo.transType = QString("09");
            pProCardBasicInfo = &pTransInfo->m_PayCardInfo;
        }

        /*
        if (TransPT_OBU == pTransInfo->m_transPayType) {
            payCardInfo.transType = QString("09");
            pProCardBasicInfo = &pTransInfo->IccInfo.ProCardBasicInfo;
        } else if (TransPT_ETCCard == pTransInfo->m_transPayType) {
            payCardInfo.transType = QString("09");
            pProCardBasicInfo = &pTransInfo->m_PayCardInfo;
        } else
            payCardInfo.transType = QString("06");
            */
    }

    if (pProCardBasicInfo) {
        if (CARD_TYPE_STORE_CARD == pProCardBasicInfo->bType) {
            payCardInfo.payCardType = "1";
        } else if (CARD_TYPE_TALLY_CARD == pProCardBasicInfo->bType)
            payCardInfo.payCardType = "2";
        else
            payCardInfo.payCardType.clear();

        payCardInfo.payCardNet = QString::fromAscii(pProCardBasicInfo->szNetworkId);  //支付卡网络号
        payCardInfo.payCardId = QString::fromAscii(pProCardBasicInfo->szCardNo);
        payCardInfo.payCardTranSN = pTransInfo->ConsumeInfo.wCardSeq;
        payCardInfo.balanceBefore = pTransInfo->ConsumeInfo.dwBalanceBefore;
        payCardInfo.balanceAfter = pTransInfo->ConsumeInfo.dwBalanceAfter;
        payCardInfo.transFee = pTransInfo->ConsumeInfo.dwMoney;
        payCardInfo.terminalNo = Raw2HexStr(pTransInfo->ConsumeInfo.psamTermNo, 6);
        payCardInfo.terminalTransNo =
                QString("%1")
                .arg(pTransInfo->ConsumeInfo.dwTermSeq, 8, 16, QLatin1Char('0'))
                .toUpper();  // Raw2HexStr((quint8 *)&pTransInfo->ConsumeInfo.dwTermSeq,
        // sizeof(pTransInfo->ConsumeInfo.dwTermSeq)); //psam脱机交易序列号
        if (0 == pTransInfo->ConsumeInfo.dwTac)
            payCardInfo.TAC = QString("00000000");
        else
            payCardInfo.TAC =
                    Raw2HexStr(pTransInfo->ConsumeInfo.bTac, sizeof pTransInfo->ConsumeInfo.bTac);

        payCardInfo.algorithmIdentifier = pTransInfo->ConsumeInfo.KeyType;  //算法标识 1-3des 2-sm4
        if (4 == payCardInfo.algorithmIdentifier) {
            payCardInfo.algorithmIdentifier = 2;
        } else
            payCardInfo.algorithmIdentifier = 1;
        payCardInfo.keyVersion = pTransInfo->ConsumeInfo.bKeyVer;  //密钥版本,车道流水没定义该字段
    } else {
        payCardInfo.terminalNo =
                QString("000000000000");  // Raw2HexStr( pTransInfo->ConsumeInfo.psamTermNo,6);
        payCardInfo.terminalTransNo = QString("00000000");
        payCardInfo.TAC = QString("00000000");
        payCardInfo.algorithmIdentifier = 2;  //算法标识 1-3des 2-sm4
        payCardInfo.keyVersion = 0;
    }
    return true;
}

bool CETCLaneCtrl::FillDealInfoLane(CTransInfo *pTransInfo, char Deal[256],
CDealInfo_Lane &DealInfo)
{
    if (pTransInfo)
        DealInfo.signStatus =
                1;  // pTransInfo->bTransOk()?1:2;          //交易状态 1-成功
    // 2-失败,之前对于09报文，由于其他两家填1，所以要求我们填1（210526）
    else
        DealInfo.signStatus = 1;

    DealInfo.modifyFlag = 1;    //修改标志 1-原始 2-冲正 3-修正 4-手工插入 (出口)
    DealInfo.sourceId.clear();  //原始流水号(出口)
    if (pTransInfo) {
        if (pTransInfo->m_transPayType == TransPT_ETCCard ||
                pTransInfo->m_transPayType == TransPT_OBU) {
            DealInfo.serviceType = 1;
        } else {
            DealInfo.serviceType = 0;  //交易服务类型 0-现金 1-etc(出口)
        }
    } else {
        DealInfo.serviceType = 0;
    }

    DealInfo.specialType.clear();    //部特请类型 |
    DealInfo.spInfo = QString("0");  //业务分析特情
    DealInfo.description.clear();
    //描述
    if (pTransInfo)
        DealInfo.consumeTime = pTransInfo->GetTotalTime();  //交易耗时
    else
        DealInfo.consumeTime = 0;

    if (pTransInfo) {
        DealInfo.specialType = pTransInfo->GetOncePaySpTypes();
    } else
        DealInfo.specialType.clear();

    /*
    if(DealInfo.specialType.length()>1)
        DealInfo.specialType.remove(0,1);
    else
        DealInfo.specialType=QString("0");
    */
    if (pTransInfo)
        DealInfo.isBlackvehicle = pTransInfo->m_bBlackCard ? 1 : 2;
    else
        DealInfo.isBlackvehicle = 2;
    DealInfo.laneSpInfo = QString::fromAscii(Deal, 256);
    return true;
}

bool CETCLaneCtrl::FillWeightInfoLane(CTransInfo *pTransInfo, CWeightInfo_Lane &WeightInfo)
{
    WeightInfo.Clear();
    WeightInfo.axleCount = 2;
    WeightInfo.overWeightRate = 0;
    WeightInfo.limitWeight = TotalWeight_Limit;

    if (pTransInfo) {
        WeightInfo.overWeightRate = pTransInfo->m_nOverRate;
        if (Ptr_Info->IsExitLane()) {
            WeightInfo.Weight = pTransInfo->m_dwToTalWeight;
            WeightInfo.limitWeight = pTransInfo->m_dwWeightLimit;

            // 【修复】首先检查是否没有称重设备
            if (Ptr_Info->bNoWeightDev()) {
                DebugLog("出口无称重设备(noweight=1)，直接取入口重量的90%");

                // 无称重设备时直接使用入口重量的90%
                quint32 dwEntryWeight = pTransInfo->vehEntryInfo.dwTotalWeight;
                quint32 dwEntryLimitWeight = pTransInfo->vehEntryInfo.dwWeightLimit;

                if (dwEntryWeight > 0 && dwEntryWeight < 0x00ffffff) {
                    // 使用入口重量的90%
                    WeightInfo.Weight = qRound(dwEntryWeight * 0.9);
                    WeightInfo.limitWeight = dwEntryLimitWeight > 0 ? dwEntryLimitWeight : TotalWeight_Limit;
                    WeightInfo.axleCount = pTransInfo->vehEntryInfo.VehicalAxles;
                    if (0 == WeightInfo.axleCount || WeightInfo.axleCount > VehWeight_MaxAlxeNum) {
                        WeightInfo.axleCount = GetVehAxisNumByVC(pTransInfo->VehInfo.VehClass);
                    }

                    // 计算超重率
                    if (WeightInfo.Weight > WeightInfo.limitWeight && WeightInfo.limitWeight > 0) {
                        if (isCar(pTransInfo->VehInfo.VehClass)) {
                            WeightInfo.overWeightRate = 0;
                        } else {
                            WeightInfo.overWeightRate = qRound((WeightInfo.Weight - WeightInfo.limitWeight) * 1000.0 / WeightInfo.limitWeight);
                        }
                    } else {
                        WeightInfo.overWeightRate = 0;
                    }

                    DebugLog(QString("无称重设备-使用入口重量90%,入口重量:%1,出口重量:%2,限重:%3,轴数:%4,超重率:%5‰")
                             .arg(dwEntryWeight)
                             .arg(WeightInfo.Weight)
                             .arg(WeightInfo.limitWeight)
                             .arg(WeightInfo.axleCount)
                             .arg(WeightInfo.overWeightRate));
                } else {
                    // 入口重量无效时的备用处理
                    if (isTruck(pTransInfo->VehInfo.VehClass)) {
                        // 货车：根据车型设置合理的默认值
                        int nAxisNum = GetVehAxisNumByVC(pTransInfo->VehInfo.VehClass);
                        quint32 dwLimitWeight = GetWeightLimitByAxisNum(nAxisNum, isZhuangXiangTruck(pTransInfo->VehInfo.VehClass));

                        WeightInfo.Weight = qRound(dwLimitWeight * 0.8);
                        WeightInfo.limitWeight = dwLimitWeight;
                        WeightInfo.overWeightRate = 0;
                        WeightInfo.axleCount = nAxisNum;

                        DebugLog(QString("无称重设备-入口重量无效,货车按车型设置默认称重,车型:%1,轴数:%2,限重:%3,重量:%4")
                                 .arg(pTransInfo->VehInfo.VehClass)
                                 .arg(nAxisNum)
                                 .arg(dwLimitWeight)
                                 .arg(WeightInfo.Weight));
                    } else {
                        // 客车：使用固定默认值
                        WeightInfo.Weight = TotalWeight_Default;
                        WeightInfo.limitWeight = TotalWeight_Limit;
                        WeightInfo.overWeightRate = 0;
                        WeightInfo.axleCount = GetVehAxisNumByVC(pTransInfo->VehInfo.VehClass);

                        DebugLog(QString("无称重设备-入口重量无效,客车使用默认称重,车型:%1,轴数:%2,重量:%3")
                                 .arg(pTransInfo->VehInfo.VehClass)
                                 .arg(WeightInfo.axleCount)
                                 .arg(WeightInfo.Weight));
                    }
                }
            } else {
                // 有称重设备的处理逻辑
                // 【新增】判断是否需要尝试获取最新称重数据
                bool bNeedTryLatestWeight = false;

                // 情况1：交易没有称重数据且有称重设备
                if ((0 == WeightInfo.Weight || WeightInfo.Weight >= 0x00ffffff)) {
                    bNeedTryLatestWeight = true;
                    DebugLog("出口无称重数据且有称重设备，需要尝试获取最新称重数据");
                }

                // 情况2：配置了checkweight=0，且称重数据等于入口数据
                if (!Ptr_Info->bCheckWeight()) {
                    if (WeightInfo.Weight == pTransInfo->vehEntryInfo.dwTotalWeight &&
                        WeightInfo.Weight > 0 && WeightInfo.Weight < 0x00ffffff) {
                        bNeedTryLatestWeight = true;
                        DebugLog(QString("出口配置checkweight=0且称重数据等于入口数据[%1]，需要尝试获取最新称重数据")
                                 .arg(WeightInfo.Weight));
                    }
                }

                if (bNeedTryLatestWeight) {
                    // 首先尝试从VehWeightInfo获取最新称重数据
                    if (TryGetLatestWeightData(pTransInfo, WeightInfo)) {
                        DebugLog(QString("交易前无称重，使用最新称重数据,weight:%1,limit:%2,axleCount:%3")
                                 .arg(WeightInfo.Weight)
                                 .arg(WeightInfo.limitWeight)
                                 .arg(WeightInfo.axleCount));
                    } else {
                        // 如果没有最新称重数据，使用入口称重数据
                        WeightInfo.Weight = pTransInfo->vehEntryInfo.dwTotalWeight;
                        WeightInfo.limitWeight = pTransInfo->vehEntryInfo.dwWeightLimit;

                        // 【修复】使用入口称重数据时，同时获取入口轴型信息
                        int nEntryAxisNum = pTransInfo->vehEntryInfo.VehicalAxles;
                        if (nEntryAxisNum > 0 && nEntryAxisNum <= VehWeight_MaxAlxeNum) {
                            WeightInfo.axleCount = nEntryAxisNum;

                            // 根据轴数确定轴型编码
                            QString sAxisType;
                            switch (nEntryAxisNum) {
                                case 6: sAxisType = "157"; break;
                                case 5: sAxisType = "155"; break;
                                case 4: sAxisType = "115"; break;
                                case 3: sAxisType = "15"; break;
                                case 2:
                                    // 2轴需要根据车型判断是货1还是货2
                                    if (isTruck(pTransInfo->VehInfo.VehClass)) {
                                        // 根据车型判断，默认使用货2
                                        sAxisType = (pTransInfo->VehInfo.VehClass == 11) ? "11" : "12";
                                    } else {
                                        sAxisType = "11";  // 非货车默认使用12
                                    }
                                    break;
                                default:
                                    sAxisType = "11";  // 默认使用12
                                    break;
                            }

                            // 计算每个轴的重量（平均分配）
                            quint32 dwTotalWeight = WeightInfo.Weight;
                            QString sEntryAxisInfo;

                            // 计算实际轴数（根据轴型编码）
                            int nActualAxisCount = nEntryAxisNum;

                            // 平均分配重量
                            quint32 dwAvgWeight = (nActualAxisCount > 0) ? (dwTotalWeight / nActualAxisCount) : 0;
                            dwAvgWeight = dwAvgWeight * 0.98;
                            WeightInfo.Weight = 0;
                            // 生成轴型信息
                            for (int i = 0; i < sAxisType.length(); i++) {
                                int nAxisType = sAxisType[i].toLatin1() - '0';
                                int nAxisWeight = 0;

                                // 根据轴型计算重量
                                switch (nAxisType) {
                                    case 1: nAxisWeight = dwAvgWeight; break;      // 1轴
                                    case 2: nAxisWeight = dwAvgWeight; break;      // 2轴
                                    case 5: nAxisWeight = dwAvgWeight * 2; break;  // 5轴（2个轴）
                                    case 7: nAxisWeight = dwAvgWeight * 3; break;  // 7轴（3个轴）
                                    default: nAxisWeight = dwAvgWeight; break;
                                }
                                WeightInfo.Weight = WeightInfo.Weight + nAxisWeight;
                                // 格式化：1位轴型 + 6位重量
                                sEntryAxisInfo += QString("%1%2")
                                    .arg(nAxisType)
                                    .arg(nAxisWeight % 1000000, 6, 10, QChar('0'));
                            }

                            WeightInfo.axisInfo = sEntryAxisInfo;
                            DebugLog(QString("使用入口轴型信息,轴数:%1,轴型编码:%2,轴型信息:%3,总重:%4")
                                     .arg(nEntryAxisNum)
                                     .arg(sAxisType)
                                     .arg(sEntryAxisInfo)
                                     .arg(dwTotalWeight));
                        }

                        DebugLog(QString("取入口称重,weight:%1,limit:%2,axleCount:%3")
                                 .arg(WeightInfo.Weight)
                                 .arg(WeightInfo.limitWeight)
                                 .arg(WeightInfo.axleCount));

                        if (0 == WeightInfo.Weight || WeightInfo.Weight >= 0x00ffffff) {
                            DebugLog(QString("出口入口称重为0xff,填写缺省称重信息"));
                            if (isTruck(pTransInfo->VehInfo.VehClass)) {
                                // 出口货车可以根据车型设置合理的默认值
                                int nAxisNum = GetVehAxisNumByVC(pTransInfo->VehInfo.VehClass);
                                quint32 dwLimitWeight = GetWeightLimitByAxisNum(nAxisNum, isZhuangXiangTruck(pTransInfo->VehInfo.VehClass));

                                // 使用限重的99%作为默认重量，避免超重
                                WeightInfo.Weight = qRound(dwLimitWeight * 0.99);
                                WeightInfo.limitWeight = dwLimitWeight;
                                WeightInfo.overWeightRate = 0;

                                DebugLog(QString("出口货车按车型设置默认称重,车型:%1,轴数:%2,限重:%3,重量:%4")
                                         .arg(pTransInfo->VehInfo.VehClass)
                                         .arg(nAxisNum)
                                         .arg(dwLimitWeight)
                                         .arg(WeightInfo.Weight));
                            } else {
                                WeightInfo.Weight = TotalWeight_Default;
                                WeightInfo.overWeightRate = 0;
                            }
                        }
                    }

                    // 计算超重率（适用于所有情况）
                    if (WeightInfo.Weight > WeightInfo.limitWeight && WeightInfo.limitWeight != 0) {
                        if (isCar(pTransInfo->VehInfo.VehClass)) {
                            WeightInfo.overWeightRate = 0;
                        } else {
                            WeightInfo.overWeightRate =
                                    qRound((WeightInfo.Weight - WeightInfo.limitWeight) * 1000.0 /
                                           WeightInfo.limitWeight);
                        }
                    } else {
                        WeightInfo.overWeightRate = 0;
                    }
                }

                // 确保轴数正确设置
                if (0 == WeightInfo.axleCount || WeightInfo.axleCount > VehWeight_MaxAlxeNum) {
                    WeightInfo.axleCount = pTransInfo->vehEntryInfo.VehicalAxles;
                    if (0 == WeightInfo.axleCount || WeightInfo.axleCount > VehWeight_MaxAlxeNum) {
                        WeightInfo.axleCount = GetVehAxisNumByVC(pTransInfo->VehInfo.VehClass);
                    }
                }
            }

        } else {
            // // 入口车道处理逻辑
            // WeightInfo.Weight = pTransInfo->vehEntryInfo
            //         .dwTotalWeight;  // pTransInfo->OBUVehInfo.dwTotalWeight; //重量
            // WeightInfo.limitWeight = pTransInfo->vehEntryInfo.dwWeightLimit;
            // if (0 == WeightInfo.Weight || WeightInfo.Weight >= 0x00ffffff) {
            //     if (isTruck(pTransInfo->VehInfo.VehClass)) {
            //         // 入口货车必须有真实称重数据，绝对不能使用OBU重量或默认值
            //         WeightInfo.Weight = 0xffffff;  // 保持无效状态
            //         WeightInfo.overWeightRate = 0;
            //         DebugLog(QString("入口货车无有效称重数据,车型:%1,不允许使用OBU重量或默认值")
            //                  .arg(pTransInfo->VehInfo.VehClass));
            //     } else {
            //         // 客车可以使用默认值
            //         WeightInfo.Weight = TotalWeight_Default;
            //         WeightInfo.overWeightRate = 0;
            //     }
            // }
            // // 只有在轴数未设置时才使用入口轴数（避免覆盖已设置的入口轴数）
            // if (0 == WeightInfo.axleCount || WeightInfo.axleCount > VehWeight_MaxAlxeNum) {
            //     WeightInfo.axleCount = pTransInfo->vehEntryInfo.VehicalAxles;
            //     if (0 == WeightInfo.axleCount || WeightInfo.axleCount > VehWeight_MaxAlxeNum) {
            //         WeightInfo.axleCount = GetVehAxisNumByVC(pTransInfo->VehInfo.VehClass);
            //     }
            // }
            WeightInfo.Weight = pTransInfo->vehEntryInfo
                                    .dwTotalWeight;  // pTransInfo->OBUVehInfo.dwTotalWeight; //重量
            WeightInfo.limitWeight = pTransInfo->vehEntryInfo.dwWeightLimit;
            if (0 == WeightInfo.Weight || WeightInfo.Weight >= 0x00ffffff) {
                if (isTruck(pTransInfo->VehInfo.VehClass)) {
                    WeightInfo.Weight = pTransInfo->OBUVehInfo.dwTotalWeight;
                    WeightInfo.overWeightRate = 0;
                    if (WeightInfo.Weight > TotalWeight_Limit)
                        WeightInfo.Weight = TotalWeight_Default;
                } else {
                    WeightInfo.Weight = TotalWeight_Default;
                    WeightInfo.overWeightRate = 0;
                }
            }
            WeightInfo.axleCount = pTransInfo->vehEntryInfo.VehicalAxles;
            if (0 == WeightInfo.axleCount || WeightInfo.axleCount > VehWeight_MaxAlxeNum) {
                WeightInfo.axleCount = GetVehAxisNumByVC(pTransInfo->VehInfo.VehClass);
            }
        }
    }

    // QString axisInfo;               //轴组信息 1位轴型 +6位重量
    // 只有在轴型信息未设置时才使用默认值或当前交易轴型（避免覆盖入口轴型）
    if (WeightInfo.axisInfo.isEmpty()) {
        WeightInfo.axisInfo = QString("10000001000000");
        if (pTransInfo) {
            QString sAxisInfo;
            pTransInfo->m_vehAxisInfo.GetConfirmedAxisInfo(sAxisInfo);
            if (sAxisInfo.length() > 0) {
                WeightInfo.axisInfo = sAxisInfo;
            }
        }
    }
    return true;
}

bool CETCLaneCtrl::FillAuthInfoLane(CAuthInfo_Lane &AuthInfo, CTransInfo *pTransInfo)
{
    if (pTransInfo && pTransInfo->m_operInfo.dwOper > 0) {
        AuthInfo.monitor = QString("%1").arg(pTransInfo->m_operInfo.dwOper);         //授权工号
        AuthInfo.monitorName = QString("%1").arg(pTransInfo->m_operInfo.sOperName);  //授权姓名
        AuthInfo.monitorTime = QDateTime2GBTimeStr(pTransInfo->m_AuthTime);
    } else {
        AuthInfo.monitor = QString("%1").arg(m_ShiftMgr.m_dwOperId);       //授权工号
        AuthInfo.monitorName = QString("%1").arg(m_ShiftMgr.m_sOperName);  //授权姓名
        QDateTime curTime = QDateTime::currentDateTime();
        AuthInfo.monitorTime = QDateTime2GBTimeStr(curTime);  //授权时间
    }
    return true;
}

bool CETCLaneCtrl::FillEnAddInfoLane(CTransInfo *pTransInfo, CEnAddInfo_Lane &addInfo)
{
    addInfo.operationMedia = 1;
    addInfo.chargeMode = 1;
    if (pTransInfo) {
        // 从车型库获取vehicleTypeDbInfo信息
        addInfo.vehicleTypeDbInfo = GetVehicleTypeDbInfo(pTransInfo);

        if (pTransInfo->mediaType == MediaType_OBU) {
            addInfo.identifyVehicleType = QString::number(pTransInfo->OBUVehInfo.bVehClass);
            addInfo.obuUserType = "";
            addInfo.obuTotalWeight = QString::number(pTransInfo->OBUVehInfo.dwTotalWeight);
            addInfo.obuMaintenanceWeight = QString::number(pTransInfo->OBUVehInfo.dwWeight);
            addInfo.obuPermittedTowWeight =
                    QString::number(pTransInfo->OBUVehInfo.dwVehWeightLimit);
            addInfo.obuPermittedWeight = QString::number(pTransInfo->OBUVehInfo.dwVehWeightLimit);

            addInfo.obuLength = QString::number(pTransInfo->OBUVehInfo.wLength);
            addInfo.obuWeight = QString::number(pTransInfo->OBUVehInfo.wWidth);
            addInfo.obuHeight = QString::number(pTransInfo->OBUVehInfo.wHeight);
            addInfo.Length = addInfo.obuLength;
            addInfo.Width = addInfo.obuWeight;
            addInfo.Height = addInfo.obuHeight;
            addInfo.vehicleUserType = pTransInfo->IccInfo.ProCardBasicInfo.bUserType;
            addInfo.OBUVehicleUserType = pTransInfo->OBUVehInfo.bUserType;
        }
        if (pTransInfo->VehInfo.GBVehType == UVT_BigTruck) addInfo.certNo = pTransInfo->m_sCertNo;
    }

    addInfo.dropLeverTime = QDateTime2GBTimeStr(QDateTime::currentDateTime());
    addInfo.branchAgency = QString("fangxing");

    // 填写车辆外轮廓尺寸信息
    FillVehicleOutlineInfoLane(pTransInfo, addInfo);

    // 计算结果车型
    addInfo.calVehicleType = QString::number(CalculateVehicleType(pTransInfo));

    return true;
}

bool CETCLaneCtrl::FillVehicleOutlineInfoLane(CTransInfo *pTransInfo, CEnAddInfo_Lane &addInfo)
{
    bool bHasWeightOutlineData = false;

    if(pTransInfo){
        CVehAxisInfo lastestVeh;
        if(VehWeightInfo::GetVehWeightInfo()->GetFirstVeh(&lastestVeh)){
            if(lastestVeh.GetVehLength() > 0  || lastestVeh.GetVehWidth() > 0 || lastestVeh.GetVehHeight() > 0){
                addInfo.Length = QString::number(lastestVeh.GetVehLength());
                addInfo.Width = QString::number(lastestVeh.GetVehWidth());
                addInfo.Height = QString::number(lastestVeh.GetVehHeight());
                bHasWeightOutlineData = true;

                DebugLog(QString("车辆外轮廓尺寸信息:长度:%1,宽度:%2,高度:%3")
                         .arg(addInfo.Length)
                         .arg(addInfo.Width)
                         .arg(addInfo.Height));
            }
        }
    }

    if(!bHasWeightOutlineData){
        DebugLog("车辆外轮廓数据采用OBU数据");
    }
}

bool CETCLaneCtrl::FillExAddInfoLane(CTransInfo *pTransInfo, CExAddInfo_Lane &addInfo)
{
    addInfo.Clear();
    addInfo.chargeMode = 1;  // 1-点亮费显

    int nParaCode;
    QString sParamName, sVer;
    if (CParamFileMgr::GetParaInfo(cfMinFee, nParaCode, sParamName, sVer)) {
        addInfo.spcRateVersion = sVer;
        if (addInfo.spcRateVersion.length() == 9) addInfo.spcRateVersion = QString("20") + sVer;
    } else
        addInfo.spcRateVersion = "0";  //最短路径计费参数版本号 不超过40个字符。

    if (!pTransInfo) return true;

    // 从车型库获取vehicleTypeDbInfo信息
    addInfo.vehicleTypeDbInfo = GetVehicleTypeDbInfo(pTransInfo);
    
    // addInfo.enMediaType =pTransInfo->dwOBUID>0?1:9;            //入口通行介质 1-OBU 2-CPC卡
    // 3-纸券 9-无通行介质-已删除
    addInfo.transPayType =
            pTransInfo->m_transPayType;  //交易支付方式 1-出口ETC 通行 2-出口ETC 刷卡通行
    // 11-现金12-其他第三方账户支付 13-银联卡支付 16-支付宝 17-微信
    addInfo.feeMileage =
            pTransInfo
            ->m_nTotalMiles;  // IccInfo.ef04Info.totalTollMiles_After; //计费总里程数(单位:米）
    addInfo.payCode.clear();  //支付码
    addInfo.payCode = pTransInfo->m_payCode;
    if (pTransInfo->mediaType == MediaType_CPC) {
        if (pTransInfo->bIsFree)
            addInfo.feeProvInfo = 0;
        else
            addInfo.feeProvInfo = pTransInfo->m_nProvFee;
        // pTransInfo->cpcIccInfo.cpcRoadInfo.nProvTollMoney_After;  // CPC卡省内累加金额
    }
    pTransInfo->GetMinFeeInfo(addInfo.shortFee, addInfo.shortFeeMileage);
    if (addInfo.shortFee > 0) {
        int nRate = pTransInfo->m_nTransFee * 100 / addInfo.shortFee;
        addInfo.feeRate = (double)nRate / 100.0;
        //交易金额占比 总交易金额/最短路径交易金额，两位小数（四舍五入）
    } else
        addInfo.feeRate = 1.0;

    addInfo.actualFeeClass =
            pTransInfo->actualFeeClass;  //实际计费方式 1位数字1 - 按 OBU内累计金额计费2 - 按
    // ETC卡内累计金额计费3 - 按全网最短路径费率 4 -
    //省内最短路径费率 5 - 省内最小费率金额计费

    if (MediaType_OBU == pTransInfo->mediaType) {
        if (pTransInfo->IccInfo.ef04Info.IsValid()) {
            addInfo.OBUtotalCount = pTransInfo->IccInfo.ef04Info
                    .totalTransOkTimes_After;  // OBU总交易成功次数 大于等于0
            addInfo.noCardCount = pTransInfo->IccInfo.ef04Info
                    .bTotalNoCardTimes_After;  // OBU未插卡累计次数 大于等于0
        }
        addInfo.ETCtotalAmount =
                pTransInfo->IccInfo.CardTollInfo.dwTotalFee;  // ETC卡累计交易金额 大于等于0（单位：分）

        if (pTransInfo->IccInfo.ef04Info.IsValid()) {
            addInfo.provTransCount =
                    pTransInfo->IccInfo.ef04Info.bLocalTransOkTimes_After;  //本省交易成功次数
            // obu累计优惠前通行费金额指的是累计计费金额，obu累计优惠后通行费金额指的是累计实收金额
            addInfo.obuTotalAmount = pTransInfo->IccInfo.ef04Info.totalFee_After;
            addInfo.obuPayFee = pTransInfo->IccInfo.ef04Info.totalFee_After;
            if (pTransInfo->bIsFree) {
                addInfo.obuTotaldisCountAmount = 0;
                addInfo.obuDiscountFee =
                        pTransInfo->IccInfo.ef04Info.totalFee_After - addInfo.obuTotaldisCountAmount;
            } else {
                addInfo.obuTotaldisCountAmount = pTransInfo->IccInfo.ef04Info.totalLastFee_After;
                addInfo.obuDiscountFee = pTransInfo->IccInfo.ef04Info.totalFee_After -
                        pTransInfo->IccInfo.ef04Info.totalLastFee_After;
            }
        }
        addInfo.vehicleUserType = pTransInfo->IccInfo.ProCardBasicInfo.bUserType;
        addInfo.OBUVehicleUserType = pTransInfo->OBUVehInfo.bUserType;
    }

    addInfo.provFee = pTransInfo->m_nProvFee;
    if (pTransInfo->actualFeeClass == FeeClass_Min) {
        if (pTransInfo->m_bRepay) {
            addInfo.provinceCount = 1;
            addInfo.provinceTransGroup = QString("36:%1").arg(pTransInfo->m_nTransFee);
        } else {
            //   addInfo.provFee = pTransInfo->m_nProvFee;
            addInfo.provinceTransGroup = pTransInfo->MinFeeInfo.GetProvinceTransGroup(
                        pTransInfo->mediaType, pTransInfo->bIsFree);
            addInfo.provinceCount = pTransInfo->MinFeeInfo.provMinFeeGroup.size();  //通行省份个数
        }

    } else if (pTransInfo->actualFeeClass == FeeClass_OBU) {
        // addInfo.provFee = pTransInfo->IccInfo.ef04Info
        //                .localLastFee_After;  //本省累计通行费金额
        //交易支付方式为出口ETC通行时必填大于等于
        // 0（单位：分）
        addInfo.provinceCount = pTransInfo->IccInfo.ef04Info.bProvinceCount_After;  //通行省份个数
        addInfo.provinceTransGroup = pTransInfo->IccInfo.ef04Info.GetProvinceFeeGroupStr(
                    pTransInfo->bIsFree);  //多省交易信息组合
        //省份与单省实收金额用“:”分隔，不同省份使用“|”
        //分隔，举例：37:100|36:200
    } else if (pTransInfo->actualFeeClass == FeeClass_Card) {
        // addInfo.provFee = pTransInfo->cpcIccInfo.cpcRoadInfo.nProvTollMoney_After;
        addInfo.provinceCount = pTransInfo->cpcIccInfo.cpcTollCellInfo_After.ProCellInfoList.size();
        addInfo.provinceTransGroup =
                pTransInfo->cpcIccInfo.cpcTollCellInfo_After.GetProvinceFeeGroup(pTransInfo->bIsFree);

        QString sEnFlagHex = pTransInfo->cpcIccInfo.cpcRoadInfo.sProvEnFlag.toUpper();
        if (sEnFlagHex == QString("000000") || sEnFlagHex == QString("FFFFFF")) {
            addInfo.enPointId = pTransInfo->vehEntryInfo.sEnGBStationId;
        } else {
            COrgBasicInfo orgFlagInfo;
            COrgBasicInfoTable *pOrgTable =
                    (COrgBasicInfoTable *)CParamFileMgr::GetParamFile(cfOrgBasicInfo);
            bool bRlt = pOrgTable->QryOrgBasicInfo(36, ORG_TYPE_GANTRY_GB, sEnFlagHex, orgFlagInfo);
            if (bRlt) {
                addInfo.enPointId = orgFlagInfo.sId;
            } else
                addInfo.enPointId = pTransInfo->vehEntryInfo.sEnGBStationId;

            addInfo.exPointId = pTransInfo->m_curGantryInfo.sGantryId;
        }

    } else if (pTransInfo->actualFeeClass == FeeClass_MinistryCenter ||
               FeeClass_ProvCenter == pTransInfo->actualFeeClass) {
        // addInfo.provFee = pTransInfo->m_fareQryResult.GetProvFee(pTransInfo->mediaType);
        addInfo.provinceCount = pTransInfo->m_fareQryResult.provinceFees.size();
        addInfo.provinceTransGroup = pTransInfo->m_fareQryResult.GetProvinceFeeGroup(
                    pTransInfo->mediaType, pTransInfo->bIsFree);
    }

    if (addInfo.provinceCount > 1)
        addInfo.multiProvince = 1;
    else
        addInfo.multiProvince = 0;
    addInfo.tolldProvinceId = QString("360201");
    addInfo.opentype = 0;  //是否开放式 0-非放式 1-是

    addInfo.feeBoardPlay =
            pTransInfo->feeBoardPlay;  //费显显示信息 1位数字1 出口费显显示金额2 出口费显显示异常提示
    addInfo.checkSign = 0;
    if (pTransInfo->VehInfo.GBVehType == UVT_FarmProduct) {
        addInfo.checkSign = 1;
    } else {
        if (pTransInfo->m_bGreenCheckFailed) {
            addInfo.checkSign = 2;
        }
    }
    //查验标志; 0-未查验，1-省内绿通合格，2-省内绿通不合格，3-集装箱合格，4-集装箱不合格

    if (pTransInfo->m_nDiscountType > 0) {
        addInfo.discountType = QString::number(pTransInfo->m_nDiscountType);
        addInfo.provinceDiscountFee = pTransInfo->m_nProvinceDiscountFee;
        addInfo.originFee = pTransInfo->m_nOriginFee;
    } else
        addInfo.discountType.clear();

    if (pTransInfo->m_bEmVeh)
        addInfo.appointId = pTransInfo->m_emVehInfo.id;
    else
        addInfo.appointId.clear();
    addInfo.dropLeverTime = QDateTime2GBTimeStr(QDateTime::currentDateTime());
    addInfo.branchAgency = QString("fangxing");

    addInfo.listNo = pTransInfo->m_sListNo;
    addInfo.opid = pTransInfo->m_sOpId;

    // 计算结果车型
    addInfo.calVehicleType = QString::number(CalculateVehicleType(pTransInfo));

    return true;
}

bool CETCLaneCtrl::FillVehEnInfoLane(CTransInfo *pTransInfo, CVehEnInfo_Lane &VehEnInfo)
{
    VehEnInfo.enTollStation = pTransInfo->vehEntryInfo.dwEnStationID;
    VehEnInfo.enTollStationName = pTransInfo->vehEntryInfo.sEnStaionName;

    VehEnInfo.enTollLane =
            QString("%1").arg(pTransInfo->vehEntryInfo.nEnLaneID, 3, 10, QLatin1Char('0'));

    VehEnInfo.enTollStationHex =
            pTransInfo->vehEntryInfo.sEnNetWorkIdHex + pTransInfo->vehEntryInfo.sEnStationHex;
    if (VehEnInfo.enTollStationHex.isEmpty()) {
        VehEnInfo.enTollStationHex = QString("00000000");
    }
    VehEnInfo.enTollLaneHex = VehEnInfo.enTollStationHex + pTransInfo->vehEntryInfo.sEnLaneHex;
    if (VehEnInfo.enTollLaneHex.isEmpty()) {
        VehEnInfo.enTollLaneHex = QString("0");
    }

    VehEnInfo.enTollLaneId = pTransInfo->vehEntryInfo.sEnGBLaneId;
    if (VehEnInfo.enTollLaneId.isEmpty()) {
        VehEnInfo.enTollLaneId = QString("G00000000000000000000");
    }
    VehEnInfo.enTollStationId = pTransInfo->vehEntryInfo.sEnGBStationId;
    if (VehEnInfo.enTollStationId.isEmpty()) {
        VehEnInfo.enTollStationId = QString("G0000000000000");
    }

    VehEnInfo.enTime = QDateTime2GBTimeStr(pTransInfo->vehEntryInfo.EnTime);
    VehEnInfo.entryOperatorID = pTransInfo->vehEntryInfo.dwEnOper;
    VehEnInfo.enVlp = GB2312toUnicode(pTransInfo->vehEntryInfo.szEnVLP).trimmed();
    if (VehEnInfo.enVlp.isEmpty()) {
        VehEnInfo.enVlp =
                QString::fromLocal8Bit(pTransInfo->VehInfo.szVehPlate).trimmed();  // QString("无车牌");
        if (VehEnInfo.enVlp.isEmpty()) {
            VehEnInfo.enVlp = QString("默A00000");
        }
    }
    VehEnInfo.enVlpc = pTransInfo->vehEntryInfo.bEnVLPC;
    if (!IsValidVehPlateColor(VehEnInfo.enVlpc)) {
        VehEnInfo.enVlpc = 0;  // VP_COLOR_UNKNOW;
    }
    VehEnInfo.enVehicleType = pTransInfo->vehEntryInfo.bEnVC;  //入口车型
    QString sClassName = GetVehClassName((CVehClass)VehEnInfo.enVehicleType);
    if (sClassName.isEmpty()) {
        VehEnInfo.enVehicleType = 1;
    }

    VehEnInfo.enVehicleClass = pTransInfo->vehEntryInfo.bEnVT;  //入口车种
    VehEnInfo.enWeight = pTransInfo->vehEntryInfo.dwTotalWeight;
    if (VehEnInfo.enWeight == 0xffffff) {
        VehEnInfo.enWeight = 1500;
    }
    /*
    if(VehEnInfo.enWeight < 1500 || VehEnInfo.enWeight > 4500)
        VehEnInfo.enWeight=1500;
        */
    VehEnInfo.enAxleCount = pTransInfo->vehEntryInfo.VehicalAxles;

    if (VehEnInfo.enAxleCount < 2) VehEnInfo.enAxleCount = 2;
    if (VehEnInfo.enAxleCount > VehWeight_MaxAlxeNum)
        VehEnInfo.enAxleCount = GetVehAxisNumByVC(pTransInfo->VehInfo.VehClass);

    VehEnInfo.enVehicleId = QString("%1_%2").arg(VehEnInfo.enVlp).arg(VehEnInfo.enVlpc);
    return true;
}

// ETC 车道不计费
bool CETCLaneCtrl::FillTollBaseInfoLane(CTransInfo *pTransInfo, CTollBaseInfo_Lane &TollBaseInfo)
{
    TollBaseInfo.Clear();
    TollBaseInfo.roadType = 1;                        //公路类型 1-高速公路 2-国道
    TollBaseInfo.overTime = pTransInfo->m_nOutTimes;  //超时时间（秒）

    if (pTransInfo) {
        TollBaseInfo.tollDistance = pTransInfo->m_nTotalMiles;
        TollBaseInfo.realDistance = pTransInfo->m_nTotalMiles;
    }
    TollBaseInfo.freeType = 0;  //免费区间类型
    TollBaseInfo.freeMode =
            0;  //免费方式 0-不免 1-全免TollBaseInfo.freeInfo =
    // QString::fromAscii(pTransInfo->VehTollInfo.m_FreeOrgList)          //免费区域信息
    TollBaseInfo.freeInfo.clear();
    return true;
}

bool CETCLaneCtrl::FillKeyInfo(CKeyInfo_Lane &keyInfo)
{
    MtcKeyProcessResultRecorder::instance()->getAndEraseKeyPressHistory(keyInfo.keyPressInfo,
                                                                        keyInfo.keyNum);
    return true;
}

bool CETCLaneCtrl::FillTollFeeInfoLane(CTransInfo *pTransInfo, CTollFeeInfo_Lane &TollFeeInfo)
{
    TollFeeInfo.Clear();
    //填门架通行费
    if (pTransInfo) {
        if (pTransInfo->actualFeeClass != FeeClass_None) {
            TollFeeInfo.payFee = pTransInfo->m_nTotalFee;
            TollFeeInfo.discountFee =
                    pTransInfo->m_nDiscountFee;  // pTransInfo->m_nTotalFee-pTransInfo->m_nTransFee;
            TollFeeInfo.fee = pTransInfo->m_nTransFee;  // GetConsumeMoney();
        }
    }

    TollFeeInfo.collectFee = 0;
    TollFeeInfo.rebateMoney = TollFeeInfo.discountFee;
    TollFeeInfo.cardCostFee = pTransInfo->m_nCardCost;
    TollFeeInfo.unpayFee = 0;  // TollFeeInfo.payFee -TollFeeInfo.discountFee - TollFeeInfo.fee;
    TollFeeInfo.unpayFlag = 0;
    TollFeeInfo.unpayCardCost = 0;
    TollFeeInfo.ticketFee = 0;
    TollFeeInfo.unifiedFee = 0;
    TollFeeInfo.enTollMoney =
            0;  // pTransInfo->favResultInfo.dwDutyMoney + pTransInfo->favResultInfo.dwFreeMoney;
    TollFeeInfo.enFreeMoney = 0;  // pTransInfo->favResultIn fo.dwFreeMoney;
    TollFeeInfo.enLastMoney = 0;  // TollFeeInfo.fee;//pTransInfo->favResultInfo.dwDutyMoney;

    qint32 dwLocalMoney = 0;    //本地
    qint32 dwConsumeMoney = 0;  //应收

    pTransInfo->GetAllMoneyInfo(TollFeeInfo.enTollMoney, TollFeeInfo.enFreeMoney,
                                TollFeeInfo.enLastMoney, dwLocalMoney, dwConsumeMoney,
                                TollFeeInfo.collectFee);

    TollFeeInfo.payType = pTransInfo->m_payType;

    //支付类型
    /*
        switch (pTransInfo->m_transPayType) {
            case TransPT_OBU:
                TollFeeInfo.payType = PayType_ETC;
                break;
            case TransPT_ETCCard:
                TollFeeInfo.payType = PayType_ETC;
                break;
            case TransPT_Cash:
                TollFeeInfo.payType = PayType_Cash;
                break;
            case TransPT_Other:
                TollFeeInfo.payType = PayType_Other;
                break;
            case TransPT_Union:
                TollFeeInfo.payType = PayType_Union;
                break;
            case TransPT_AliPay:
                TollFeeInfo.payType = PayType_Alipay;
                break;
            case TransPT_WeChat:
                TollFeeInfo.payType = PayType_WeChat;
                break;
            default:
                TollFeeInfo.payType = PayType_ETC;
                break;
        }*/
    TollFeeInfo.payOrderNum.clear();
    TollFeeInfo.payOrderNum = pTransInfo->m_payOrderNum;
    TollFeeInfo.payRebate =
            0;  // (qint32)(pTransInfo->m_nTransFee*100.0/pTransInfo->m_nTotalFee);//pTransInfo->VehTollInfo.wNormalRoadDiscount;

    pTransInfo->GetFeeInfoL(TollFeeInfo.disCode, TollFeeInfo.feeInfo1);
    return true;
}

bool CETCLaneCtrl::FillInvoiceInfoLane(CTransInfo *pTransInfo, CInvoiceInfo_Lane &invoiceInfo)
{
    invoiceInfo.Clear();
    if (!pTransInfo) return false;
    if (1) {
        if (pTransInfo->m_nInvoiceCnt > 0) {
            invoiceInfo.identifycation = 1;
            if (pTransInfo->m_payType == PayType_Cash) invoiceInfo.invoiceCnt = 1;
        } else {
            invoiceInfo.identifycation = 2;
        }
        /*
        if (pTransInfo->mediaType == MediaType_OBU) {
            invoiceInfo.identifycation = 2;
        } else {
            if (pTransInfo->m_transPayType == TransPT_ETCCard) {
                invoiceInfo.identifycation = 2;
            } else {
                if (pTransInfo->m_nTransFee > 0) {
                    invoiceInfo.identifycation = 1;
                } else
                    invoiceInfo.identifycation = 2;
            }
        }*/
    } else {
        if (pTransInfo->m_llInvoiceId > 0) {
            invoiceInfo.invoiceCode = QString("%1").arg(m_InvoiceInfo.GetCurrentCode());
            invoiceInfo.invoiceCnt = 1;
            invoiceInfo.invoiceId =
                    QString("%1").arg(pTransInfo->m_llInvoiceId, 8, 10, QLatin1Char('0'));

            invoiceInfo.identifycation = 1;
            invoiceInfo.invoiceType = QString("02");
        } else
            invoiceInfo.Clear();
    }
    return true;
}

bool CETCLaneCtrl::FiillTollIntervalInfo(CTransInfo *pTransInfo,
                                         CTollIntervalInfo_Lane &TollInterval)
{
    TollInterval.Clear();
    TollInterval.provinceGroup = QString("360201");

    if (!pTransInfo) return true;
    TollInterval.tollIntervalsCount = 0;
    TollInterval.gantryIdGroup = "";       // QString("000");
    TollInterval.tollIntervalsGroup = "";  // QString("00000000");
    TollInterval.transTimeGroup = "";
    TollInterval.chargefeeGroup = "";        // QString("0");
    TollInterval.chargeDisountGroup = "";    // QString("0");
    TollInterval.rateModeVersionGroup = "";  // QString("0");
    TollInterval.rateParaVersionGroup = "";  // QString("0");
    TollInterval.sectionGroup.clear();

    if (MediaType_CPC == pTransInfo->mediaType && !pTransInfo->m_bBadCard) {
        TollInterval.gantryIdGroup = pTransInfo->cpcIccInfo.cpcRoadInfo.FlagInfoList_After;
    }

    if (pTransInfo->actualFeeClass == FeeClass_Min) {
        if (pTransInfo->m_bRepay) {
            TollInterval.provinceGroup = QString("360201");
        } else {
            TollInterval.provinceGroup = pTransInfo->MinFeeInfo.provinceGroup;
            CMinFeeInfo_Province minFeeInfo;
            if (pTransInfo->MinFeeInfo.GetLocalProvInfo(minFeeInfo)) {
                TollInterval.tollIntervalsCount = minFeeInfo.tollIntervalsCount;
                TollInterval.tollIntervalsGroup = minFeeInfo.tollIntervalsGroup;

                if (pTransInfo->bIsFree) {
                    for (int i = 0; i < TollInterval.tollIntervalsCount; ++i) {
                        TollInterval.chargefeeGroup = TollInterval.chargefeeGroup + QString("|0");
                    }
                    if (TollInterval.chargefeeGroup.length() > 0)
                        TollInterval.chargefeeGroup.remove(0, 1);
                    TollInterval.chargeDisountGroup = minFeeInfo.chargeFeeGroup;
                } else {
                    TollInterval.chargefeeGroup = minFeeInfo.chargeFeeGroup;
                    TollInterval.chargeDisountGroup = minFeeInfo.discoutFeeGroup;
                }
                TollInterval.rateModeVersionGroup = minFeeInfo.rateModeVersionGroup;
                TollInterval.rateParaVersionGroup = minFeeInfo.rateParaVersionGroup;
                TollInterval.sectionGroup = minFeeInfo.sectionGroup;
                QString sTransTime = QDateTime2GBTimeStr(pTransInfo->TransTime);
                for (int i = 0; i < TollInterval.tollIntervalsCount; ++i) {
                    TollInterval.transTimeGroup += QString("|%1").arg(sTransTime);
                }
                if (TollInterval.transTimeGroup.length() > 0) {
                    TollInterval.transTimeGroup.remove(0, 1);
                    ;
                }
            }
            TollInterval.tollFeeGroup =
                    pTransInfo->MinFeeInfo.GetTollFeeGroup(pTransInfo->mediaType, pTransInfo->bIsFree);
        }
    } else if (pTransInfo->actualFeeClass == FeeClass_OBU) {
        TollInterval.provinceGroup = QString("360201");
        TollInterval.tollFeeGroup =
                pTransInfo->IccInfo.ef04Info.GetTollFeeGroup(pTransInfo->bIsFree);
        TollInterval.tollIntervalsGroup = pTransInfo->gantryFeeInfo.tollIntervalIDs;
        TollInterval.tollIntervalsCount = pTransInfo->gantryFeeInfo.tollIntervalsCount;
        TollInterval.chargefeeGroup = pTransInfo->gantryFeeInfo.feeGroup;
        TollInterval.chargeDisountGroup = pTransInfo->gantryFeeInfo.discountFeeGroup;
        TollInterval.rateModeVersionGroup.clear();
        TollInterval.rateParaVersionGroup.clear();
        TollInterval.sectionGroup.clear();
        QString sTransTime = QDateTime2GBTimeStr(pTransInfo->TransTime);
        for (int i = 0; i < TollInterval.tollIntervalsCount; ++i) {
            TollInterval.transTimeGroup += QString("|%1").arg(sTransTime);
        }
        if (TollInterval.transTimeGroup.length() > 0) {
            TollInterval.transTimeGroup.remove(0, 1);
            ;
        }

    } else if (pTransInfo->actualFeeClass == FeeClass_Card) {
        QString sProvinsGroup;
        TollInterval.tollFeeGroup = pTransInfo->cpcIccInfo.cpcTollCellInfo_After.GetTollFeeGroup(
                    sProvinsGroup, pTransInfo->bIsFree);
        TollInterval.provinceGroup = sProvinsGroup;
        TollInterval.tollIntervalsCount = pTransInfo->gantryFeeInfo.tollIntervalsCount;
        TollInterval.tollIntervalsGroup = pTransInfo->gantryFeeInfo.tollIntervalIDs;
        TollInterval.chargefeeGroup = pTransInfo->gantryFeeInfo.feeGroup;
        TollInterval.rateModeVersionGroup.clear();
        TollInterval.rateParaVersionGroup.clear();
        TollInterval.sectionGroup.clear();
        QString sTransTime = QDateTime2GBTimeStr(pTransInfo->TransTime);
        for (int i = 0; i < TollInterval.tollIntervalsCount; ++i) {
            TollInterval.transTimeGroup += QString("|%1").arg(sTransTime);
        }
        if (TollInterval.transTimeGroup.length() > 0) {
            TollInterval.transTimeGroup.remove(0, 1);
            ;
        }
    } else if (pTransInfo->actualFeeClass == FeeClass_MinistryCenter ||
               FeeClass_ProvCenter == pTransInfo->actualFeeClass) {
        CProvinceFee provFee;
        QString sFeeGroup;
        QString sProvinceGroup;

        if (pTransInfo->m_fareQryResult.GetProvFeeInfo(pTransInfo->mediaType, pTransInfo->bIsFree,
                                                       provFee, sFeeGroup, sProvinceGroup)) {
            TollInterval.tollFeeGroup = sFeeGroup;
            TollInterval.tollIntervalsCount = pTransInfo->m_fareQryResult.GetTollIntervalInfo(
                        TollInterval.tollIntervalsGroup,
                        TollInterval.transTimeGroup);  // provFee.tollIntervalsCount;

            QString sChargePayFeeGroup;
            pTransInfo->m_fareQryResult.GetFeeGroup(
                        pTransInfo->bIsFree, sChargePayFeeGroup, TollInterval.chargefeeGroup,
                        TollInterval.chargeDisountGroup, TollInterval.rateModeVersionGroup,
                        TollInterval.rateParaVersionGroup);

            TollInterval.provinceGroup = sProvinceGroup;
        }
    }

    return true;
}

bool CETCLaneCtrl::FiillTollIntervalInfo_Repay(CTransInfo *pTransInfo,
                                               CTollIntervalInfo_Lane &TollInterval)
{
    TollInterval.Clear();
    TollInterval.provinceGroup = QString("360201");

    if (!pTransInfo) return true;
    TollInterval.tollIntervalsCount = 0;
    TollInterval.gantryIdGroup = "";       // QString("000");
    TollInterval.tollIntervalsGroup = "";  // QString("00000000");
    TollInterval.transTimeGroup = "";
    TollInterval.chargefeeGroup = "";        // QString("0");
    TollInterval.chargeDisountGroup = "";    // QString("0");
    TollInterval.rateModeVersionGroup = "";  // QString("0");
    TollInterval.rateParaVersionGroup = "";  // QString("0");
    TollInterval.sectionGroup.clear();

    TollInterval.tollIntervalsCount = pTransInfo->gantryFeeInfo.tollIntervalsCount;
    TollInterval.gantryIdGroup = pTransInfo->m_curGantryInfo.sGantryId;
    TollInterval.tollFeeGroup = QString("%1").arg(pTransInfo->m_nTransFee);
    TollInterval.tollIntervalsGroup = pTransInfo->gantryFeeInfo.tollIntervalIDs;
    TollInterval.chargefeeGroup = pTransInfo->gantryFeeInfo.feeGroup;
    TollInterval.chargeDisountGroup = pTransInfo->gantryFeeInfo.discountFeeGroup;
    TollInterval.rateModeVersionGroup.clear();
    TollInterval.rateParaVersionGroup.clear();
    TollInterval.sectionGroup.clear();
    QString sTransTime = QDateTime2GBTimeStr(pTransInfo->TransTime);
    for (int i = 0; i < TollInterval.tollIntervalsCount; ++i) {
        TollInterval.transTimeGroup += QString("|%1").arg(sTransTime);
    }
    if (TollInterval.transTimeGroup.length() > 0) {
        TollInterval.transTimeGroup.remove(0, 1);
    }

    return true;
}

bool CETCLaneCtrl::FillIntervalInfo_Lane(const QString &sId, CTransInfo *pTransInfo,
                                         QList<CSplitProvince_Lane> &splitInfo)
{
    splitInfo.clear();
    CSplitProvince_Lane ProvinceInfo;
    int nId = 0;
    if (!pTransInfo) return false;
    if (pTransInfo->actualFeeClass == FeeClass_Min) {
        if (pTransInfo->m_bRepay) {
            ProvinceInfo.Clear();
            ProvinceInfo.id = sId;
            ProvinceInfo.modifyFlag = 1;
            ProvinceInfo.sn = nId;
            ProvinceInfo.provinceId =
                    QString("%1").arg(36, 2, 10, QLatin1Char('0')) + QString("0201");
            ProvinceInfo.tollSupport = 4;
            ProvinceInfo.tollFee = pTransInfo->m_nTransFee;
            splitInfo.push_back(ProvinceInfo);
            return true;
        }
        QList<CMinFeeInfo_Province>::iterator it = pTransInfo->MinFeeInfo.provMinFeeGroup.begin();
        for (; it != pTransInfo->MinFeeInfo.provMinFeeGroup.end(); ++it) {
            ProvinceInfo.Clear();
            ProvinceInfo.id = sId;
            ProvinceInfo.modifyFlag = 1;
            ProvinceInfo.sn = nId;
            nId++;
            ProvinceInfo.provinceId =
                    QString("%1").arg(it->tollProvinceId, 2, 10, QLatin1Char('0')) + QString("0201");
            ProvinceInfo.tollSupport = 4;  //

            if (pTransInfo->mediaType == MediaType_OBU)
                ProvinceInfo.tollFee = it->tollFee95;
            else
                ProvinceInfo.tollFee = it->tollFee;

            if (pTransInfo->bIsFree) {
                if (pTransInfo->m_nDiscountType != 0x07 && pTransInfo->m_nDiscountType != 0x02 &&
                        pTransInfo->m_nDiscountType != 0x01)
                    ProvinceInfo.tollFee = 0;
            }
            ProvinceInfo.tollIntervalsCount = it->tollIntervalsCount;
            ProvinceInfo.tollIntervalsGroup = it->tollIntervalsGroup;

            QString sTransTime = QDateTime2GBTimeStr(pTransInfo->TransTime);
            for (int i = 0; i < it->tollIntervalsCount; ++i) {
                ProvinceInfo.transTimeGroup += QString("|%1").arg(sTransTime);
            }
            if (ProvinceInfo.transTimeGroup.length() > 0) {
                ProvinceInfo.transTimeGroup.remove(0, 1);
            }

            ProvinceInfo.chargePayGroup = it->chargeFeeGroup;
            if (pTransInfo->bIsFree) {
                ProvinceInfo.chargeFeeGroup = it->discoutFeeGroup;
                ProvinceInfo.chargeDiscountGroup = it->chargeFeeGroup;
            } else {
                ProvinceInfo.chargeDiscountGroup = it->discoutFeeGroup;
                ProvinceInfo.chargeFeeGroup = it->chargeFeeGroup;
            }

            ProvinceInfo.rateModeVersionGroup = it->rateModeVersionGroup;
            ProvinceInfo.rateVersionGroup = it->rateVersionGroup;
            ProvinceInfo.rateParaVersionGroup = it->rateParaVersionGroup;
            ProvinceInfo.sectionGroup = it->sectionGroup;
            splitInfo.push_back(ProvinceInfo);
        }
        return true;
    } else if (FeeClass_OBU == pTransInfo->actualFeeClass) {
        if (pTransInfo->IccInfo.ef04Info.provinceFeeGroup.isEmpty()) return true;
        if (pTransInfo->IccInfo.ef04Info.bProvinceCount_After !=
                pTransInfo->IccInfo.ef04Info.provinceFeeGroup.size()) {
            return true;
            ;
        }
        QList<CProvinceFeeInfo>::iterator it =
                pTransInfo->IccInfo.ef04Info.provinceFeeGroup.begin();
        for (; it != pTransInfo->IccInfo.ef04Info.provinceFeeGroup.end(); ++it) {
            ProvinceInfo.Clear();
            ProvinceInfo.id = sId;
            ProvinceInfo.sn = nId;
            nId++;
            ProvinceInfo.modifyFlag = 1;
            ProvinceInfo.provinceId =
                    QString("%1").arg(it->proviceId, 2, 16, QLatin1Char('0')) + QString("0201");
            ProvinceInfo.tollSupport = 3;  //按OBU内的折扣后金额
            ProvinceInfo.tollFee = it->provinceFee;

            if (pTransInfo->bIsFree) {
                if (pTransInfo->m_nDiscountType != 0x07 && pTransInfo->m_nDiscountType != 0x01 &&
                        pTransInfo->m_nDiscountType != 0x02)
                    ProvinceInfo.tollFee = 0;
            }

            splitInfo.push_back(ProvinceInfo);
        }
    } else if (FeeClass_Card == pTransInfo->actualFeeClass) {
        int i = 0;
        COrgBasicInfoTable *pOrgBasicTable =
                (COrgBasicInfoTable *)CParamFileMgr::GetParamFile(cfOrgBasicInfo);
        foreach (CProvCellInfo value,
                 pTransInfo->cpcIccInfo.cpcTollCellInfo_After.ProCellInfoList) {
            ProvinceInfo.Clear();
            ProvinceInfo.id = sId;
            ProvinceInfo.sn = nId;
            nId++;
            ProvinceInfo.modifyFlag = 1;
            ProvinceInfo.provinceId =
                    QString("%1").arg(value.bProv, 2, 10, QLatin1Char('0')) + QString("0201");
            ProvinceInfo.tollSupport = 3;
            ProvinceInfo.tollFee = value.nProvTollMoney;
            if (pTransInfo->bIsFree) {
                if (pTransInfo->m_nDiscountType != 0x07 && pTransInfo->m_nDiscountType != 0x01 &&
                        pTransInfo->m_nDiscountType != 0x02)
                    ProvinceInfo.tollFee = 0;
            }

            ProvinceInfo.tollIntervalsCount = 0;
            ProvinceInfo.tollIntervalsGroup.clear();
            ProvinceInfo.chargePayGroup = QString("%1").arg(value.nPayProvTollMoney);

            ProvinceInfo.chargeFeeGroup = QString("%1").arg(value.nProvTollMoney);
            ProvinceInfo.chargeDiscountGroup =
                    QString("%1").arg(value.nPayProvTollMoney - value.nProvTollMoney);
            if (pTransInfo->bIsFree) {
                if (pTransInfo->m_nDiscountType != 0x07 && pTransInfo->m_nDiscountType != 0x01 &&
                        pTransInfo->m_nDiscountType != 0x02) {
                    ProvinceInfo.chargeFeeGroup = QString("%1").arg(0);
                    ProvinceInfo.chargeDiscountGroup = QString("%1").arg(value.nPayProvTollMoney);
                }
            }
            QDateTime enTime;
            UnixTime2QDateTime_GB(value.nPassTime, enTime);
            ProvinceInfo.enTime = QDateTime2GBTimeStr(enTime);
            QDateTime exTime;
            UnixTime2QDateTime_GB(value.nNewPassTime, exTime);
            ProvinceInfo.exTime = QDateTime2GBTimeStr(exTime);
            ProvinceInfo.sectionName.clear();
            ProvinceInfo.enPointId = value.sEnFlag;
            if (value.sEnFlag == QString("000000") || value.sEnFlag == QString("FFFFFF")) {
                if (0 == i) {
                    ProvinceInfo.enPointId = pTransInfo->vehEntryInfo.sEnGBStationId;
                    ProvinceInfo.enTollStationName = pTransInfo->vehEntryInfo.sEnStaionName;
                } else {
                    ProvinceInfo.enPointId = value.sEnFlag;
                }
            } else {
                if (pOrgBasicTable) {
                    COrgBasicInfo orgBasicInfo;
                    bool bRlt = pOrgBasicTable->QryOrgBasicInfo(value.bProv, ORG_TYPE_GANTRY_GB,
                                                                value.sEnFlag, orgBasicInfo);
                    if (!bRlt) {
                        ErrorLog(QString("分省信息，查询入口标识点%1信息失败").arg(value.sEnFlag));
                        ProvinceInfo.enPointId = pTransInfo->vehEntryInfo.sEnGBStationId;
                        ProvinceInfo.enTollStationName = pTransInfo->vehEntryInfo.sEnStaionName;
                    } else {
                        ProvinceInfo.enPointId = orgBasicInfo.sId;
                        ProvinceInfo.enTollStationName = orgBasicInfo.sName;
                    }
                }
            }
            if (pOrgBasicTable) {
                COrgBasicInfo orgBasicInfo;
                orgBasicInfo.Clear();
                bool bRlt = pOrgBasicTable->QryOrgBasicInfo(value.bProv, ORG_TYPE_GANTRY_GB,
                                                            value.sNewFlag, orgBasicInfo);
                if (!bRlt) {
                    ErrorLog(QString("分省信息，查询入口标识点%1信息失败").arg(value.sEnFlag));
                    ProvinceInfo.exPointId = value.sNewFlag;
                } else {
                    ProvinceInfo.exPointId = orgBasicInfo.sId;
                    ProvinceInfo.exTollStationName = orgBasicInfo.sName;
                }
            }

            i++;
            splitInfo.push_back(ProvinceInfo);
        }
    } else if (FeeClass_MinistryCenter == pTransInfo->actualFeeClass ||
               FeeClass_ProvCenter == pTransInfo->actualFeeClass) {
        foreach (CProvinceFee value, pTransInfo->m_fareQryResult.provinceFees) {
            ProvinceInfo.Clear();
            ProvinceInfo.id = sId;
            ProvinceInfo.sn = nId;
            ++nId;
            ProvinceInfo.modifyFlag = 1;
            ProvinceInfo.provinceId =
                    QString("%1").arg(value.provinceId, 2, 10, QLatin1Char('0')) + QString("0201");
            ProvinceInfo.tollSupport = value.tollSupport;
            if (ProvinceInfo.tollSupport != 1 && ProvinceInfo.tollSupport != 2) {
                if (FeeClass_ProvCenter == pTransInfo->actualFeeClass)
                    ProvinceInfo.tollSupport = 1;
                else
                    ProvinceInfo.tollSupport = 2;
            }

            int nFee = 0;
            /*
            if (MediaType_OBU == pTransInfo->mediaType)
                nFee = value.fee;
            else*/
            nFee = value.GetFee();
            ProvinceInfo.tollFee = nFee;

            ProvinceInfo.tollIntervalsCount = value.tollIntervalsCount;
            ProvinceInfo.tollIntervalsGroup = value.tollIntervalsGroup;
            ProvinceInfo.transTimeGroup = value.transTimeGroup;

            ProvinceInfo.chargeDiscountGroup = value.discountFeeGroup;
            ProvinceInfo.chargeFeeGroup = value.feeGroup;

            if (pTransInfo->bIsFree) {
                if (pTransInfo->m_nDiscountType != 0x07 && pTransInfo->m_nDiscountType != 0x01 &&
                        pTransInfo->m_nDiscountType != 0x02) {
                    ProvinceInfo.tollFee = 0;
                    ProvinceInfo.chargeFeeGroup = value.feeGroupFree;
                    ProvinceInfo.chargeDiscountGroup = value.feeGroup;
                }
            }

            ProvinceInfo.chargePayGroup = value.chargePayGroup;
            ProvinceInfo.enTime.clear();
            ProvinceInfo.exTime.clear();
            ProvinceInfo.sectionGroup.clear();
            ProvinceInfo.sectionName.clear();
            splitInfo.push_back(ProvinceInfo);
        }
    }
    return true;
}

bool CETCLaneCtrl::FillIntervalInfo_Lane_Repay(const QString &sId, CTransInfo *pTransInfo,
                                               const CTollIntervalInfo_Lane &TollInterval,
                                               QList<CSplitProvince_Lane> &splitInfo)
{
    splitInfo.clear();
    CSplitProvince_Lane ProvinceInfo;
    int nId = 0;
    if (!pTransInfo) return false;
    ProvinceInfo.Clear();
    ProvinceInfo.id = sId;
    ProvinceInfo.modifyFlag = 1;
    ProvinceInfo.sn = nId;
    ProvinceInfo.provinceId = QString("360201");
    ProvinceInfo.tollSupport = 4;
    ProvinceInfo.tollFee = pTransInfo->m_nTransFee;
    ProvinceInfo.tollIntervalsCount = TollInterval.tollIntervalsCount;
    ProvinceInfo.tollIntervalsGroup = TollInterval.tollIntervalsGroup;
    ProvinceInfo.chargePayGroup = TollInterval.chargefeeGroup;
    ProvinceInfo.chargeDiscountGroup = TollInterval.chargeDisountGroup;
    ProvinceInfo.chargeFeeGroup = TollInterval.chargefeeGroup;
    ProvinceInfo.transTimeGroup = TollInterval.transTimeGroup;
    ProvinceInfo.exTime = QDateTime2GBTimeStr(pTransInfo->TransTime);
    splitInfo.push_back(ProvinceInfo);
    return true;
}

QString SplitFlagInfo(const QString &sFlag)
{
    int nLen = sFlag.length();
    int nCnt = nLen / 7;
    QString stmp;
    QString sResult;
    sResult.clear();
    for (int i = 0; i < nCnt; ++i) {
        stmp = sFlag.mid(i * 7, 7).right(3);
        sResult += stmp;
    }
    return sResult;
}

//判断是否允许后续车辆通行
bool CETCLaneCtrl::bAllowllContinuePass(bool bDownBar)
{
    CTransInfo *pTransInfo = NULL;
    {
        QMutexLocker locker(&m_transInfoMt);
        int nSize = m_VehQueue.size();
        if (nSize > 0) {
            pTransInfo = m_VehQueue.front();
        }
        if (pTransInfo) {
            if (pTransInfo->bTransOk()) {
                DebugLog(QString("车辆队列有车,继续放行"));
                return true;
            } else {
                if (bDownBar) {
                    SetRefusePass();
                }
                return false;
            }
        }
    }
    //只要是交易成功就允许放行
    {
        QMutexLocker locker(&m_WaitTransMt);
        if (0 == m_VehWaitQueue.size()) {
            if (bDownBar) {
                SetRefusePass();
            }
            return false;
        }
        pTransInfo = m_VehWaitQueue.front();
        if (pTransInfo) {
            if (pTransInfo->bTransOk()) {
                DebugLog(QString("等待队列有车,继续放行"));
                return true;
            } else {
                if (bDownBar) {
                    SetRefusePass();
                }
                return false;
            }
        }
    }
    return false;
}

void CETCLaneCtrl::SetLastLoopStatus(int ndevId, bool bStatus, QString sAllStatus)
{
    QMutexLocker locker(&m_LoopStatusMt);
    m_sAllLoopStatus = sAllStatus;
    QString sStatus;
    if (ndevId == DI_LoopDetect) {
        sStatus = bStatus ? "11" : "10";
    } else if (DI_LoopFront == ndevId) {
        sStatus = bStatus ? "21" : "20";
    } else {
        return;
    }
    if (sStatus == m_sLastLoopStatus.right(2)) {
        return;
    }
    m_sLastLoopStatus.append(sStatus);
    m_sLastLoopStatus.remove(0, 2);
    return;
}

void CETCLaneCtrl::StartDownBarTimer()
{
    QMutexLocker locker(&m_DownBarMt);

    if (m_pDownBarTimer->isActive()) {
        m_pDownBarTimer->stop();
    }
    m_pDownBarTimer->start(50);
}

void CETCLaneCtrl::AddHaveTransVeh(const CTransInfo &transInfo)
{
    QMutexLocker locker(&m_lockHaveTransVehMt);
    CTransInfo tmpTransInfo = transInfo;
    tmpTransInfo.m_InQueTime = QDateTime::currentDateTime().toTime_t();
    m_HaveTransVeh.push_front(tmpTransInfo);
    while (m_HaveTransVeh.size() > 10) {
        m_HaveTransVeh.pop_back();
    }
    return;
}

void CETCLaneCtrl::StartVehLeaveTimer()
{
    QMutexLocker locker(&m_vehLeaveMx);
    if (m_vehLeaveTimer.isActive()) m_vehLeaveTimer.stop();
    m_vehLeaveTimer.start(30000);
}

void CETCLaneCtrl::StopVehLeaveTimer()
{
    QMutexLocker locker(&m_vehLeaveMx);
    if (m_vehLeaveTimer.isActive()) m_vehLeaveTimer.stop();
}

void CETCLaneCtrl::StartVehDetectTimer()
{
    QMutexLocker locker(&m_vehDetectMt);
    if (m_vehDetectTimer.isActive()) m_vehDetectTimer.stop();
    m_vehDetectTimer.start(Ptr_Info->GetMaxTimeOnBackLoop());
}

void CETCLaneCtrl::StopVehDetectTimer()
{
    QMutexLocker locker(&m_vehDetectMt);
    if (m_vehDetectTimer.isActive()) m_vehDetectTimer.stop();
}

// void CETCLaneCtrl::SaveQrCode(const CTransInfo &transInfo)
//{
//    if (Ptr_Info->IsEntryLane()) return;
//    if (Ptr_Info->IsETCLane()) return;
//    if (isTruck(transInfo.VehInfo.VehClass)) {
//        m_qrCodeInfo.vehicleId = QString("%1_%2")
//                                     .arg(GB2312toUnicode(transInfo.VehInfo.szVehPlate))
//                                     .arg(transInfo.VehInfo.nVehPlateColor);
//        m_qrCodeInfo.enStationId = transInfo.vehEntryInfo.sEnGBStationId;
//        m_qrCodeInfo.exStationId = Ptr_Info->GetGBStationId();
//        m_qrCodeInfo.enWeight = transInfo.vehEntryInfo.dwTotalWeight;
//        m_qrCodeInfo.exWeight = transInfo.m_dwToTalWeight;
//        m_qrCodeInfo.mediaType = transInfo.mediaType;
//        m_qrCodeInfo.transactionId = transInfo.m_sId;
//        m_qrCodeInfo.passId = transInfo.GetPassId();
//        m_qrCodeInfo.exTime = QDateTime2GBTimeStr(transInfo.TransTime);
//        m_qrCodeInfo.transPayType = transInfo.m_transPayType;
//        m_qrCodeInfo.fee = transInfo.m_nTransFee;
//        m_qrCodeInfo.payFee = transInfo.m_nTotalFee;
//        m_qrCodeInfo.vehicleSign =
//            QString("0x%1").arg(transInfo.m_bVehState, 2, 16, QLatin1Char('0'));
//        //
//        DebugLog(QString("发送二维码"));

//        emit NotifyRefreshQrCode(false);
//    }
//}

bool CETCLaneCtrl::SaveAutoVLPResultToVehQue(int nDevIndex, const CAutoRegInfo &regInfo,
                                             bool bWhiteList)
{
    bool bSaved = false;
    int nQueSize = 0;
    CTransInfo *pTransInfo = NULL;
    {
        QMutexLocker locker(&m_transInfoMt);
        nQueSize = m_VehQueue.size();
        if (0 < nQueSize) {
            pTransInfo = m_VehQueue.last();
            if (pTransInfo->mediaType == MediaType_OBU) {
                if (pTransInfo->m_nRsuIndex == DevIndex_First &&
                        pTransInfo->AutoRegInfo.id.isEmpty()) {
                    DebugLog("将抓拍图片保存进车道交易信息中");
                    pTransInfo->AutoRegInfo = regInfo;
                    bSaved = true;
                }
            }
        }
    }

    if (bSaved) {
        if (pTransInfo && !pTransInfo->bTransOk()) {
            if (bWhiteList && CParamFileMgr::IsWhiteListVeh(regInfo.sAutoVehPlate)) {
                DebugLog(QString("车牌识别%1为公务车").arg(regInfo.sAutoVehPlate));
                CRsuOpResult opResult;
                if (1 == nQueSize) {
                    DebugLog(QString("公务车自动放行").arg(regInfo.sAutoVehPlate));
                    m_lastTransInfo.ClearTransInfo();
                    CVehInfo vehInfo;
                    vehInfo.VehClass = VC_Car1;
                    QByteArray bPlate = UnicodetoGB2312(regInfo.sAutoVehPlate);
                    qsnprintf(vehInfo.szVehPlate, sizeof vehInfo.szVehPlate, "%s", bPlate.data());
                    vehInfo.GBVehType = UVT_Normal;
                    m_lastTransInfo.SetVehInfo(&vehInfo);
                    m_lastTransInfo.CompleteTrans(nDevIndex, TransPT_OBU, &opResult, Tr_Successed,
                                                  CTransInfo::Ts_Finished);
                    CompleteTrans(Tr_Successed);
                }
            }
        }
        return true;
    }

    {
        QMutexLocker waitlocker(&m_WaitTransMt);
        if (!m_VehWaitQueue.isEmpty()) {
            pTransInfo = m_VehWaitQueue.first();
            if (pTransInfo->AutoRegInfo.id.isEmpty()) {
                DebugLog("将抓拍图片保存进等待交易信息中");
                pTransInfo->AutoRegInfo = regInfo;
                return true;
            }
        }
    }
    return false;
}

bool CETCLaneCtrl::IsTheLastVeh(const QString &sVehplate, int nColor)
{
    QMutexLocker locker(&m_lastVehMt);
    if (sVehplate.isEmpty()) return false;
    if (sVehplate == m_lastVehInfo.sAutoVehPlate && nColor == m_lastVehInfo.nAutoVLPColor) {
        QDateTime curTime = QDateTime::currentDateTime();
        if (m_lastVehInfo.AuRegTime.secsTo(curTime) < 5) {
            return true;
        }
    }
    return false;
}

QString CETCLaneCtrl::GetLastLoopStatus()
{
    QMutexLocker locker(&m_LoopStatusMt);
    return m_sLastLoopStatus.left(2);
}

QString CETCLaneCtrl::GetCurLoopStatus()
{
    QMutexLocker locker(&m_LoopStatusMt);
    return m_sLastLoopStatus.right(2);
}

//该函数只是队列中最新的车辆，bTransOk=true 时，取得是交易成功但没保存的车辆
CTransInfo *CETCLaneCtrl::GetLastWasteOfNotSaved(bool bTransOk)
{
    QMutexLocker locker(&m_transInfoMt);
    if (m_VehQueue.isEmpty()) return NULL;

    QList<CTransInfo *>::iterator it = m_VehQueue.end() - 1;
    for (; it >= m_VehQueue.begin(); --it) {
        CTransInfo *pTransInfo = *it;
        if (bTransOk) {
            if ((Tr_Failed < pTransInfo->transResult) &&
                    (pTransInfo->transState == CTransInfo::Ts_WaitToSave)) {
                return pTransInfo;
            }
        } else {
            return pTransInfo;
        }
    }
    return NULL;
}

CTransInfo *CETCLaneCtrl::PopTransInfoFromFrontList()
{
    CTransInfo *pTransInfo = NULL;
    {
        QMutexLocker locker(&m_WaitTransMt);
        if (m_VehWaitQueue.isEmpty()) return NULL;
        pTransInfo = m_VehWaitQueue.first();
        m_VehWaitQueue.pop_front();
    }
    emit NotifyFrontVehQueChanged();

    return pTransInfo;
}

void CETCLaneCtrl::AddToReserveQue(const CTransInfo *pTransInfo)
{
    if (!pTransInfo) return;

    CTransInfo transInfo = *pTransInfo;
    QMutexLocker locker(&m_ReserveMt);
    m_ReservedQue.clear();
    m_ReservedQue.push_front(transInfo);
    return;
}

//增加车辆进等待车辆队列,bReserved 表示倒车
CTransInfo *CETCLaneCtrl::AddNewTransInfoToFrontList(const CTransInfo &TransInfo, bool bReserved)
{
    CTransInfo *pTransInfo = new CTransInfo(TransInfo);
    {
        QMutexLocker locker(&m_WaitTransMt);
        pTransInfo->m_InQueTime = QDateTime::currentDateTime().toTime_t();
        if (bReserved) {
            m_VehWaitQueue.push_front(pTransInfo);
        } else {
            if (m_VehWaitQueue.size() > 0) {
                CTransInfo *pFirstTransInfo = m_VehWaitQueue.front();
                if (pFirstTransInfo->m_bReserved && (pFirstTransInfo->m_InQueTime > 0)) {
                    qint64 subTime = pTransInfo->m_InQueTime - pFirstTransInfo->m_InQueTime;
                    if (subTime > 20) {
                        DebugLog(QString("倒车车辆,队列保存时间:%1秒,自动清除").arg(subTime));
                        SaveLastTransInfo(0, pFirstTransInfo, false);
                        m_VehWaitQueue.pop_front();
                        delete pFirstTransInfo;
                    }
                }
            }
            m_VehWaitQueue.push_back(pTransInfo);
        }
    }
    emit NotifyFrontVehQueChanged();
    return pTransInfo;
}

//增加车辆进过车队列，如果pTransInfo=NULL,则表示增加一辆异常车，异常车只有到压后线圈时才保存
void CETCLaneCtrl::AddTransVehToList(int nInductCnt, CTransInfo *pTransInfo, bool AddToFront)
{
    CTransInfo *ptmpTransInfo = NULL;
    if (!pTransInfo) {
        ptmpTransInfo = new CTransInfo();
        ptmpTransInfo->CompleteTrans(DevIndex_Manual, TransPT_OBU, NULL, Tr_Failed);
    } else
        ptmpTransInfo = pTransInfo;

    {
        ptmpTransInfo->SetDetectCnt(nInductCnt);
        ptmpTransInfo->m_InQueTime = QDateTime::currentDateTime().toTime_t();
        QMutexLocker locker(&m_transInfoMt);
        if (AddToFront)
            m_VehQueue.push_front(ptmpTransInfo);
        else
            m_VehQueue.push_back(ptmpTransInfo);
        DebugLog(QString("过车队列增加车辆,车辆数[%1],交易状态:%2,result:%3")
                 .arg(m_VehQueue.size())
                 .arg(ptmpTransInfo->transState)
                 .arg(ptmpTransInfo->transResult));
    }
    emit NotifyVehQueChanged();
}

void CETCLaneCtrl::AddTransVehToListFromFrontQueue(int nInductCnt, CTransInfo *pTransInfo)
{
    if (!pTransInfo) {
        return;
    }

    {
        CTransInfo *ptmpTransInfo = pTransInfo;
        pTransInfo->SetDetectCnt(nInductCnt);
        pTransInfo->m_InQueTime = QDateTime::currentDateTime().toTime_t();
        QMutexLocker locker(&m_transInfoMt);
        CTransInfo *pLastTransInfo = NULL;
        if (m_VehQueue.size() > 0) pLastTransInfo = m_VehQueue.last();
        if (pLastTransInfo) {
            if (ptmpTransInfo->dwOBUID > 0 && ptmpTransInfo->dwOBUID == pLastTransInfo->dwOBUID) {
                //*pLastTransInfo = *ptmpTransInfo;
                DebugLog(QString("前天线交易车辆进入队列,更新之前保存车辆信息%1")
                         .arg(ptmpTransInfo->dwOBUID));
                m_VehQueue.pop_back();
                delete pLastTransInfo;
            }
        }
        m_VehQueue.push_back(ptmpTransInfo);
        DebugLog(QString("过车队列增加车辆,车辆数[%1],交易状态:%2,result:%3")
                 .arg(m_VehQueue.size())
                 .arg(ptmpTransInfo->transState)
                 .arg(ptmpTransInfo->transResult));
    }
    emit NotifyVehQueChanged();
}

bool CETCLaneCtrl::bHaseAbnormalVehInQueue(QString &sError, quint32 &dwOBUID)
{
    QMutexLocker lock(&m_transInfoMt);
    if (m_VehQueue.isEmpty()) return false;

    QList<CTransInfo *>::iterator it = m_VehQueue.begin();
    for (; it != m_VehQueue.end(); ++it) {
        if (!(*it)->bTransOk()) {
            sError = QString("车道内有未交易车辆");
            dwOBUID = (*it)->dwOBUID;
            return true;
        }
    }
    return false;
}

bool CETCLaneCtrl::bAbnormalVehOfTheFirstVehInQueue(CTransInfo &transInfo)
{
    QMutexLocker locker(&m_transInfoMt);
    if (m_VehQueue.isEmpty())
    {
        return false;
    }
    CTransInfo *pTransInfo = m_VehQueue.front();
    transInfo = *pTransInfo;
    if (!pTransInfo->bTransOk()) return true;
    return false;
}

/*
 *校验是否有异常车
 * 返回值： 3-车道内已经交易车辆过多，暂停交易  2-如果队列内有异常车，则返回2
 *1-如果前线圈有车，返回1
 */
int CETCLaneCtrl::HaveDisturbCar(int nDevIndex, quint32 dwOBUId, const CVehPosInfo *pVehPos,
                                 QString &sError)
{
    if (nDevIndex != DevIndex_First) {
        return 0;
    }

    if (dwOBUId > 0) {
        //车道内有异常车辆
        if (GetAllVehCountInQue() >= 5) {
            sError = QString("车道内车辆过多,请稍后");
            return 3;
        }

        /*
        quint32 dwTmpOBUId = 0;
        CRsuDev *pRsuDev = CDeviceFactory::GetRsuDev(DevIndex_Second);
        if (!pRsuDev || (!pRsuDev->bIniteOk()) || (PSAM_NORMAL != pRsuDev->GetPsamStatus())) {
            if (bHaseAbnormalVehInQueue(sError, dwTmpOBUId)) {
                DebugLog("后天线异常,车道内有未交易车辆,前天线不允许交易");
                return 2;
            }
        }
        */

        CIOCard *pIOCard = CDeviceFactory::GetIoCard();

        bool bHaveAbnormalVeh = false;

        CIODevStatus frontStatus, detectStatus;
        pIOCard->GetDIPortStatus(DI_LoopFront, frontStatus);
        pIOCard->GetDIPortStatus(DI_LoopDetect, detectStatus);
        if (detectStatus.bStatus) {
            //双天线都按光栅处理，因为不判断队列内异常车，所以光栅1有信号，不管光栅2有没有信号，都缺省认为前车干扰，不予处理避免队列错误
            // if (!frontStatus.bStatus) {
            DebugLog(QString("光栅1有车,跟车干扰"));
            // qint64 curTime = QDateTime::currentMSecsSinceEpoch();
            bHaveAbnormalVeh = true;
            /*
            if (curTime > detectStatus.nTrigerTime &&
                curTime - detectStatus.nTrigerTime > Ptr_Info->GetDetectTimes()) {
                sError = QString("光栅1有车");
                DebugLog(QString("光栅1有车,当前时间：%1,检测线圈时间:%2")
                             .arg(curTime)
                             .arg(detectStatus.nTrigerTime));
                bHaveAbnormalVeh = true;
            }*/
            //}
        }
        //前天先交易，不管队列内是否有异常车，但是如果此时光栅上刚好有车，后车暂时不交易
        if (bHaveAbnormalVeh) {
            return 1;
        }
    }
    return 0;
}

int CETCLaneCtrl::GetVehCount_FrontQue()
{
    QMutexLocker locker(&m_WaitTransMt);
    return m_VehWaitQueue.size();
}

int CETCLaneCtrl::GetAllVehCountInQue()
{
    QMutexLocker locker(&m_transInfoMt);
    return m_VehQueue.size() + m_VehWaitQueue.size();
}

int CETCLaneCtrl::GetVehCount_BackQue()
{
    QMutexLocker locker(&m_transInfoMt);
    return m_VehQueue.size();
}

void CETCLaneCtrl::SetVehLeadOut()
{
    if (!m_bVehLeadOut) {
        DebugLog("紧急车");
        SetAllowPass(true, false);
        // CDeviceFactory::GetIOCard()->SetAllowPass();
    } else {
        DebugLog("取消紧急车");
        // CDeviceFactory::GetIOCard()->SetRefusePass();
        SetRefusePass();
    }
    m_bVehLeadOut = !m_bVehLeadOut;
}

void CETCLaneCtrl::SetLaneStatus(qint32 nLaneStatus)
{
    if (m_LaneStatus != nLaneStatus) {
        m_LaneStatus = nLaneStatus;
        emit(NotifyLaneStatusChanged());
    }
}

/**
 * @brief
 * @param bAllowPass 交易完是否抬杆。false 入口卡机写卡成功后，不抬杆，需取卡后再抬
 * @return
 */
void CETCLaneCtrl::CompleteTrans(CTransResult bTransResult, int nIndex, bool bAllowPass)
{
#ifdef QT_DEBUG

#endif
    CTransInfo *pLastTransInfo = GetLastTransInfo(nIndex);
    //DebugLog(QString("transResult is %1, transState is %2").arg(pLastTransInfo->transResult).arg(pLastTransInfo->transState));
    if (DevIndex_Manual == nIndex || nIndex == DevIndex_Second) {
        //后天线或者人工操作交易完成取一次车牌识别结果
        if (pLastTransInfo && bTransResult == Tr_Successed) {
            if (pLastTransInfo->VehInfo.AutoVehClass > VC_None) {
                if (pLastTransInfo->m_vcrResult.dwCarID > 0) {
                    //采用小黄人车牌识别结果,以小黄人结果发送车牌识别报文
                    CAutoRegInfo autoRegInfo;
                    if (SaveAutoVehPlateMsg(pLastTransInfo->VehInfo, &pLastTransInfo->m_vcrResult,
                                            autoRegInfo)) {
                        pLastTransInfo->AutoRegInfo = autoRegInfo;
                    }
                } else {  //这一步是在按车型图片保存完报文后，重新设置vcr，用于后面过车时删除用
                    //因为既然有自动识别车型，就应该有对应的车型队列数据，且一定是第一辆车
                    VCRDev *pVcrDev = CDeviceFactory::GetVCRDev();
                    if (pVcrDev) {
                        VcrResult vcrRlt;
                        if (pVcrDev->GetFistVcrResult(vcrRlt)) {
                            pLastTransInfo->SetVcrResult(&vcrRlt);
                        }
                    }
                }
            }

            if (pLastTransInfo->AutoRegInfo.id.isEmpty()) {
                GetETCAutoRegInfo(nIndex, pLastTransInfo->AutoRegInfo, true, false);
            } else {
                ClearAutoRegInfo(nIndex);
            }
        }
    }

    if (pLastTransInfo) {  //发送实时过车报文
        if (pLastTransInfo->transState <= CTransInfo::Ts_WaitToSave) {
            CRealTimePass_RRU_FD msg;
            if (FillRealTimePass(msg, *pLastTransInfo)) {
                QString sJson = CHttpMsgManager::GetRruJSon(msg);
                // DebugLog(QString("etcsu:%1").arg(sJson));
                QDataToSave dataToSave;
                CHttpMsgManager::PackMsgToSave(dataToSave, sJson, QString("RRU"), 0, 0);
                QString stmpFileName;
                m_pDataMgr->SaveData(dataToSave, stmpFileName, 0, 0);
            }
        }
    }

    if (bTransResult > Tr_Failed)  //交易成功
    {
        if (Ptr_Info->IsExitLane()) {  //天线交易完成不能立即保存，要等待分省信息
            if ((0 == pLastTransInfo->dwOBUID) && (!pLastTransInfo->AutoRegInfo.id.isEmpty())) {
                if (pLastTransInfo->transState == CTransInfo::Ts_WaitToSave) {
                    DebugLog(QString("出口交易完成,直接保存流水,vlp:%1,id:%2")
                             .arg(pLastTransInfo->AutoRegInfo.sAutoVehPlate)
                             .arg(pLastTransInfo->AutoRegInfo.id));
                    SaveLastTransInfo(1, pLastTransInfo, true);
                }
            } else {
                if ((!pLastTransInfo->m_bForTac) &&
                        (pLastTransInfo->transState == CTransInfo::Ts_WaitToSave)) {
                    saveTmpTransWaste_Ex(pLastTransInfo);
                }
            }
        }

        if (Ptr_Info->bUseVehLib()) {
            CVehTypeLibMgr::GetVehTypeLibMgr()->DebugLogVehInfo(pLastTransInfo->VehInfo);
        }

        AddHaveTransVeh(*pLastTransInfo);
        CTransInfo *pTransInfoInQue = NULL;
        if (DevIndex_First == nIndex) {
            //前天线只有OBU交易
            //取队列内交易最后一个交易失败的流水
            CTransInfo *pFailedTransInfo = Ptr_ETCCtrl->GetLastWasteOfNotSaved(false);
            //为避免交易过程中，队列内车辆被删除，因此保存临时变量
            CTransInfo tmpTransInfo;
            if (pFailedTransInfo) {
                tmpTransInfo = *pFailedTransInfo;
                pFailedTransInfo = &tmpTransInfo;
                DebugLog(QString("队列内存在未交易车辆,obu:%1,result:%2,sate:%3")
                         .arg(pFailedTransInfo->dwOBUID)
                         .arg(pFailedTransInfo->transResult)
                         .arg(pFailedTransInfo->transState));
            }

            pTransInfoInQue = pLastTransInfo;
            /*
            if (pFailedTransInfo && (pLastTransInfo->dwOBUID == pFailedTransInfo->dwOBUID)) {
                DebugLog(QString("前天线交易完成，当前有未交易完车辆[%1]，更新当前车辆[%2]")
                             .arg(pFailedTransInfo->dwOBUID)
                             .arg(pLastTransInfo->dwOBUID));
                if (!UpdateVehInfoInQueue(*pLastTransInfo, true)) {
                    DebugLog("前天线交易完成,更新队列异常车辆状态失败，增加新车辆");
                    AddNewTransInfoToFrontList(*pLastTransInfo);
                }
            }*/
            if (ReplaceLastVehInfoInQueue(*pLastTransInfo)) {
                int nVehCount = GetVehCount_BackQue();
                DebugLog(QString("前天线交易完成，更新当前车辆[%1],当前后队列车辆数:%2")
                         .arg(pLastTransInfo->dwOBUID)
                         .arg(nVehCount));
                if (nVehCount > 1) bAllowPass = false;

            } else {
                AddNewTransInfoToFrontList(*pLastTransInfo);
                int nVehCount = GetVehCount_BackQue();
                DebugLog(QString("前天线交易完成,保存当前交易结果,后队列车辆数:%1").arg(nVehCount));
                if (nVehCount > 0) bAllowPass = false;
            }

        } else {  //后天线或cpc交易完，如果队列内有异常车，就更新异常车，如果没有，就从队列前端增加一辆车
            pTransInfoInQue = pLastTransInfo;
            CTransInfo *pTransInfo = RemoveTransInfoFromQue(true, false);
            if (pTransInfo && (!pTransInfo->bTransOk())) {
                //更新函数内已经发送显示流水报文
                UpdateVehInfoInQueue(*pLastTransInfo, false);
                //                pTransInfoInQue = pLastTransInfo;
            } else {
                // if (pTransInfo != pLastTransInfo) {
                CTransInfo *pNewTransInfo = new CTransInfo(*pLastTransInfo);
                AddTransVehToList(1, pNewTransInfo, true);
                //              pTransInfoInQue = pLastTransInfo;
            }
            // emit NotifyVehQueChanged();
        }

        //保证在队列内增加车辆后，再抬杆
        bool bOutTime = !Ptr_Info->bHaveBackDev();
        if (bAllowPass) {
            bool bForceup = nIndex == DevIndex_Second || nIndex == DevIndex_Manual;
            SetAllowPass(bForceup, bOutTime);
            DebugLog(QString("抬杆放行"));
            StartVehLeaveTimer();

            // 发送停止卡机定时器的信号(仅对ETC交易)
            if (pLastTransInfo && (pLastTransInfo->mediaType == MediaType_OBU) && (DevIndex_First != nIndex)) {
                emit NotifyStopCardMgrTimer();
                DebugLog("发送停止卡机定时器信号");
            }
        }
        if (pTransInfoInQue) {
            if (DevIndex_Second == nIndex || DevIndex_Manual == nIndex) {
                int nFee = pTransInfoInQue->m_nTransFee;
                QString sVehPlate = GB2312toUnicode(pTransInfoInQue->VehInfo.szVehPlate);
                VDMLibSingleton::instance()->DisplayTransactionInfo(
                            nFee, sVehPlate, pTransInfoInQue->VehInfo.VehClass);
            }
            emit NotifyTransCompleteETC(false, 0, pTransInfoInQue);
        }
    } else {
        //前天线交易失败,-直接保存交易失败流水,后天线交易失败,不增加失败流水
        if (DevIndex_First == nIndex) {
            AddHaveTransVeh(*pLastTransInfo);
        }
        // todo 为避免产生异常数据，暂时不发送失败报文
        /*
        if(Ptr_Info->IsEntryLane()){
            saveTransWaste_EN(pLastTransInfo);
        }else
            saveTransWaste_EX(pLastTransInfo);
        */
        //目前交易失败流水都是B5帧失败时生成的。
        if (pLastTransInfo->mediaType == MediaType_OBU) {
            if (pLastTransInfo->etcTransType == TransType_ExNormal &&
                    (!pLastTransInfo->bReGetTac)) {
                saveTransWaste_Tac(pLastTransInfo, 0);
            }
        }
        DebugLog(QString("发送ETC交易失败信号"));
        emit NotifyTransCompleteETC(false, 0, pLastTransInfo);
    }
}

void CETCLaneCtrl::SetImageInfoByVcr(CTransInfo *pTransInfo)
{
    if (!Ptr_Info->bUseVcrResult()) return;
    if (!pTransInfo->AutoRegInfo.id.isEmpty()) return;
    if (pTransInfo->m_nRsuIndex != DevIndex_First) {
        return;
    }
    QString sVehPlate = GB2312toUnicode(pTransInfo->VehInfo.szVehPlate);
    if (sVehPlate.isEmpty()) return;
    VCRDev *pDev = CDeviceFactory::GetVCRDev();
    if (!pDev) return;
    VcrResult vcrResult;
    bool bRlt = pDev->GetVcrResult(pTransInfo->VehInfo.nVehPlateColor, sVehPlate, vcrResult);
    if (!bRlt) return;

    pTransInfo->SetVcrResult(&vcrResult);
    CAutoRegInfo autoRegInfo;
    DebugLog(QString("前天线车辆抓拍取小黄人识别结果%1").arg(sVehPlate));
    memcpy(pTransInfo->VehInfo.szAutoVehPlate, pTransInfo->VehInfo.szVehPlate,
           sizeof pTransInfo->VehInfo.szVehPlate);
    pTransInfo->VehInfo.nAutoVehPlateColor = pTransInfo->VehInfo.nVehPlateColor;
    if (SaveAutoVehPlateMsg(pTransInfo->VehInfo, &pTransInfo->m_vcrResult, autoRegInfo)) {
        pTransInfo->AutoRegInfo = autoRegInfo;
        return;
    }
    return;
}

void CETCLaneCtrl::EmitNotifyTransCompleteSign(CTransInfo *pTransInfo, bool bRedo)
{
    emit NotifyTransCompleteETC(bRedo, 0, pTransInfo);
}

void CETCLaneCtrl::ReCompleteTrans_HaveTrans(int nIndex, CTransInfo *pReserveTrans, bool bReserved)
{
    CTransInfo *pLastTransInfo = Ptr_ETCCtrl->GetLastTransInfo(nIndex);
    pReserveTrans->SaveTo(*pLastTransInfo);
    CompleteTrans(Tr_Successed, nIndex, true);
    CTransInfo *pCurTransInfo = Ptr_ETCCtrl->GetCurTransInfo(nIndex);
    if (pCurTransInfo) {
        pCurTransInfo->ClearTransInfo();
    }
    DebugLog(QString("重复交易车辆抬杆放行"));
    return;
}

CTransInfo *CETCLaneCtrl::GetCurTransInfo(int nIndex, quint32 nCheckVehQue)
{
    //前天线交易
    if (DevIndex_First == nIndex)
        return &m_curTransInfo;
    else {
        // CTransInfo transInfo;
        //有前天线时,必须队列内有异常车才允许后天线交易
        /*
        if (nCheckVehQue && Ptr_Info->bHaveFrontDev()) {
            if (!bAbnormalVehOfTheFirstVehInQueue(transInfo)) {
                return NULL;
            }
        }*/
        if (DevIndex_Second == nIndex)
            return &m_backTransInfo;
        else
            return &m_manuTransInfo;
    }
}

bool CETCLaneCtrl::AppStart(QString &sError)
{
    // m_UpdateServer.SetConnInfo(QString("172.16.204.1"), 30015, Ptr_Info->GetStationName(),
    // Ptr_Info->GetStationID(), Ptr_Info->GetLaneId());

    if (Ptr_Info->IsExitLane()) {
        GetAndResendTmpFile();
    }
    sError = QString("发送程序启动报文");
    CShiftParam NewShiftParam, curShiftParam;
    NewShiftParam.Clear();
    curShiftParam.Clear();

    QByteArray syncData;
    QByteArray lastShiftSumInfo, curShiftSumInfo;

    //先读取工班汇总数
    bool bResult = m_pDataMgr->GetBigShiftSumInfo(lastShiftSumInfo, curShiftSumInfo);
    if (!bResult) {
        sError = QString("班次汇总信息失败");
        return false;
    }
    m_ShiftMgr.SetBigShiftSumData(lastShiftSumInfo, curShiftSumInfo);

    //读取小班汇总数
    bool bRlt = m_pDataMgr->GetSyncData(
                syncData, QSqliteDataSaver::SumType_Shift);  //从数据库获取工班汇总信息
    CShiftSumInfo *pShiftSumInfo = NULL;
    if (bRlt && syncData.size() > 0) {
        pShiftSumInfo = (CShiftSumInfo *)syncData.constData();
        if ((pShiftSumInfo->nStationID != m_nStationId) || (pShiftSumInfo->nLaneID != m_nLaneId)) {
            if ((pShiftSumInfo->nStationID != 0) && (pShiftSumInfo->nLaneID != 0)) {
                sError = QString("数据库内工班合计数信息与实际不符,nStatinId:%1 nLaneId:%2")
                        .arg(pShiftSumInfo->nStationID)
                        .arg(pShiftSumInfo->nLaneID);
                ErrorLog(sError);
                return false;
            }
        }

        m_ShiftMgr.InitShiftMgr(m_nStationId, m_nLaneId, pShiftSumInfo);
        if (pShiftSumInfo->nOperID > 0 && 0 == pShiftSumInfo->statusFlag) {
            DebugLog(QString("程序上次未正常关闭,operId:%1").arg(pShiftSumInfo->nOperID));
            //上次车道未下班，补发下班报文，更新工班合计数
            UnLogin(true);
        } else {
            if (0 == m_ShiftMgr.m_curShiftSumInfo.wShiftID) {
                m_ShiftMgr.SumShift();
                CShiftSumInfo curShiftSumInfo;
                m_ShiftMgr.GetBigShiftSumInfo(curShiftSumInfo);
                if (!SaveBigShiftSumData(curShiftSumInfo, 0)) {
                    ErrorLog("程序启动-保存班次汇总更新失败");
                    sError = QString("保存班次数据失败");
                    return false;
                }
            }
        }
    }

    //初始化门架小时合计数
    QByteArray bSumData;
    bRlt = m_pDataMgr->GetSyncData(bSumData, QSqliteDataSaver::SumType_Hour);
    CGantrySumInfo_H *pGantrySumInfo = NULL;
    if (bRlt && bSumData.size() > 0) {
        pGantrySumInfo = (CGantrySumInfo_H *)bSumData.constData();
        QString sGantryId = QString::fromAscii(pGantrySumInfo->szGantryId);
        if (sGantryId.length() > 0) {
            CTollGantryMgr::GetTollGantryMgr()->SetGantrySumInfo_New(bSumData);
        }
    }

    QString sCurBatch = CBatchMgr::GetBatchMgr()->GetCurBatch();
    CTollGantryMgr::GetTollGantryMgr()->CheckBatchChanged_New(sCurBatch);

    //如果是出口，加载票据
    if (Ptr_Info->IsExitLane()) {
        GetInvoiceInfo()->LoadFromFile();
    }

    //切换班次
    m_ShiftMgr.CheckAndSwitchShift(false);
    return SaveAppStarMsg(true);
}

void CETCLaneCtrl::AppClose() { SaveAppStarMsg(false); }

bool CETCLaneCtrl::SaveAppStarMsg(bool bStart)
{
    // TraceLog(QString("发送程序%1报文").arg((bStart?"启动":"退出")));
    return true;
}

void CETCLaneCtrl::SetTermId(const quint8 *pTermId, uint nMaxLen)
{
    uint tmp = sizeof(m_TermCode);
    memcpy(m_TermCode, pTermId, qMin(tmp, nMaxLen));
}

void CETCLaneCtrl::ChangeToVehInputState()
{
    if (m_pVehInputState) {
        //切换到输入状态
        Ptr_RemoteCtrl->ChangeState_Input();
        CAbstractState::ChangeToNextState(m_pVehInputState);
    }
}
void CETCLaneCtrl::ChangeToPSAMAuthState()
{
    if (m_pPSAMAuthStaste) {
        if (CAbstractState::GetCurState() != m_pPSAMAuthStaste)
            CAbstractState::ChangeToNextState(m_pPSAMAuthStaste);
    }
}

void CETCLaneCtrl::ChangeToVehMoneyState()
{
    if (m_pVehMoneyState) {
        Ptr_RemoteCtrl->ChangeState_ExitPay();
        CAbstractState::ChangeToNextState(m_pVehMoneyState);
    }
}

void CETCLaneCtrl::StartETCState() { CAbstractState::SetETCState(m_pETCLaneState); }

void CETCLaneCtrl::CloseETCState() { CAbstractState::SetETCState(NULL); }

void CETCLaneCtrl::SaveVLPRSumWaste(const QString &sHourBatchNum, quint32 vehicleDataCount,
                                    quint32 vehiclePicCount)
{
    CVLPHourSum_TOLLDISHSU VLPRSum;
    VLPRSum.id = QString("%1%2")
            .arg(Ptr_Info->GetGBLaneId())
            .arg(sHourBatchNum);  //批次汇总流水ID 收费车道编号+小时批次号(YYYYMMDD)
    VLPRSum.stationProId = QString::number(Ptr_Info->GetStationID());  //收费站编号（省标）
    VLPRSum.stationId = Ptr_Info->GetGBStationId();                    //收费站编号（国标）
    VLPRSum.stationName = Ptr_Info->GetStationName();                  //收费站名称
    VLPRSum.laneNum = Ptr_Info->GetGBLaneId();                         //国标车道编码
    VLPRSum.hourBatchNo = sHourBatchNum;  //车牌识别批次号 YYYYMMDDHH,对应车道识别流水批次
    VLPRSum.collectDate = QDateTime::currentDateTime().toString("yyyyMMdd");  //统计日期 YYYYMMDD
    VLPRSum.vehicleDataCount = vehicleDataCount;  //车牌识别流水数
    VLPRSum.vehiclePicCount = vehiclePicCount;    //车牌识别图片流水数
    VLPRSum.branchAgency = QString("fangxing");

    QString sJson = CHttpMsgManager::GetVlpPHourSumJSon(VLPRSum);
    QDataToSave dataToSave;
    CHttpMsgManager::PackMsgToSave(dataToSave, sJson, QString("TOLLDISHSU"), 0, 0);
    QString stmpFileName;
    ;
    m_pDataMgr->SaveData(dataToSave, stmpFileName, 0, 0);
}

void CETCLaneCtrl::InitAllState()
{
    m_pPSAMAuthStaste = new CLaneState_PSAMAuth(this);
    m_LaneStates.push_back(m_pPSAMAuthStaste);

    if (Ptr_Info->IsEntryLane())
        m_pVehInputState = new CLaneState_VehInputEntry(this);
    else
        m_pVehInputState = new CLaneState_VehInputExit(this);

    m_LaneStates.push_back(m_pVehInputState);
    m_pUnLoginState = new CLaneState_UnLogin(this);
    m_LaneStates.push_back(m_pUnLoginState);
    m_pWaitVehPassState = new CLaneState_WaitVehPass(this);
    m_LaneStates.push_back(m_pWaitVehPassState);
    m_pMotorCadeState = new CLaneState_MotorCade(this);
    m_LaneStates.push_back(m_pMotorCadeState);

    m_pVehMoneyState = new CLaneState_VehMoney(this);
    m_LaneStates.push_back(m_pVehMoneyState);

    m_pETCLaneState = new CLaneState_ETCState(this);
    m_LaneStates.push_back(m_pETCLaneState);
}

void CETCLaneCtrl::ClearAllState() { m_LaneStates.clear(); }

void CETCLaneCtrl::SetLaneStateUI(int nStateId, CAbstractStateUI *pStateUI)
{
    foreach (CAbstractState *pState, m_LaneStates) {
        if (pState->GetStateId() == nStateId) {
            pState->SetStateUI(pStateUI);
        }
    }
}

void CETCLaneCtrl::SetAuthorizeInfo(const CAuthorizeInfo &AuthInfo) { m_AuthorizeInfo = AuthInfo; }

bool CETCLaneCtrl::SaveWorkSumData(const CShiftSumInfo &ShiftSumInfo)
{
    if (!m_pDataMgr) return false;
    QByteArray bSyncData;
    bSyncData.append((char *)&ShiftSumInfo, sizeof ShiftSumInfo);
    return m_pDataMgr->SaveSyncData(bSyncData, QSqliteDataSaver::SumType_Shift);
}

bool CETCLaneCtrl::SaveBigShiftSumData(const CShiftSumInfo &ShiftSumInfo, int nDataType)
{
    if (!m_pDataMgr) return false;

    QByteArray sumData;
    sumData.append((char *)&ShiftSumInfo, sizeof ShiftSumInfo);
    return m_pDataMgr->SaveBigShiftData(sumData, nDataType);
}

bool CETCLaneCtrl::EndCurShiftInfo(CShiftSumInfo &lastShiftSumInfo)
{
    m_ShiftMgr.GetBigShiftSumInfo(lastShiftSumInfo);
    m_ShiftMgr.SetLastShiftSumInfo(lastShiftSumInfo);

    return SaveBigShiftSumData(lastShiftSumInfo, 1);
}

bool CETCLaneCtrl::SaveGanteyHourSumData(const QByteArray &SumInfo)
{
    if (!m_pDataMgr) {
        return false;
    }
    return m_pDataMgr->SaveSyncData(SumInfo, QSqliteDataSaver::SumType_Hour);
}

void CETCLaneCtrl::OnDevStatusChangedEvent(qint32 nDevId, qint32 bStatus)
{
    CStdLog::StdLogDevInfo_DevStatus(nDevId, bStatus);
    char cStatus = 0 == bStatus ? '1' : '0';
    switch (nDevId) {
    case DEV_CardReader:
    case DEV_CardReader1:
    case DEV_CardReader2:
        m_DevInfo[DEV_INDEX_CardReader] = cStatus;
        break;
    case DEV_ETCFare:
    case DEV_MTCFare:
        m_DevInfo[DEV_INDEX_FeeDisplay] = cStatus;
        break;
    case DEV_VideoCard:
        m_DevInfo[DEV_INDEX_VideoCard] = cStatus;
        break;
    case DEV_Printf:
        m_DevInfo[DEV_INDEX_Printer] = cStatus;
        break;
    case DEV_Weight:
        m_DevInfo[DEV_INDEX_Weighter] = cStatus;
        break;
    case DEV_VPR:
        m_DevInfo[DEV_INDEX_PlaterRecog] = cStatus;
        break;
    case DEV_RSU:
    case DEV_RSU1:
        m_DevInfo[DEV_INDEX_ETCAnt] = cStatus;
        break;
    default:
        break;
    }
    return;
}

bool CETCLaneCtrl::OnDOChangeEvent(qint32 nDo, bool bStatus)
{
    char StatusChar = bStatus ? '1' : '2';
    switch (nDo) {
    case DO_CanopyLightGreen:
        // m_DevInfo[DEV_INDEX_]=StatusChar;
        break;
    case DO_CanopyLightRed:
        // m_DevInfo[DEV_INDEX_CanopyLight]=StatusChar;
        break;
    case DO_PassLightGreen:
        m_DevInfo[DEV_INDEX_TrafficLight] = StatusChar;
        break;
    case DO_PassLightRed:
        break;
    case DO_BarrierDown:
        break;
    case DO_BarrierUp:
        m_DevInfo[DEV_INDEX_AutoBar] = StatusChar;
        break;
    case DO_AlarmSound:
        break;
    default:
        break;
    }
    return true;
}

//保存车牌识别图像，并返回图像名称
bool CETCLaneCtrl::SaveVLPImg(const CVPRResult &vehinfo, QString &sBigImgFileName,
                              QString &sRelativeFileName)
{
    if (!Ptr_Info->bUseStdVPR()) {
        QDateTime dtCur = QDateTime::currentDateTime();
        QString sRelativePath, sFileName;
        FormatPicFileName(dtCur, 1, sRelativePath, sFileName, vehinfo.nDevIndex);
        QString imgPath = Ptr_Info->GetPicPath() + sRelativePath;
        CreatePath(imgPath);

        sBigImgFileName = imgPath + sFileName;
        sRelativeFileName = QString(".\\pic") + sRelativePath + sFileName;
        DebugLog(QString("sRelativeFileName:%1").arg(sRelativeFileName));

        sFileName.clear();
        sRelativePath.clear();
        FormatPicFileName(dtCur, 0, sRelativePath, sFileName, vehinfo.nDevIndex);
        QString sSmallImgFileName = imgPath + sFileName;
        DebugLog(QString("nBigImgSize:%1").arg(vehinfo.nBigImgSize));
        return false;
    } else {
        QDateTime dtCur = QDateTime::currentDateTime();
        QString sRelativePath, sFileName;
        FormatPicFileName(dtCur, 1, sRelativePath, sFileName, vehinfo.nDevIndex);
        QString imgPath = Ptr_Info->GetPicPath() + sRelativePath;
        CreatePath(imgPath);

        sBigImgFileName = imgPath + sFileName;
        sRelativeFileName = QString(".\\pic") + sRelativePath + sFileName;
        DebugLog(QString("保存车牌识别图片,nBigImgSize:%1,fileName:%2")
                 .arg(vehinfo.nBigImgSize)
                 .arg(sBigImgFileName));
        if (!Movefile(vehinfo.sBigName, sBigImgFileName)) {
            return false;
        }
        return true;
    }
}

bool CETCLaneCtrl::SetETCAutoVehPlate(int nIndex, const CVehInfo &vehInfo,
                                      const QString &sBigImgFileName,
                                      const QString &sRelativeFileName, CAutoRegInfo &autoRegInfo,
                                      VcrResult *pVcrResult)
{
    autoRegInfo.ClearResult();
    CVLPWaste_VIU viu;
    {
        QMutexLocker locker(&m_ETCAutoRegMt);
        if (nIndex > 1) nIndex = 1;
        m_ETCAutoRegInfos[nIndex].ClearResult();
        QString sVehPlate = GB2312toUnicode(vehInfo.szAutoVehPlate).simplified();
        if (sVehPlate.isEmpty()) {
            DebugLog(QString("车牌识别%1流水内车牌号为空不发送流水报文").arg(nIndex));
            return false;
        } else {
            DebugLog(QString("保存车牌识%1别结果:%2").arg(nIndex).arg(sVehPlate));
        }
        m_ETCAutoRegInfos[nIndex].nDevIndex = nIndex;
        m_ETCAutoRegInfos[nIndex].nAutoVLPColor = vehInfo.nAutoVehPlateColor;
        m_ETCAutoRegInfos[nIndex].sAutoVehPlate = sVehPlate;
        m_ETCAutoRegInfos[nIndex].sBigFileName = sBigImgFileName;
        m_ETCAutoRegInfos[nIndex].sRelativeFileName = sRelativeFileName;
        m_ETCAutoRegInfos[nIndex].AuRegTime = QDateTime::currentDateTime();
        QString sBatch, sNo;
        QString sHexId = Ptr_Info->GetHexLaneID();
        m_ETCAutoRegInfos[nIndex].id =
                Ptr_Info->GetGBLaneId() + CBatchMgr::GetBatchMgr()->GetBatchInfo(
                    sBatch, sNo, sHexId, CBatchMgr::SnType_VLP, false);

        autoRegInfo = m_ETCAutoRegInfos[nIndex];
        DebugLog(QString("车牌识别%1,流水号:%2").arg(nIndex).arg(m_ETCAutoRegInfos[nIndex].id));
        //发送报文
        viu.id = m_ETCAutoRegInfos[nIndex].id;
        viu.hourBatchNo = sBatch;
        viu.laneSign = Ptr_Info->GetLaneSign();
        viu.TollStationId = Ptr_Info->GetGBStationId();
        viu.laneNum = QString("%1").arg(Ptr_Info->GetLaneId());
        viu.pointTime = QDateTime2GBTimeStr(m_ETCAutoRegInfos[nIndex].AuRegTime);
        viu.identifyVlp = m_ETCAutoRegInfos[nIndex].sAutoVehPlate;
        viu.identifyVlpc = m_ETCAutoRegInfos[nIndex].nAutoVLPColor;

        viu.identifyType = "";
        if (pVcrResult) {
            QString sVcName = GetVehClassName(pVcrResult->vehclass);
            if (!sVcName.isEmpty()) viu.identifyType = QString("%1").arg(pVcrResult->vehclass);
        }

        if (Ptr_Info->IsEntryLane())
            viu.direction = 1;
        else
            viu.direction = 2;

        viu.opTime = QDateTime2GBTimeStr(m_ETCAutoRegInfos[nIndex].AuRegTime);
        if (viu.identifyVlp.length() > 0) {
            quint8 bColor = viu.identifyVlpc;
            if (!IsValidVehPlateColor(bColor)) bColor = VP_COLOR_UNKNOW;
            viu.vehicleId = QString("%1_%2").arg(viu.identifyVlp).arg(bColor);
        }
        DebugLog("开始保存车牌识别结果");

        /*
        if (Ptr_Info->bHaveFrontDev() && nIndex == DevIndex_First) {
            if (SaveAutoVLPResultToVehQue(nIndex, m_ETCAutoRegInfos[nIndex])) {
                DebugLog("保存车牌识别结果成功");
                m_ETCAutoRegInfos[nIndex].ClearResult();
            } else
                DebugLog("当前队列内无车，保存车牌识别结果失败,等待交易完成后自取");
        }*/
    }

    QString sJson = CHttpMsgManager::GetVluJSon(viu);
    QDataToSave dataToSave;
    CHttpMsgManager::PackMsgToSave(dataToSave, sJson, "VIU", 0, 0);
    DebugLog("开始发送车牌识别报文");
    QString stmpFileName;
    m_pDataMgr->SaveData(dataToSave, stmpFileName, 0, 0);
    CVIPUMgr::GetVIPUMgr()->AddVehInfo(autoRegInfo, pVcrResult);
    return true;
}

bool CETCLaneCtrl::SaveAutoVehPlateMsg(const CVehInfo &vehInfo, VcrResult *pVcrResult,
                                       CAutoRegInfo &autoRegInfo)
{
    if (!pVcrResult) return false;
    CVLPWaste_VIU viu;
    {
        autoRegInfo.ClearResult();
        QString sVehPlate = GB2312toUnicode(vehInfo.szAutoVehPlate).simplified();
        if (sVehPlate.isEmpty()) {
            DebugLog(QString("小黄人车牌识别流水内车牌号为空不发送流水报文"));
            return false;
        } else {
            DebugLog(QString("保存小黄人车牌识别结果:%1,识别时间:%2")
                     .arg(sVehPlate)
                     .arg(pVcrResult->dtPlate.toString("yyyyMMddhhmmss")));
        }
        autoRegInfo.nDevIndex = 1;
        autoRegInfo.nAutoVLPColor = vehInfo.nAutoVehPlateColor;
        autoRegInfo.sAutoVehPlate = sVehPlate;
        autoRegInfo.sBigFileName.clear();
        autoRegInfo.sRelativeFileName.clear();
        autoRegInfo.AuRegTime = pVcrResult->dtPlate;
        autoRegInfo.AutoVehClass = vehInfo.AutoVehClass;

        QDateTime curTime = QDateTime::currentDateTime();
        if (qAbs(autoRegInfo.AuRegTime.secsTo(curTime)) > 60 * 30) {
            DebugLog(QString("小黄人识别时间:%1,调整为当前时间")
                     .arg(autoRegInfo.AuRegTime.toString("yyyyMMddhhmmss")));
            autoRegInfo.AuRegTime = curTime.addSecs(-20);
        }

        if (sVehPlate.isEmpty()) {
            DebugLog(QString("车型识别流水内车牌号为空不发送流水报文"));
            return false;
        } else {
            DebugLog(QString("发送小黄人车牌识别流水,车牌号:%1").arg(sVehPlate));
        }
        QString sBatch, sNo;
        QString sHexId = Ptr_Info->GetHexLaneID();
        QString sId =
                Ptr_Info->GetGBLaneId() + CBatchMgr::GetBatchMgr()->GetBatchInfo(
                    sBatch, sNo, sHexId, CBatchMgr::SnType_VLP, false);

        autoRegInfo.id = sId;
        //发送报文
        viu.id = sId;
        viu.hourBatchNo = sBatch;
        viu.laneSign = Ptr_Info->GetLaneSign();
        viu.TollStationId = Ptr_Info->GetGBStationId();
        viu.laneNum = QString("%1").arg(Ptr_Info->GetLaneId());
        QString sCapTime = QDateTime2GBTimeStr(autoRegInfo.AuRegTime);
        viu.pointTime = sCapTime;
        viu.identifyVlp = GB2312toUnicode(vehInfo.szAutoVehPlate);
        viu.identifyVlpc = vehInfo.nAutoVehPlateColor;

        viu.identifyType = "";
        if (pVcrResult) {
            QString sVcName = GetVehClassName(pVcrResult->vehclass);
            if (!sVcName.isEmpty()) viu.identifyType = QString("%1").arg(pVcrResult->vehclass);
        }

        if (Ptr_Info->IsEntryLane())
            viu.direction = 1;
        else
            viu.direction = 2;

        viu.opTime = sCapTime;

        DebugLog(QString("生成小黄人车牌识别流水号:%1,识别时间:%2").arg(sId).arg(viu.opTime));

        if (viu.identifyVlp.length() > 0) {
            quint8 bColor = viu.identifyVlpc;
            if (!IsValidVehPlateColor(bColor)) bColor = VP_COLOR_UNKNOW;
            viu.vehicleId = QString("%1_%2").arg(viu.identifyVlp).arg(bColor);
        }
    }
    DebugLog("开始小黄人车牌识别流水");
    QString sJson = CHttpMsgManager::GetVluJSon(viu);
    QDataToSave dataToSave;
    CHttpMsgManager::PackMsgToSave(dataToSave, sJson, "VIU", 0, 0);
    DebugLog("开始发送车牌(小黄人)识别报文");
    QString stmpFileName;
    m_pDataMgr->SaveData(dataToSave, stmpFileName, 0, 0);
    this->SaveVIPUMsg_VcrResult(viu.id, pVcrResult);
    return true;
}

bool CETCLaneCtrl::SaveVIPUMsg(const CAutoRegInfo &autoRegInfo, VcrResult *pResult)
{
    // 1、根据车牌查找车型识别信息
    VcrResult vcrResult;
    VcrResult *pVcrResult = pResult;
    if (!pVcrResult) {
        /*
        DebugLog(QString("Save vipu, reget vcrresult,%1_%2")
                     .arg(autoRegInfo.sAutoVehPlate)
                     .arg(autoRegInfo.nAutoVLPColor));

        VCRDev *pVcrDev = CDeviceFactory::GetVCRDev();
        if (pVcrDev) {
            bool bHaveVcr = pVcrDev->GetVcrResult(autoRegInfo.nAutoVLPColor,
                                                  autoRegInfo.sAutoVehPlate, vcrResult);
            if (bHaveVcr) {
                if (pVcrDev->bSendVideo()) {
                    vcrResult.sVideoFileName = pVcrDev->GetVideoFileName(vcrResult.dwCarID);
                }
                pVcrResult = &vcrResult;
                DebugLog(QString("regetvehclass,vc:%1,videoFileName:%2,tailfileName:%3")
                             .arg((int)pVcrResult->vehclass)
                             .arg(pVcrResult->sVideoFileName)
                             .arg(pVcrResult->bigImage[VehImg_Tail].sImgFile));
            }
        }*/
    } else {
        DebugLog(QString("save vipu,vehclass,vc:%1,plate:%2,videoFileName:%3,tailfileName:%4")
                 .arg((int)pVcrResult->vehclass)
                 .arg(autoRegInfo.sAutoVehPlate)
                 .arg(pVcrResult->sVideoFileName)
                 .arg(pVcrResult->bigImage[VehImg_Tail].sImgFile));
    }

    //图片流水只有发送时才保存成Json
    CVLPImage_SaveData imageData;
    memset(&imageData, 0, sizeof imageData);
    bool bHaveBigFile = false;
    qsnprintf(imageData.szId, sizeof imageData.szId, "%s", autoRegInfo.id.toAscii().constData());
    if (autoRegInfo.sBigFileName.length() > 0) {
        QByteArray bigImg;
        bHaveBigFile = LoadImageFromFile(autoRegInfo.sBigFileName, bigImg);
        if (bHaveBigFile) {
            qsnprintf(imageData.szImageFileName, sizeof imageData.szImageFileName, "%s",
                      autoRegInfo.sBigFileName.toLocal8Bit().data());
            DebugLog(QString("save vipu,bigImg:%1")
                     .arg(QString::fromLocal8Bit(imageData.szImageFileName)));
        } else {
            DebugLog(QString("车牌识别图片文件%1,不存在"));
        }
    } else {
        DebugLog(QString("save vipu,bigImg is null"));
    }

    if (pVcrResult) {
        QString VLPPath = GetCurrentPath() + QString("VehVideo/");
        CreatePath(VLPPath);
        QString sTime = QDateTime::currentDateTime().toString("MMddhhmmss");
        DebugLog(QString("save vipu,video fileName:%1").arg(pVcrResult->sVideoFileName));
        if (pVcrResult->sVideoFileName.length() > 0) {
            QString sFileName =
                    VLPPath + QString("V_") + GetFileNameFromFullName(pVcrResult->sVideoFileName);

            DebugLog(QString("NewVideofile:%1,oldVideoFile:%2")
                     .arg(sFileName)
                     .arg(pVcrResult->sVideoFileName));

            if (CopyFile_Replace(pVcrResult->sVideoFileName, sFileName)) {
                qsnprintf(imageData.szVideoImageFileName, sizeof imageData.szVideoImageFileName,
                          "%s", sFileName.toLocal8Bit().data());
                qsnprintf(imageData.szVideoImgSuffix, sizeof imageData.szVideoImgSuffix, "mp4");
            } else {
                DebugLog(QString("save vipu,copy video file %1 to %2 failed")
                         .arg(pVcrResult->sVideoFileName)
                         .arg(sFileName));
            }
        }
        QString sTailFileName = pVcrResult->bigImage[VehImg_Tail].sImgFile;
        DebugLog(QString("save vipu tail file:%1").arg(sTailFileName));
        if (sTailFileName.length() > 0) {
            QString sFileName = VLPPath + QString("T_") + GetFileNameFromFullName(sTailFileName);

            DebugLog(QString("New taile fileName:%1").arg(sFileName));
            if (CopyFile_Replace(sTailFileName, sFileName)) {
                qsnprintf(imageData.sztailImageFileName, sizeof imageData.sztailImageFileName, "%s",
                          sFileName.toLocal8Bit().data());
            } else {
                DebugLog(QString("copy tail file %1 to %2失败").arg(sTailFileName).arg(sFileName));
            }
        }
        QString sBodyFileName = pVcrResult->bigImage[VehImg_Body].sImgFile;
        DebugLog(QString("save vipu,body file:%1").arg(sBodyFileName));
        if (sBodyFileName.length() > 0) {
            QString sFileName = VLPPath + QString("B_") + GetFileNameFromFullName(sBodyFileName);
            DebugLog(QString("save vipy,new bodyfile:%1").arg(sFileName));
            if (CopyFile_Replace(sBodyFileName, sFileName)) {
                qsnprintf(imageData.szBodyImageFileName, sizeof imageData.szBodyImageFileName, "%s",
                          sFileName.toLocal8Bit().data());
            } else {
                DebugLog(QString("拷贝车身文件%1 to%2失败").arg(sBodyFileName).arg(sFileName));
            }
        }

        if (!bHaveBigFile) {
            QString sHeadFileName = pVcrResult->bigImage[VehImg_Front].sImgFile;
            DebugLog(QString("不存在车牌图片,改用车型结果:%1").arg(sHeadFileName));
            if (sHeadFileName.length() > 0) {
                QString sFileName =
                        VLPPath + QString("H_") + GetFileNameFromFullName(sHeadFileName);
                DebugLog(QString("save vipu,new headfile:%1").arg(sFileName));
                if (CopyFile_Replace(sHeadFileName, sFileName)) {
                    qsnprintf(imageData.szImageFileName, sizeof imageData.szImageFileName, "%s",
                              sFileName.toLocal8Bit().data());
                } else {
                    DebugLog(QString("拷贝车头文件%1 to%2失败").arg(sHeadFileName).arg(sFileName));
                }
            }
        }
    }

    QDataToSave vipuToSave;
    CHttpMsgManager::PackBinMsgToSave(vipuToSave, (quint8 *)&imageData, sizeof imageData, "VIPU");
    QString sTmpFileName;
    bool bRlt = m_pDataMgr->SaveData(vipuToSave, sTmpFileName, 0, 0);
    if (bRlt) {
        DebugLog(QString("save vipu,%1").arg(autoRegInfo.sAutoVehPlate));
    }
    return true;
}

bool CETCLaneCtrl::SaveVIPUMsg_VcrResult(const QString &sId, VcrResult *pVcrResult)
{
    if (!pVcrResult || 0 == pVcrResult->dwCarID) return false;

    CVLPImage_SaveData imageData;
    memset(&imageData, 0, sizeof imageData);
    qsnprintf(imageData.szId, sizeof imageData.szId, "%s", sId.toLocal8Bit().constData());

    QString VLPPath = GetCurrentPath() + QString("VehVideo/");
    CreatePath(VLPPath);
    QString sTime = QDateTime::currentDateTime().toString("MMddhhmmss");
    DebugLog(QString("save vipu,video fileName:%1").arg(pVcrResult->sVideoFileName));
    if (pVcrResult->sVideoFileName.length() > 0) {
        QString sFileName =
                VLPPath + QString("V_") + GetFileNameFromFullName(pVcrResult->sVideoFileName);

        DebugLog(QString("NewVideofile:%1,oldVideoFile:%2")
                 .arg(sFileName)
                 .arg(pVcrResult->sVideoFileName));

        if (CopyFile_Replace(pVcrResult->sVideoFileName, sFileName)) {
            qsnprintf(imageData.szVideoImageFileName, sizeof imageData.szVideoImageFileName, "%s",
                      sFileName.toLocal8Bit().data());
            qsnprintf(imageData.szVideoImgSuffix, sizeof imageData.szVideoImgSuffix, "avi");
        } else {
            DebugLog(QString("save vipu,copy video file %1 to %2 failed")
                     .arg(pVcrResult->sVideoFileName)
                     .arg(sFileName));
        }
    }
    QString sTailFileName = pVcrResult->bigImage[VehImg_Tail].sImgFile;
    DebugLog(QString("save vipu tail file:%1").arg(sTailFileName));
    if (sTailFileName.length() > 0) {
        QString sFileName = VLPPath + QString("T_") + GetFileNameFromFullName(sTailFileName);

        DebugLog(QString("New taile fileName:%1").arg(sFileName));
        if (CopyFile_Replace(sTailFileName, sFileName)) {
            qsnprintf(imageData.sztailImageFileName, sizeof imageData.sztailImageFileName, "%s",
                      sFileName.toLocal8Bit().data());
        } else {
            DebugLog(QString("copy tail file %1 to %2失败").arg(sTailFileName).arg(sFileName));
        }
    }
    QString sBodyFileName = pVcrResult->bigImage[VehImg_Body].sImgFile;
    DebugLog(QString("save vipu,body file:%1").arg(sBodyFileName));
    if (sBodyFileName.length() > 0) {
        QString sFileName = VLPPath + QString("B_") + GetFileNameFromFullName(sBodyFileName);
        DebugLog(QString("save vipy,new bodyfile:%1").arg(sFileName));
        if (CopyFile_Replace(sBodyFileName, sFileName)) {
            qsnprintf(imageData.szBodyImageFileName, sizeof imageData.szBodyImageFileName, "%s",
                      sFileName.toLocal8Bit().data());
        } else {
            DebugLog(QString("拷贝车身文件%1 to%2失败").arg(sBodyFileName).arg(sFileName));
        }
    }
    QString sHeadFileName = pVcrResult->bigImage[VehImg_Front].sImgFile;
    DebugLog(QString("save vipu,head file:%1").arg(sHeadFileName));
    if (sHeadFileName.length() > 0) {
        QString sFileName = VLPPath + QString("H_") + GetFileNameFromFullName(sHeadFileName);
        DebugLog(QString("save vipu,new headfile:%1").arg(sFileName));
        if (CopyFile_Replace(sHeadFileName, sFileName)) {
            qsnprintf(imageData.szImageFileName, sizeof imageData.szImageFileName, "%s",
                      sFileName.toLocal8Bit().data());
        } else {
            DebugLog(QString("拷贝车头文件%1 to%2失败").arg(sHeadFileName).arg(sFileName));
        }
    }

    QDataToSave vipuToSave;
    CHttpMsgManager::PackBinMsgToSave(vipuToSave, (quint8 *)&imageData, sizeof imageData, "VIPU");
    QString sTmpFileName;
    bool bRlt = m_pDataMgr->SaveData(vipuToSave, sTmpFileName, 0, 0);
    if (bRlt) {
        DebugLog(QString("save vipu,%1").arg(pVcrResult->sPlate));
    }
    return bRlt;
}

bool CETCLaneCtrl::bReETCAutoCap(int nIndex, const QString &sAutoVehPlate)
{
    if (sAutoVehPlate.isEmpty() || sAutoVehPlate.contains(QString("无车牌"))) return true;
    for (int i = 0; i < 2; ++i) {
        if (sAutoVehPlate == m_ETCAutoRegInfos[i].sAutoVehPlate) {
            DebugLog(QString("重复车牌识别结果,识别结果:[%1][%2]上一结果:[%3][%4]")
                     .arg(nIndex)
                     .arg(sAutoVehPlate)
                     .arg(i)
                     .arg(m_ETCAutoRegInfos[i].sAutoVehPlate));
            return true;
        }
    }
    return false;
}

void CETCLaneCtrl::ClearAutoRegInfo(int nIndex)
{
    if (nIndex > 1) nIndex = 1;
    QMutexLocker locker(&m_ETCAutoRegMt);
    DebugLog(QString("清除车牌识别%1的识别结果:%2")
             .arg(nIndex)
             .arg(m_ETCAutoRegInfos[nIndex].sAutoVehPlate));
    m_ETCAutoRegInfos[nIndex].ClearResult();
    return;
}

bool CETCLaneCtrl::GetETCAutoRegInfo(int nIndex, CAutoRegInfo &AutoRegInfo, bool bClearRlt,
                                     bool bCheckTime)
{
    /*
#ifdef QT_DEBUG
    static int i = 1;
    AutoRegInfo.sAutoVehPlate = QString("赣A1234%1").arg(i++);
    AutoRegInfo.AuRegTime = QDateTime::currentDateTime();
    QString sBatch, sNo, sLaneHex;
    sLaneHex = Ptr_Info->GetHexLaneID();
    AutoRegInfo.id =
        Ptr_Info->GetGBLaneId() +
        CBatchMgr::GetBatchMgr()->GetBatchInfo(sBatch, sNo, sLaneHex, CBatchMgr::SnType_VLP,
false); return true; #endif*/
    if (nIndex > 1) nIndex = 1;
    QMutexLocker locker(&m_ETCAutoRegMt);
    if (m_ETCAutoRegInfos[nIndex].IsValid(bCheckTime)) {
        AutoRegInfo = m_ETCAutoRegInfos[nIndex];
        if (bClearRlt) {
            DebugLog(QString("清除车牌识别%1的识别结果:%2")
                     .arg(nIndex)
                     .arg(m_ETCAutoRegInfos[nIndex].sAutoVehPlate));
            m_ETCAutoRegInfos[nIndex].ClearResult();
        }
        return true;
    }
    return false;
}

bool CETCLaneCtrl::GetETCAutoRegInfoByPlate(const QString &sPlate, CAutoRegInfo &autoRegInfo)
{
    if (sPlate.isEmpty()) return false;

    for (int i = 0; i < 2; ++i) {
        if (sPlate == m_ETCAutoRegInfos[i].sAutoVehPlate) {
            autoRegInfo = m_ETCAutoRegInfos[i];
            return true;
        }
    }
    return false;
}

void CETCLaneCtrl::ChangeToUnLoginState()
{
    if (m_pUnLoginState) {
        CAbstractState::ChangeToNextState(m_pUnLoginState);
        Ptr_RemoteCtrl->Logout();
    }
}

void CETCLaneCtrl::ChangeToWaitVehPassState()
{
    if (m_pWaitVehPassState) {
        CAbstractState::ChangeToNextState(m_pWaitVehPassState);
    }
}

void CETCLaneCtrl::ChangeToMotorcade()
{
    if (m_pMotorCadeState) {
        CAbstractState::ChangeToNextState(m_pMotorCadeState);
    }
}

bool CETCLaneCtrl::SaveVehCapPic(QString &sRelativeFileName)
{
    QDateTime CapTime = QDateTime::currentDateTime();
    DebugLog("格式化文件名");
    QString sRelativePath, sFileName;
    FormatPicFileName(CapTime, 0, sRelativePath, sFileName);
    QString sLastFilePath = QString("%1%2").arg(Ptr_Info->GetPicPath()).arg(sRelativePath);
    DebugLog("创建目录");
    if (!CreatePath(sLastFilePath)) {
        ErrorLog(QString("创建图片目录%1失败").arg(sLastFilePath));
        return false;
    }

    sRelativeFileName = QString(".\\pic%1%2").arg(sRelativePath).arg(sFileName);
    QString sFullFileName = QString("%1%2").arg(sLastFilePath).arg(sFileName);
    CVideoCard *pVideoCard = CDeviceFactory::GetVideoCard();
    DebugLog("开始保存/抓拍");
    if (!pVideoCard->GetCapFileToLast(sFullFileName)) {
        ErrorLog(QString("保存车辆抓拍图片%1失败").arg(sFullFileName));
        return false;
    }
    sRelativeFileName = sFullFileName;
    return true;
}

QString CETCLaneCtrl::GetVehicleTypeDbInfo(CTransInfo *pTransInfo){
    // 从车型库获取vehicleTypeDbInfo信息
    CVehInfo_VehLib vehLibInfo;
    QString sVehPlate = QString::fromLocal8Bit(pTransInfo->VehInfo.szVehPlate).trimmed();
    int nVlpColor = pTransInfo->VehInfo.nVehPlateColor;
    bool bVehLib = CVehTypeLibMgr::GetVehTypeLibMgr()->QueryVehFromCache(sVehPlate, nVlpColor, vehLibInfo);
    if (bVehLib && !vehLibInfo.vehicleTypeDbInfo.isEmpty()) {
        // 如果成功获取车型库信息，将其赋值给addInfo.vehicleTypeDbInfo
        DebugLog(QString("填充车型库信息vehicleTypeDbInfo: %1").arg(vehLibInfo.vehicleTypeDbInfo));
        return vehLibInfo.vehicleTypeDbInfo;    }
    else {
        // 如果没有获取到车型库信息，使用默认格式填充
        // 格式：车型|车种|轴数|轴型，没有的项用空格代替
        int vehType = 0;
        int vehClass = 0;
        int axleNum = 0;
        QString axleType = " "; // 轴型默认为空格
        QString vehicleTypeDbInfo = QString("%1|%2|%3|%4")
                .arg(vehType > 0 ? QString::number(vehType) : " ")
                .arg(vehClass > 0 ? QString::number(vehClass) : " ")
                .arg(axleNum > 0 ? QString::number(axleNum) : " ")
                .arg(axleType);
        DebugLog(QString("未从车型库获取到信息，使用默认vehicleTypeDbInfo: %1").arg(vehicleTypeDbInfo));
        return vehicleTypeDbInfo;    }}
// 根据车种、车型、轴数计算结果车型
int CETCLaneCtrl::CalculateVehicleType(CTransInfo *pTransInfo)
{
    if (!pTransInfo) return 0;
    // 获取初始车型、车种和轴数
    int vehClass = pTransInfo->VehInfo.VehClass; // 车型
    int gbVehType = pTransInfo->VehInfo.GBVehType; // 车种
    int axleNum = pTransInfo->GetVehAxisNum(); // 轴数
    int calVehicleType = vehClass;
    // 默认使用原始车型
    // 当车种不等于UVT_Normal且车型大于10时，通过轴数反推结果车型
    if (gbVehType != UVT_Normal && vehClass > 10 && vehClass <20) {
        // 根据轴数反推车型的逻辑
        // 这里需要根据实际业务规则实现具体的反推逻辑
        switch(axleNum) {
        case 2:
            //calVehicleType = 1; // 示例：2轴对应车型1
            break;
        case 3:
            calVehicleType = 13; // 示例：3轴对应车型2
            break;
        case 4:
            calVehicleType = 14; // 示例：4轴对应车型3
            break;
        case 5:
            calVehicleType = 15; // 示例：5轴对应车型4
            break;
        case 6:
            calVehicleType = 16; // 示例：6轴对应车型5
            break;
        default:
            if (axleNum > 6) {
                calVehicleType = 6; // 示例：大于6轴的对应车型6
            } else {
                calVehicleType = vehClass; // 默认使用原始车型
            }
            break;
        }
        DebugLog(QString("车种非标准，根据轴数%1反推车型为%2").arg(axleNum).arg(calVehicleType));
    } else {
        // 使用原始车型作为结果
        DebugLog(QString("使用原始车型%1作为结果车型").arg(vehClass));
    }
    return calVehicleType;
}

void CETCLaneCtrl::StopVehInputCardMgrTimer()
{
    m_pVehInputState->StopCardMgrTimer(1);

}
void CETCLaneCtrl::StartVehInputCardMgrTimer()
{
    m_pVehInputState->StartCardMgrTimer();
}



// 根据轴数获取限重
quint32 CETCLaneCtrl::GetWeightLimitByAxisNum(int nAxisNum, bool bZhuangXiang)
{
    // 根据轴数生成简单的轴组类型（假设都是单轴）
    quint32 dwAxisGroup = 0;
    for (int i = 0; i < nAxisNum; i++) {
        dwAxisGroup = dwAxisGroup * 10 + 1; // 假设都是单轴(1)
    }

    if (bZhuangXiang) {
        // 专项作业车限重标准
        return GetWeightLimitForSpecial(dwAxisGroup);
    } else {
        // 普通货车限重标准
        return GetWeightLimit(dwAxisGroup);
    }
}

bool CETCLaneCtrl::TryGetLatestWeightData(CTransInfo* pTransInfo, CWeightInfo_Lane& WeightInfo)
{
    if (!pTransInfo) {
        return false;
    }

    // 获取VehWeightInfo实例
    VehWeightInfo* pVehWeightInfo = VehWeightInfo::GetVehWeightInfo();
    if (!pVehWeightInfo) {
        DebugLog("获取VehWeightInfo实例失败");
        return false;
    }

    // 检查是否有称重数据
    if (pVehWeightInfo->GetVehCount() <= 0) {
        DebugLog("当前没有称重数据");
        return false;
    }

    // 获取首辆车的称重信息
    CVehAxisInfo vehAxisInfo;
    quint32 dwTotalWeight = 0;
    quint32 dwLimitWeight = 0;
    qint32 nOverRate = 0;

    bool bZhuangXiang = isZhuangXiangTruck(pTransInfo->VehInfo.VehClass);
    if (!pVehWeightInfo->GetFirstVehWeight(bZhuangXiang, vehAxisInfo, dwTotalWeight, dwLimitWeight, nOverRate)) {
        DebugLog("获取首辆车称重信息失败");
        return false;
    }

    // 检查称重数据是否有效
    if (dwTotalWeight == 0 || dwTotalWeight >= 0x00ffffff) {
        DebugLog(QString("称重数据无效：总重=%1").arg(dwTotalWeight));
        return false;
    }

    // 简单的匹配检查：车型是否匹配
    int nAxisNum = vehAxisInfo.GetConfirmedSingleAxisNum();
    int nExpectedAxisNum = GetVehAxisNumByVC(pTransInfo->m_vcrResult.vehclass);

    // 轴数差异不超过1轴认为匹配
    if (abs(nAxisNum - nExpectedAxisNum) > 1) {
        DebugLog(QString("轴数不匹配：称重轴数[%1]，期望轴数[%2]").arg(nAxisNum).arg(nExpectedAxisNum));
        return false;
    }

    // 填充称重信息
    WeightInfo.Weight = dwTotalWeight;
    WeightInfo.limitWeight = dwLimitWeight;
    WeightInfo.overWeightRate = nOverRate;
    WeightInfo.axleCount = nAxisNum;

    // 获取轴组信息
    QString sAxisInfo;
    vehAxisInfo.GetConfirmedAxisInfo(sAxisInfo);
    WeightInfo.axisInfo = sAxisInfo;

    DebugLog(QString("成功获取最新称重数据：车牌[%1]，总重[%2]kg，轴数[%3]，轴组[%4]")
             .arg(GB2312toUnicode(pTransInfo->VehInfo.szVehPlate))
             .arg(dwTotalWeight)
             .arg(nAxisNum)
             .arg(sAxisInfo));

    // 【新增】成功获取称重数据后，删除该称重信息，避免重复使用
    // CVehAxisInfo removedVehAxisInfo;
    // if (pVehWeightInfo->RemoveFirst(&removedVehAxisInfo)) {
    //     DebugLog(QString("删除已使用的称重数据，总重: %1kg")
    //              .arg(removedVehAxisInfo.GetConfirmedTotalRawWeight()));
    // } else {
    //     DebugLog("删除称重数据失败");
    // }

    return true;
}
